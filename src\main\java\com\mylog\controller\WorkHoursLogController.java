package com.mylog.controller;

import com.mylog.model.ProjectTask;
import com.mylog.model.WorkHoursLog;
import com.mylog.service.ProjectService;
import com.mylog.service.TaskService;
import com.mylog.service.WorkHoursLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 工期登记控制器
 * 提供工期登记表的Web界面和REST API接口
 */
@Controller
@RequestMapping("/work-hours")
public class WorkHoursLogController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WorkHoursLogController.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private WorkHoursLogService workHoursLogService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ProjectService projectService;

    /**
     * 工期登记主页面
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String listWorkHours(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "businessId", required = false) Integer businessId,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Model model) {

        logger.info("加载工期登记列表页面");

        try {
            // 创建分页请求，按创建时间降序排序
            Pageable pageable = PageRequest.of(page, Math.min(size, 100),
                    Sort.by(Sort.Direction.DESC, "createdTime"));

            Page<WorkHoursLog> workHoursPage;

            // 根据查询条件获取数据
            if (businessType != null && !businessType.trim().isEmpty()) {
                if (businessId != null) {
                    // 按业务类型和业务ID查询
                    workHoursPage = workHoursLogService.findWorkHoursLogsByBusinessTypeAndBusinessId(businessType,
                            businessId, pageable);
                } else {
                    // 只按业务类型查询
                    workHoursPage = workHoursLogService.findWorkHoursLogsByBusinessType(businessType, pageable);
                }
            } else if (startDate != null && endDate != null) {
                // 按时间范围查询
                String startDateStr = startDate.atStartOfDay().format(DATE_TIME_FORMATTER);
                String endDateStr = endDate.atTime(23, 59, 59).format(DATE_TIME_FORMATTER);
                workHoursPage = workHoursLogService.findWorkHoursLogsByTimeRange(startDateStr, endDateStr, pageable);
            } else {
                // 获取所有记录
                workHoursPage = workHoursLogService.findAllWorkHoursLogs(pageable);
            }

            // 添加数据到模型
            model.addAttribute("workHoursPage", workHoursPage);
            model.addAttribute("currentPage", workHoursPage.getNumber());
            model.addAttribute("totalPages", workHoursPage.getTotalPages());
            model.addAttribute("totalElements", workHoursPage.getTotalElements());
            model.addAttribute("size", size);
            model.addAttribute("businessType", businessType);
            model.addAttribute("businessId", businessId);
            model.addAttribute("startDate", startDate);
            model.addAttribute("endDate", endDate);
            model.addAttribute("activeMenu", "work-hours");

            logger.info("工期登记列表加载成功，共{}条记录", workHoursPage.getTotalElements());

        } catch (Exception e) {
            logger.error("加载工期登记列表失败: {}", e.getMessage(), e);
            model.addAttribute("error", "加载工期登记列表失败：" + e.getMessage());
        }

        return "work-hours/list";
    }

    /**
     * 新建工期登记页面
     */
    @GetMapping("/new")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String newWorkHoursForm(Model model) {
        logger.info("显示新建工期登记表单");

        model.addAttribute("workHoursLog", new WorkHoursLog());
        model.addAttribute("activeMenu", "work-hours");

        return "work-hours/form";
    }

    /**
     * 编辑工期登记页面
     */
    @GetMapping("/{id}/edit")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String editWorkHoursForm(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        logger.info("显示编辑工期登记表单，ID: {}", id);

        try {
            Optional<WorkHoursLog> workHoursLogOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!workHoursLogOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "工期登记记录不存在");
                return "redirect:/work-hours";
            }

            model.addAttribute("workHoursLog", workHoursLogOpt.get());
            model.addAttribute("activeMenu", "work-hours");

        } catch (Exception e) {
            logger.error("加载工期登记记录失败: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "加载工期登记记录失败：" + e.getMessage());
            return "redirect:/work-hours";
        }

        return "work-hours/form";
    }

    /**
     * 保存工期登记
     */
    @PostMapping("/save")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String saveWorkHours(@ModelAttribute WorkHoursLog workHoursLog,
            RedirectAttributes redirectAttributes) {
        logger.info("保存工期登记: {}", workHoursLog);

        try {
            // 设置创建时间
            if (workHoursLog.getId() == null) {
                workHoursLog.setCreatedTime(LocalDateTime.now().format(DATE_TIME_FORMATTER));
            }

            WorkHoursLog savedLog = workHoursLogService.saveWorkHoursLog(workHoursLog);

            String message = workHoursLog.getId() == null ? "工期登记创建成功" : "工期登记更新成功";
            redirectAttributes.addFlashAttribute("message", message);

            logger.info("工期登记保存成功: ID={}", savedLog.getId());

        } catch (Exception e) {
            logger.error("保存工期登记失败: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "保存工期登记失败：" + e.getMessage());
        }

        return "redirect:/work-hours";
    }

    /**
     * 删除工期登记
     */
    @PostMapping("/delete")
    @PreAuthorize("hasRole('ADMIN')")
    public String deleteWorkHours(@RequestParam("id") Long id, RedirectAttributes redirectAttributes) {
        logger.info("删除工期登记: ID={}", id);

        try {
            Optional<WorkHoursLog> workHoursLogOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!workHoursLogOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "工期登记记录不存在");
                return "redirect:/work-hours";
            }

            workHoursLogService.deleteWorkHoursLog(id);
            redirectAttributes.addFlashAttribute("message", "工期登记删除成功");

            logger.info("工期登记删除成功: ID={}", id);

        } catch (Exception e) {
            logger.error("删除工期登记失败: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除工期登记失败：" + e.getMessage());
        }

        return "redirect:/work-hours";
    }

    /**
     * 删除工期登记 - 支持路径参数
     */
    @PostMapping("/delete/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String deleteWorkHoursById(@PathVariable("id") Long id, RedirectAttributes redirectAttributes) {
        logger.info("删除工期登记: ID={}", id);

        try {
            Optional<WorkHoursLog> workHoursLogOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!workHoursLogOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "工期登记记录不存在");
                return "redirect:/work-hours";
            }

            workHoursLogService.deleteWorkHoursLog(id);
            redirectAttributes.addFlashAttribute("message", "工期登记删除成功");

            logger.info("工期登记删除成功: ID={}", id);

        } catch (Exception e) {
            logger.error("删除工期登记失败: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除工期登记失败：" + e.getMessage());
        }

        return "redirect:/work-hours";
    }

    /**
     * 工期登记详情页面 - 增强版本，包含统计和相关记录
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String viewWorkHours(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        logger.info("查看工期登记详情: ID={}", id);

        try {
            Optional<WorkHoursLog> workHoursLogOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!workHoursLogOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "工期登记记录不存在");
                return "redirect:/work-hours";
            }

            WorkHoursLog workHours = workHoursLogOpt.get();
            model.addAttribute("workHours", workHours);

            // 获取该业务的统计信息
            try {
                Double totalHours = workHoursLogService.getLatestHoursInventory(workHours.getBusinessType(),
                        workHours.getBusinessId());
                List<WorkHoursLog> allRecords = workHoursLogService.findWorkHoursLogsByBusinessTypeAndBusinessId(
                        workHours.getBusinessType(), workHours.getBusinessId());

                Map<String, Object> statistics = new HashMap<>();
                statistics.put("totalHours", totalHours != null ? totalHours : 0.0);
                statistics.put("recordCount", allRecords.size());
                statistics.put("averageHours",
                        allRecords.size() > 0 && totalHours != null ? totalHours / allRecords.size() : 0.0);

                model.addAttribute("statistics", statistics);

                // 获取相关记录（最新5条，排除当前记录）
                List<WorkHoursLog> relatedRecords = allRecords.stream()
                        .filter(log -> !log.getId().equals(id))
                        .limit(5)
                        .collect(java.util.stream.Collectors.toList());

                model.addAttribute("relatedRecords", relatedRecords);
                model.addAttribute("totalRelatedCount", allRecords.size() - 1);

            } catch (Exception e) {
                logger.warn("获取统计信息失败: {}", e.getMessage());
            }

            model.addAttribute("activeMenu", "work-hours");

        } catch (Exception e) {
            logger.error("加载工期登记详情失败: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "加载工期登记详情失败：" + e.getMessage());
            return "redirect:/work-hours";
        }

        return "work-hours/view";
    }

    /**
     * API: 获取所有工期登记记录
     */
    @GetMapping("/api/all")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> getAllWorkHours(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size) {

        Map<String, Object> response = new HashMap<>();

        try {
            Pageable pageable = PageRequest.of(page, Math.min(size, 100),
                    Sort.by(Sort.Direction.DESC, "createdTime"));
            Page<WorkHoursLog> workHoursPage = workHoursLogService.findAllWorkHoursLogs(pageable);

            response.put("success", true);
            response.put("data", workHoursPage.getContent());
            response.put("page", workHoursPage.getNumber());
            response.put("size", workHoursPage.getSize());
            response.put("totalElements", workHoursPage.getTotalElements());
            response.put("totalPages", workHoursPage.getTotalPages());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取工期登记列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取工期登记列表失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 根据业务类型查询工期登记
     */
    @GetMapping("/api/business-type/{businessType}")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> getWorkHoursByBusinessType(
            @PathVariable("businessType") String businessType,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size) {

        Map<String, Object> response = new HashMap<>();

        try {
            Pageable pageable = PageRequest.of(page, Math.min(size, 100),
                    Sort.by(Sort.Direction.DESC, "createdTime"));
            Page<WorkHoursLog> workHoursPage = workHoursLogService.findWorkHoursLogsByBusinessType(businessType,
                    pageable);

            response.put("success", true);
            response.put("data", workHoursPage.getContent());
            response.put("page", workHoursPage.getNumber());
            response.put("size", workHoursPage.getSize());
            response.put("totalElements", workHoursPage.getTotalElements());
            response.put("totalPages", workHoursPage.getTotalPages());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("根据业务类型查询工期登记失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 根据业务类型和业务ID查询工期登记
     */
    @GetMapping("/api/business/{businessType}/{businessId}")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> getWorkHoursByBusiness(
            @PathVariable("businessType") String businessType,
            @PathVariable("businessId") Integer businessId,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size) {

        Map<String, Object> response = new HashMap<>();

        try {
            Pageable pageable = PageRequest.of(page, Math.min(size, 100),
                    Sort.by(Sort.Direction.DESC, "createdTime"));
            Page<WorkHoursLog> workHoursPage = workHoursLogService.findWorkHoursLogsByBusinessTypeAndBusinessId(
                    businessType, businessId, pageable);

            response.put("success", true);
            response.put("data", workHoursPage.getContent());
            response.put("page", workHoursPage.getNumber());
            response.put("size", workHoursPage.getSize());
            response.put("totalElements", workHoursPage.getTotalElements());
            response.put("totalPages", workHoursPage.getTotalPages());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("根据业务查询工期登记失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 创建工期登记
     */
    @PostMapping("/api/create")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> createWorkHours(@RequestBody WorkHoursLog workHoursLog) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 设置创建时间
            workHoursLog.setCreatedTime(LocalDateTime.now().format(DATE_TIME_FORMATTER));

            WorkHoursLog savedLog = workHoursLogService.saveWorkHoursLog(workHoursLog);

            response.put("success", true);
            response.put("message", "工期登记创建成功");
            response.put("data", savedLog);

            logger.info("API创建工期登记成功: ID={}", savedLog.getId());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("API创建工期登记失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "创建失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 添加工期（自动计算存量）
     */
    @PostMapping("/api/add-hours")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> addWorkHours(
            @RequestParam("businessType") String businessType,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("hoursChange") Double hoursChange,
            @RequestParam("reason") String reason,
            @RequestParam(value = "creator", required = false) String creator,
            @RequestParam(value = "remark", required = false) String remark,
            @RequestParam(value = "bonus", required = false) Double bonus) {

        Map<String, Object> response = new HashMap<>();
        try {
            WorkHoursLog savedLog;
            // 使用默认的额定天数0.0来满足数据库约束
            Double daysRated = 0.0;

            // 使用包含所有字段的方法
            savedLog = workHoursLogService.addWorkHours(businessType, businessId, hoursChange, daysRated, reason,
                    creator, remark, bonus, creator); // 使用creator作为responsiblePerson的默认值

            response.put("success", true);
            response.put("message", "工期添加成功");
            response.put("data", savedLog);

            logger.info("API添加工期成功: 业务类型={}, 业务ID={}, 工期变化={}", businessType, businessId, hoursChange);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("API添加工期失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "添加工期失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 获取工期统计信息
     */
    @GetMapping("/api/statistics/{businessType}/{businessId}")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> getWorkHoursStatistics(
            @PathVariable("businessType") String businessType,
            @PathVariable("businessId") Integer businessId) {

        Map<String, Object> response = new HashMap<>();

        try {
            Double currentInventory = workHoursLogService.getLatestHoursInventory(businessType, businessId);
            Double totalHoursChange = workHoursLogService.calculateTotalHoursChange(businessType, businessId);
            List<WorkHoursLog> recentLogs = workHoursLogService
                    .findWorkHoursLogsByBusinessTypeAndBusinessId(businessType, businessId);

            // 只取最近10条记录
            if (recentLogs.size() > 10) {
                recentLogs = recentLogs.subList(0, 10);
            }

            response.put("success", true);
            response.put("currentInventory", currentInventory);
            response.put("totalHoursChange", totalHoursChange);
            response.put("recentLogs", recentLogs);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取工期统计信息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取统计信息失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 更新工期登记
     */
    @PutMapping("/api/{id}")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> updateWorkHours(
            @PathVariable("id") Long id,
            @RequestBody WorkHoursLog workHoursLog) {

        Map<String, Object> response = new HashMap<>();

        try {
            Optional<WorkHoursLog> existingOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!existingOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "工期登记记录不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            workHoursLog.setId(id);
            WorkHoursLog savedLog = workHoursLogService.saveWorkHoursLog(workHoursLog);

            response.put("success", true);
            response.put("message", "工期登记更新成功");
            response.put("data", savedLog);

            logger.info("API更新工期登记成功: ID={}", id);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("API更新工期登记失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "更新失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 删除工期登记
     */
    @DeleteMapping("/api/{id}")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteWorkHoursApi(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Optional<WorkHoursLog> workHoursLogOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!workHoursLogOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "工期登记记录不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            workHoursLogService.deleteWorkHoursLog(id);

            response.put("success", true);
            response.put("message", "工期登记删除成功");

            logger.info("API删除工期登记成功: ID={}", id);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("API删除工期登记失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "删除失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 获取单个工期登记详情
     */
    @GetMapping("/api/{id}")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> getWorkHoursById(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Optional<WorkHoursLog> workHoursLogOpt = workHoursLogService.findWorkHoursLogById(id);
            if (!workHoursLogOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "工期登记记录不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            response.put("success", true);
            response.put("data", workHoursLogOpt.get());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取工期登记详情失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取详情失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 预览工期变化结果
     */
    @GetMapping("/api/preview")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Map<String, Object>> previewWorkHours(
            @RequestParam("businessType") String businessType,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("hoursChange") Double hoursChange) {

        Map<String, Object> response = new HashMap<>();

        try {
            Double currentInventory = workHoursLogService.getLatestHoursInventory(businessType, businessId);
            if (currentInventory == null) {
                currentInventory = 0.0;
            }

            Double predictedInventory = currentInventory + hoursChange;

            response.put("success", true);
            response.put("currentInventory", currentInventory);
            response.put("predictedInventory", predictedInventory);
            response.put("hoursChange", hoursChange);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("预览工期变化失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "预览失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 修补工期记录
     * 查找所有评级工期大于0的任务，并为没有工期记录的任务添加记录
     */
    @PostMapping("/repair")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> repairWorkHoursRecords() {

        Map<String, Object> response = new HashMap<>();

        try {
            logger.info("开始执行工期记录修补操作");

            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication != null ? authentication.getName() : "system";

            // 获取所有任务
            List<ProjectTask> allTasks = taskService.findAllTasksNoPage();
            logger.info("获取到所有任务数量: {}", allTasks.size());
            // 筛选出额定工期和工期都大于0的任务
            List<ProjectTask> tasksWithRatedDuration = allTasks.stream()
                    .filter(task -> task.getRatedDurationDays() != null &&
                            task.getRatedDurationDays().compareTo(BigDecimal.ZERO) > 0 &&
                            task.getDurationDays() != null &&
                            task.getDurationDays().compareTo(BigDecimal.ZERO) > 0)
                    .collect(java.util.stream.Collectors.toList());

            logger.info("找到额定工期和工期都大于0的任务数量: {}", tasksWithRatedDuration.size());

            int existingRecords = 0;
            int createdRecords = 0;            // 检查每个任务是否已有工期记录
            for (ProjectTask task : tasksWithRatedDuration) {
                String businessType = "任务";
                Integer businessId = task.getTaskId().intValue();

                // 检查是否已存在工期记录和累计工期数值
                Long recordCount = workHoursLogService.countWorkHoursLogsByBusinessTypeAndBusinessId(businessType, businessId);
                Double currentInventory = workHoursLogService.getLatestHoursInventory(businessType, businessId);
                
                // 如果存在记录且累计工期大于0，则跳过
                if (recordCount > 0 && currentInventory != null && currentInventory > 0) {
                    existingRecords++;
                    logger.debug("任务 {} 已存在工期记录且累计工期大于0 ({}天)，跳过", task.getTaskId(), currentInventory);
                    continue;
                }

                // 需要修补：不存在记录或累计工期为0或null
                try {
                    Double ratedDuration = task.getRatedDurationDays().doubleValue();
                    String reason, remark;
                    Double hoursChange=task.getDurationDays().doubleValue();
                    String responsiblePerson = task.getResponsible() != null ? task.getResponsible() : username;
                    
                    if (recordCount == 0) {
                        // 不存在记录，创建初始记录
                        reason = "无工期记录，系统自动修补 - 工期记录";
                        remark = String.format("任务[%s]无工期记录（自动修补添加）",
                                task.getTaskName() != null ? task.getTaskName() : "未命名任务");
                       
                        logger.info("为任务 {} [{}] 添加工期记录，评级工期: {} 天",
                                task.getTaskId(), task.getTaskName(), ratedDuration);
                    } else {
                        // 存在记录但累计工期为0或null，更新记录
                        reason = "累计工期为0，系统自动修补 - 工期记录";
                        remark = String.format("任务[%s]累计工期为0，更新为工期（自动修补）",
                                task.getTaskName() != null ? task.getTaskName() : "未命名任务");
                        
                        logger.info("为任务 {} [{}] 更新工期记录，累计工期: {} -> {}，评级工期: {} 天",
                                task.getTaskId(), task.getTaskName(), currentInventory, ratedDuration, ratedDuration);
                    }

                    // 添加工期记录
                    workHoursLogService.addWorkHours(businessType, businessId, hoursChange, ratedDuration, reason, username, remark, responsiblePerson);
                    createdRecords++;
                    
                } catch (Exception e) {
                    logger.error("为任务 {} 修补工期记录失败: {}", task.getTaskId(), e.getMessage(), e);
                }
            }

            logger.info("工期记录修补完成 - 总任务数: {}, 已有记录: {}, 新增记录: {}",
                    tasksWithRatedDuration.size(), existingRecords, createdRecords);

            response.put("success", true);
            response.put("totalTasks", tasksWithRatedDuration.size());
            response.put("existingRecords", existingRecords);
            response.put("createdRecords", createdRecords);
            response.put("message", String.format("修补完成！处理任务 %d 个，已有记录 %d 个，新增记录 %d 个",
                    tasksWithRatedDuration.size(), existingRecords, createdRecords));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("修补工期记录时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "修补失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * API: 修复所有责任人数据
     * 查找所有工期记录，根据业务类型和业务ID找到对应的项目或任务责任人，然后更新工期记录中的责任人字段
     */
    @PostMapping("/fix-responsible-person")
    @ResponseBody
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> fixResponsiblePersonData() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("开始修复所有责任人数据");
            
            // 获取所有工期记录
            List<WorkHoursLog> allLogs = workHoursLogService.findAllWorkHoursLogs();
            
            int totalRecords = allLogs.size();
            int fixedRecords = 0;
            int failedRecords = 0;
            
            for (WorkHoursLog workHoursLog : allLogs) {
                try {
                    String businessType = workHoursLog.getBusinessType();
                    Integer businessId = workHoursLog.getBusinessId();
                    String responsiblePerson = null;
                    
                    // 根据业务类型查找责任人
                    if ("项目".equals(businessType)) {
                        // 查找项目责任人
                        try {
                            var project = projectService.findProjectById(businessId.longValue());
                            if (project.isPresent() && project.get().getResponsible() != null) {
                                responsiblePerson = project.get().getResponsible();
                            }
                        } catch (Exception e) {
                            logger.warn("查找项目ID {} 的责任人失败: {}", businessId, e.getMessage());
                        }
                    } else if ("任务".equals(businessType)) {
                        // 查找任务责任人
                        try {
                            var task = taskService.findTaskById(businessId.longValue());
                            if (task.isPresent() && task.get().getResponsible() != null) {
                                responsiblePerson = task.get().getResponsible();
                            }
                        } catch (Exception e) {
                            logger.warn("查找任务ID {} 的责任人失败: {}", businessId, e.getMessage());
                        }
                    }
                    
                    // 如果找到责任人且与当前记录的责任人不同，则更新
                    if (responsiblePerson != null && !responsiblePerson.equals(workHoursLog.getResponsiblePerson())) {
                        workHoursLog.setResponsiblePerson(responsiblePerson);
                        workHoursLogService.saveWorkHoursLog(workHoursLog);
                        fixedRecords++;
                        logger.debug("更新工期记录ID {} 的责任人为: {}", workHoursLog.getId(), responsiblePerson);
                    }
                    
                } catch (Exception e) {
                    failedRecords++;
                    logger.error("修复工期记录ID {} 的责任人数据失败: {}", workHoursLog.getId(), e.getMessage(), e);
                }
            }
            
            logger.info("修复责任人数据完成：总记录数={}, 成功修复数={}, 失败记录数={}", 
                    totalRecords, fixedRecords, failedRecords);

            response.put("success", true);
            response.put("totalRecords", totalRecords);
            response.put("fixedRecords", fixedRecords);
            response.put("failedRecords", failedRecords);
            response.put("message", String.format("修复完成！总记录数: %d，成功修复: %d，失败: %d", 
                    totalRecords, fixedRecords, failedRecords));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("修复责任人数据时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "修复失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
