<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head th:replace="~{fragments/layout :: head('违规登记任务报表')}">
    <meta charset="UTF-8">
    <title>违规登记任务报表</title>    <style>
        .task-row:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }
        
        .task-id-link {
            font-weight: 500;
            color: #0d6efd;
        }
        
        .task-id-link:hover {
            color: #0a58ca;
            text-decoration: underline !important;
        }
    </style>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <!-- 移动端导航切换按钮 -->
        <button class="btn btn-link d-md-none mt-3 mb-2 p-0 fs-4" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>

        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">违规登记任务报表</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary"
                        onclick="exportToPDF()">导出PDF</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary"
                        onclick="exportToExcel()">导出Excel</button>
                </div>                <div class="form-check form-switch me-2 d-flex align-items-center">
                    <input class="form-check-input" type="checkbox" id="sendWeixinMessage">
                    <label class="form-check-label ms-1 small" for="sendWeixinMessage">发送微信通知</label>
                </div>
                <a th:href="@{/reports}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
            </div>
        </div>

        <!-- 加载完成提示 -->
        <div id="loadCompleteAlert" class="alert alert-success alert-dismissible fade show" role="alert"
            style="display: none;">
            <i class="bi bi-check-circle-fill me-2"></i>
            <strong>加载完成！</strong> 违规登记任务报表已成功加载。
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- 任务列表 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">违规任务列表 (<span th:text="${tasks != null ? tasks.size() : 0}">0</span>)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">                        <thead>
                            <tr>
                                <th>任务ID</th>
                                <th>任务名称</th>
                                <th>所属项目</th>
                                <th>负责人</th>
                                <th>风险</th>
                                <th>进度</th>
                                <th>状态</th>
                                <th>评论天数</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>                        <tbody>
                            <tr th:if="${tasks == null || tasks.empty}">
                                <td colspan="9" class="text-center">暂无违规任务数据</td>
                            </tr>
                            <tr th:each="task : ${tasks}" class="task-row">                                <td>
                                    <a th:href="@{/tasks/{id}(id=${task.taskId})}" th:text="${task.taskId}" class="task-id-link text-decoration-none">任务ID</a>
                                </td>
                                <td th:text="${task.taskName}">任务名称</td>
                                <td th:text="${task.project != null ? task.project.projectName : ''}">所属项目</td>
                                <td th:text="${task.responsible}">负责人</td>
                                <td>
                                    <span th:class="${'badge ' +
                                                (task.risk == '正常' ? 'bg-success' :
                                                (task.risk == '低' ? 'bg-info' :
                                                (task.risk == '中' ? 'bg-warning' : 'bg-danger')))}"
                                        th:text="${task.risk}">风险等级</span>
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            th:style="'width: ' + (${task.progress != null ? task.progress : 0}) + '%'"
                                            th:class="${'progress-bar ' +
                                                      ((task.progress ?: 0) <= 30 ? 'bg-danger' :
                                                      ((task.progress ?: 0) <= 70 ? 'bg-warning' : 'bg-success'))}"
                                            th:text="(${task.progress != null ? task.progress : 0}) + '%'">0%</div>
                                    </div>
                                </td>
                                <td> <span th:class="${'badge ' +
                                                (task.status == '进行中' ? 'bg-primary' :
                                                (task.status == '已完成' ? 'bg-success' :
                                                (task.status == '未开始' ? 'bg-secondary' :
                                                (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                        th:text="${task.status}">状态</span>
                                </td>
                                <td>
                                    <span th:if="${task.commentDays != null}" th:class="${'badge ' +
                                                    (task.commentDays >= 7 ? 'bg-danger' :
                                                    (task.commentDays >= 4 ? 'bg-warning' :
                                                    (task.commentDays >= 0 ? 'bg-success' : 'bg-dark')))}"
                                        th:text="${task.commentDays}">0.0</span>
                                    <span th:unless="${task.commentDays != null}">-</span>
                                </td>
                                <td th:text="${task.createdDateTime != null ? #temporals.format(task.createdDateTime, 'yyyy-MM-dd HH:mm') : '-'}"
                                    style="white-space: nowrap;">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script th:inline="javascript">
        // 页面加载完成后显示提示
        document.addEventListener('DOMContentLoaded', function () {
            // 检查URL参数是否包含fromAjax=true
            const urlParams = new URLSearchParams(window.location.search);
            const fromAjax = urlParams.get('fromAjax');

            // 如果是从AJAX加载完成后跳转过来的，显示加载完成提示
            if (fromAjax === 'true') {
                const loadCompleteAlert = document.getElementById('loadCompleteAlert');
                if (loadCompleteAlert) {
                    loadCompleteAlert.style.display = 'block';

                    // 5秒后自动关闭提示
                    setTimeout(function () {
                        loadCompleteAlert.classList.remove('show');
                        setTimeout(function () {
                            loadCompleteAlert.style.display = 'none';
                        }, 150);
                    }, 5000);
                }
            }
        });

        // 导出PDF
        function exportToPDF() {
            window.print();
        }        // 导出Excel
        function exportToExcel() {
            const sendWeixinMessage = document.getElementById('sendWeixinMessage').checked;
            
            // 使用Thymeleaf内联表达式构建URL
            const baseUrl = /*[[@{/reports/violation-tasks/export}]]*/ '/reports/violation-tasks/export';
            
            // 构建完整URL
            const url = baseUrl + '?sendWeixinMessage=' + (sendWeixinMessage ? 'true' : 'false');
            
            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('报表导出成功！\n文件已保存到：' + data.message);
                    } else {
                        alert('导出失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('导出错误:', error);
                    alert('导出失败，请稍后重试');
                });
        }
    </script>
</body>

</html>