package com.mylog.service;

import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

@Service
public class OptionsService {

    private static final Logger logger = LoggerFactory.getLogger(OptionsService.class);

    @Value("${mylog.data.path:data}")
    private String dataPath;

    // 各种配置项的缓存
    private final Map<String, List<String>> optionsCache = new HashMap<>();

    // 移除 PostConstruct 初始化，避免重复加载
    // 由 OptionsInitializer 统一负责预加载

    /**
     * 从数据库读取配置选项
     * 
     * @param category 配置类别/文件名
     * @return 选项列表
     */
    public List<String> readOptionsFromFile(String category) {
        String fileName = category;

        // 如果未缓存，则从数据库加载
        if (!optionsCache.containsKey(fileName)) {
            loadOptionsFromDatabase(fileName.replace(".txt", ""));
        }

        // 返回缓存的选项，如果没有则返回空列表
        return optionsCache.getOrDefault(fileName, new ArrayList<>());
    }

    /**
     * 强制重新读取配置选项
     * 
     * @param category 配置类别/文件名
     * @return 选项列表
     */
    public List<String> forceReadOptionsFromFile(String category) {
        String fileName = category;

        // 从数据库重新加载选项
        loadOptionsFromDatabase(fileName.replace(".txt", ""));

        // 返回选项
        return optionsCache.getOrDefault(fileName, new ArrayList<>());
    }    /**
     * 从数据库加载配置选项
     * 
     * @param category 配置类别
     */
    private void loadOptionsFromDatabase(String category) {
        List<String> options = new ArrayList<>();
        logger.info("📊 开始从数据库加载配置: {}", category);

        try {
            String sql = "SELECT value FROM ConfigOptions WHERE category = ? ORDER BY sort_order";
            try (Connection conn = DriverManager.getConnection("************************************");
                    PreparedStatement pstmt = conn.prepareStatement(sql)) {

                pstmt.setString(1, category);
                ResultSet rs = pstmt.executeQuery();

                while (rs.next()) {
                    options.add(rs.getString("value"));
                }

                // 如果数据库中没有数据则不处理
                if (options.isEmpty()) {
                    logger.warn("📊 数据库中没有找到配置项: {}", category);
                    return;
                }

            }

            // 将选项保存到缓存
            optionsCache.put(category + ".txt", options);
            logger.info("📊 从数据库加载 {} 配置，共 {} 项，前几项: {}", 
                category, options.size(), options.subList(0, Math.min(3, options.size())));
        } catch (SQLException e) {
            logger.error("📊 从数据库加载配置 {} 失败: {}", category, e.getMessage());
            // 发生错误时使用空列表
            optionsCache.put(category + ".txt", options);
        }
    }

    /**
     * 预加载所有选项到缓存中，通常在应用启动时调用
     */
    public void preloadAllOptions() {
        logger.info("开始预加载所有选项到缓存中");

        // 清空现有缓存
        optionsCache.clear(); // 加载所有选项
        forceReadOptionsFromFile("视觉类型.txt");
        forceReadOptionsFromFile("人员.txt");
        forceReadOptionsFromFile("销售.txt");
        forceReadOptionsFromFile("机械.txt");
        forceReadOptionsFromFile("电气.txt");
        forceReadOptionsFromFile("任务名称.txt");
        forceReadOptionsFromFile("任务类型.txt");
        forceReadOptionsFromFile("项目类型.txt");

        logger.info("所有选项预加载完成");
    }

    public List<String> getVisionTypes() {
        return readOptionsFromFile("视觉类型.txt");
    }    public List<String> getPersonnel() {
        List<String> personnel = forceReadOptionsFromFile("人员.txt");
        logger.info("📊 OptionsService.getPersonnel() - 返回人员数量: {}", personnel.size());
        if (!personnel.isEmpty()) {
            logger.info("📊 OptionsService.getPersonnel() - 前几个人员: {}", personnel.subList(0, Math.min(3, personnel.size())));
        }
        return personnel;
    }

    public List<String> getSalesPersonnel() {
        return readOptionsFromFile("销售.txt");
    }

    public List<String> getMechanicalPersonnel() {
        return readOptionsFromFile("机械.txt");
    }

    public List<String> getElectricalPersonnel() {
        return readOptionsFromFile("电气.txt");
    }

    /**
     * 获取任务名称列表
     * 
     * @return 任务名称列表
     */
    public List<String> getTaskNames() {
        return readOptionsFromFile("任务名称.txt");
    }  
    
    /**
     * 获取任务持续时间比例
     * 
     * @param category 任务类别
     * @param value    任务名称
     * @param visionType 视觉类型（可以为null，用于向后兼容）
     * @return 持续时间比例，如果没有找到则返回0.0
     */
    public Double getTaskDurationRatio(String category, String value, String visionType) {
        // 从数据库usermanagement.db的configoptions表中获取category和value等于形参数的ratio和ratio2字段值
        // 任务名称只比较前2个字符
        // 如果VisionType中包含"自制"，则返回ratio，否则返回ratio2

        String sql = "SELECT ratio, ratio2 FROM ConfigOptions WHERE category = ? AND SUBSTR(value, 1, 2) = SUBSTR(?, 1, 2)";
        try (Connection conn = DriverManager.getConnection("************************************");
                PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, category);
            pstmt.setString(2, value);
            ResultSet rs = pstmt.executeQuery();            if (rs.next()) {
                // 根据VisionType是否包含"自制"来决定返回ratio还是ratio2
                // 如果visionType为null（向后兼容），则返回ratio
                if (visionType == null || visionType.contains("自制")) {
                    return rs.getDouble("ratio");
                } else {
                    return rs.getDouble("ratio2");
                }
            }
        } catch (SQLException e) {
            logger.error("获取任务持续时间比例失败: {}", e.getMessage());
        }        logger.warn("未找到任务持续时间比例: category={}, value={}, visionType={}", category, value, visionType);
        // 如果没有找到对应的比例，返回0.0
        return 0.0; // 默认返回0.0
    }

    /**
     * 获取任务类型列表
     * 
     * @return 任务类型列表
     */
    public List<String> getTaskTypes() {
        return readOptionsFromFile("任务类型.txt");
    }

    /**
     * 获取项目类型列表
     * 
     * @return 项目类型列表
     */
    public List<String> getProjectTypes() {
        return readOptionsFromFile("项目类型.txt");
    }
    
    /**
     * 根据分类和比例字段名获取前缀列表
     * 
     * @param category 配置类别
     * @param ratioFieldName 比例字段名称（"ratio"或"ratio2"）
     * @return 符合条件的记录value字段的前两个字符列表
     */
    public List<String> getPrefixesByCategoryAndRatio(String category, String ratioFieldName) {
        List<String> prefixes = new ArrayList<>();
        
        try {
            // 构建SQL查询语句，查询指定category且ratioFieldName大于0的记录
            String sql = "SELECT value FROM ConfigOptions WHERE category = ? AND " + ratioFieldName + " > 0";
            
            try (Connection conn = DriverManager.getConnection("************************************");
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setString(1, category);
                ResultSet rs = pstmt.executeQuery();
                
                while (rs.next()) {
                    String value = rs.getString("value");
                    if (value != null && value.length() >= 2) {
                        // 提取前两个字符
                        String prefix = value.substring(0, 2);
                        // 避免重复添加
                        if (!prefixes.contains(prefix)) {
                            prefixes.add(prefix);
                        }
                    }
                }
                
                // logger.info("获取到前缀列表: category={}, ratioFieldName={}, 结果数量={}", 
                //            category, ratioFieldName, prefixes.size());
            }
        } catch (SQLException e) {
            logger.error("获取前缀列表失败: category={}, ratioFieldName={}, 错误: {}", 
                        category, ratioFieldName, e.getMessage(), e);
        }
        
        return prefixes;
    }
}