@echo off
echo === Database Backup Task Quick Check ===
echo.

echo 1. Task Status:
schtasks /query /tn "MyLog-DB-Backup" /fo table 2>nul
if %errorlevel% neq 0 echo   [ERROR] Task not found!

echo.
echo 2. Backup Files:
if exist "dataBk\" (
    dir /b "dataBk\*.db" 2>nul | find /c ".db" >nul
    if %errorlevel% equ 0 (
        echo   Found backup files:
        dir /b "dataBk\*.db"
    ) else (
        echo   No backup files found
    )
) else (
    echo   Backup directory missing
)

echo.
echo 3. Quick Actions:
echo   Test backup: test-backup.bat
echo   Manual run: schtasks /run /tn "MyLog-DB-Backup"
echo   View details: check-task-status.bat
echo.
pause
