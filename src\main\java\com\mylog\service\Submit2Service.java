package com.mylog.service;

import com.mylog.model.Submit2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface Submit2Service {
    
    Submit2 saveSubmit(Submit2 submit);
    
    Optional<Submit2> findSubmitById(Long id);
    
    void deleteSubmit(Long id);
    
    Page<Submit2> findSubmitsByTaskId(Long taskId, Pageable pageable);
    
    List<Submit2> findAllSubmitsByTaskId(Long taskId);
    
    long countSubmitsByTaskId(Long taskId);
    
}