package com.mylog.service.impl;

import com.mylog.dto.EventCheckInDTO;
import com.mylog.model.EventCheckIn;
import com.mylog.model.EventReminder;
import com.mylog.model.user.User;
import com.mylog.repository.EventCheckInRepository;
import com.mylog.repository.EventReminderRepository;
import com.mylog.service.EventCheckInService;
import com.mylog.service.UserService;
import com.mylog.util.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 事件签到服务实现类
 */
@Service
@Transactional
public class EventCheckInServiceImpl implements EventCheckInService {

    private static final Logger logger = LoggerFactory.getLogger(EventCheckInServiceImpl.class);    @Autowired
    private EventCheckInRepository checkInRepository;
    
    @Autowired
    private EventReminderRepository reminderRepository;
    
    @Autowired
    private UserService userService;    @Override
    public EventCheckInDTO checkIn(Long reminderId, String userId) {
        return checkIn(reminderId, userId, null, null, null);
    }
    
    @Override
    public EventCheckInDTO checkIn(Long reminderId, String userId, String notes, Double latitude, Double longitude) {
        // 检查是否可以签到
        if (!canCheckIn(reminderId, userId)) {
            throw new IllegalStateException("无法进行签到：不在有效时间窗口内或已经签到过");
        }
        
        // 获取提醒信息
        EventReminder reminder = reminderRepository.findById(reminderId)
            .orElseThrow(() -> new IllegalArgumentException("提醒不存在"));
        
        // 转换用户ID
        Long userIdLong = convertUserIdStringToLong(userId);
        
        // 创建签到记录
        EventCheckIn checkIn = new EventCheckIn(reminder, userIdLong);
        
        // 设置备注和位置信息
        if (notes != null && !notes.trim().isEmpty()) {
            checkIn.setNotes(notes);
        }
        if (latitude != null && longitude != null) {
            checkIn.setLatitude(latitude);
            checkIn.setLongitude(longitude);
        }
        
        EventCheckIn savedCheckIn = checkInRepository.save(checkIn);
        return convertToDTO(savedCheckIn);
    }@Override
    public boolean canCheckIn(Long reminderId, String userId) {
        // 转换用户ID
        Long userIdLong = convertUserIdStringToLong(userId);
        
        // 检查是否已经签到过
        if (checkInRepository.findByEventReminderIdAndUserId(reminderId, userIdLong).isPresent()) {
            return false;
        }
        
        // 检查是否在有效时间窗口内
        return isWithinCheckInWindow(reminderId);
    }    @Override
    public Optional<EventCheckInDTO> getCheckInRecord(Long reminderId, String userId) {
        // 转换用户ID
        Long userIdLong = convertUserIdStringToLong(userId);
        
        Optional<EventCheckIn> checkIn = checkInRepository.findByEventReminderIdAndUserId(reminderId, userIdLong);
        return checkIn.map(this::convertToDTO);
    }@Override
    public List<EventCheckInDTO> getEventCheckIns(Long eventId) {
        List<EventCheckIn> checkIns = checkInRepository.findByEventReminderIdOrderByCheckInTimeDesc(eventId);
        return checkIns.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }    @Override
    public List<EventCheckInDTO> getUserCheckInHistory(String userId, LocalDateTime startDate, LocalDateTime endDate) {
        Long userIdLong = convertUserIdStringToLong(userId);
        List<EventCheckIn> checkIns = checkInRepository.findByUserIdAndTimeRange(
            userIdLong, startDate, endDate);
        return checkIns.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isWithinCheckInWindow(Long reminderId) {
        Optional<EventReminder> reminderOpt = reminderRepository.findById(reminderId);
        if (!reminderOpt.isPresent()) {
            return false;
        }
        
        EventReminder reminder = reminderOpt.get();
        
        // 如果不需要签到，返回false
        if (!reminder.getRequiresCheckIn()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime reminderTime = reminder.getReminderTime();
        int windowMinutes = reminder.getCheckInWindowMinutes() != null ? 
            reminder.getCheckInWindowMinutes() : 30; // 默认30分钟窗口
        
        // 检查是否在签到时间窗口内（提醒时间前后的窗口）
        LocalDateTime windowStart = reminderTime.minusMinutes(windowMinutes / 2);
        LocalDateTime windowEnd = reminderTime.plusMinutes(windowMinutes / 2);
        
        return now.isAfter(windowStart) && now.isBefore(windowEnd);
    }    @Override
    public CheckInStatistics getCheckInStatistics(String userId, LocalDateTime startDate, LocalDateTime endDate) {
        Long userIdLong = convertUserIdStringToLong(userId);
        
        // 获取用户在指定时间段内需要签到的提醒总数
        long totalReminders = reminderRepository.countByUserIdAndRequiresCheckInTrueAndReminderTimeBetween(
            userId, startDate, endDate);
        
        // 获取用户在指定时间段内的签到次数
        long checkedInCount = checkInRepository.countByUserIdAndTimeRange(
            userIdLong, startDate, endDate);
        
        long missedCount = totalReminders - checkedInCount;
        double checkInRate = totalReminders > 0 ? (double) checkedInCount / totalReminders * 100 : 0;
        
        return new CheckInStatistics(totalReminders, checkedInCount, missedCount, checkInRate);
    }    /**
     * 将实体转换为DTO
     */
    private EventCheckInDTO convertToDTO(EventCheckIn checkIn) {
        EventCheckInDTO dto = new EventCheckInDTO();
        dto.setId(checkIn.getId());
        dto.setUserId(checkIn.getUserId());
        dto.setCheckInTime(checkIn.getCheckInTime());
        dto.setCreatedTime(checkIn.getCreatedTime());
        dto.setLatitude(checkIn.getLatitude());
        dto.setLongitude(checkIn.getLongitude());
        dto.setNotes(checkIn.getNotes());
        
        // 安全地获取关联的提醒信息
        try {
            EventReminder reminder = checkIn.getEventReminder();
            if (reminder != null) {
                dto.setEventReminderId(reminder.getId());
                dto.setReminderMessage(reminder.getMessage());
                dto.setReminderTime(reminder.getReminderTime());
                
                // 安全地获取关联的事件信息
                try {
                    if (reminder.getEvent() != null) {
                        dto.setEventTitle(reminder.getEvent().getTitle());
                        dto.setEventStartTime(reminder.getEvent().getStartTime());
                    } else {
                        dto.setEventTitle("关联事件已删除");
                    }
                } catch (Exception e) {
                    logger.warn("获取事件信息失败，事件可能已被删除: {}", e.getMessage());
                    dto.setEventTitle("关联事件已删除");
                }
            } else {
                logger.warn("签到记录 {} 关联的提醒为空", checkIn.getId());
                dto.setEventTitle("关联提醒已删除");
                dto.setReminderMessage("提醒已删除");
            }
        } catch (Exception e) {
            logger.warn("获取提醒信息失败，提醒可能已被删除: 签到记录ID={}, 错误: {}", 
                       checkIn.getId(), e.getMessage());
            dto.setEventTitle("关联提醒已删除");
            dto.setReminderMessage("提醒已删除");
            // 设置一个默认的提醒ID，避免空指针
            dto.setEventReminderId(-1L);
        }
        
        return dto;
    }

    // ========== 为签到页面添加的新方法实现 ==========    @Override
    public List<PendingCheckInDTO> getPendingCheckIns(String userId) {
        // System.out.println("========== Service: 开始获取待签到事件 ==========");
        // System.out.println("Service: 用户ID: " + userId);
        //logger.info("========== Service: 开始获取待签到事件 =========="); // 修改为 logger.info
        //logger.info("Service: 用户ID: {}", userId); // 修改为 logger.info 并使用参数化日志

        // 获取当前用户名
        String currentUsername = SecurityUtils.getCurrentUsername();
        //logger.info("Service: 当前登录用户名: {}", currentUsername);

        // 模拟当前时间
        LocalDateTime now = LocalDateTime.now();
        // System.out.println("Service: 当前时间: " + now);
        //logger.info("Service: 当前时间: {}", now); // 修改为 logger.info        // 1. 获取用户创建的所有需要签到的事件提醒
        // System.out.println("Service: 准备调用 EventReminderRepository.findPendingCheckInsForUser");
        //logger.info("Service: 准备调用 EventReminderRepository.findPendingCheckInsForUser"); // 修改为 logger.info
        
        // 计算时间范围（当前时间的前后12小时）
        LocalDateTime startTime = now.minusHours(12);
        LocalDateTime endTime = now.plusHours(12);
        
        List<EventReminder> reminders = reminderRepository.findPendingCheckInsForUser(
            Long.valueOf(userId), 
            startTime, 
            endTime
        );

        
        // System.out.println("Service: 获取到的提醒数量: " + reminders.size());
        logger.info("Service: 获取到的提醒数量: {}", reminders.size()); // 修改为 logger.info

        List<PendingCheckInDTO> pendingCheckIns = new ArrayList<>();
        for (EventReminder reminder : reminders) {
            // 检查当前用户是否在recipients中
            String recipients = reminder.getRecipients();
            boolean isUserInRecipients = false;
            
            if (recipients != null && !recipients.trim().isEmpty() && currentUsername != null) {
                // recipients是逗号分隔的用户名列表，检查当前用户名是否包含在其中
                String[] recipientArray = recipients.split(",");
                for (String recipient : recipientArray) {
                    if (recipient.trim().equals(currentUsername)) {
                        isUserInRecipients = true;
                        break;
                    }
                }
                logger.info("Service: 提醒ID: {}, recipients: {}, 当前用户是否在recipients中: {}", 
                           reminder.getId(), recipients, isUserInRecipients);
            } else {
                // 如果recipients为空，则默认包含所有用户
                isUserInRecipients = true;
                logger.info("Service: 提醒ID: {}, recipients为空，默认包含当前用户", reminder.getId());
            }
            
            // 如果当前用户不在recipients中，跳过此提醒
            if (!isUserInRecipients) {
                logger.info("Service: 跳过提醒ID: {}, 当前用户 {} 不在recipients中", reminder.getId(), currentUsername);
                continue;
            }
            
            // 2. 计算签到窗口
            Integer checkInWindowMinutes = reminder.getCheckInWindowMinutes();
            if (checkInWindowMinutes == null) {
                checkInWindowMinutes = 30; // 默认30分钟
            }
            
            LocalDateTime checkInWindowStart = reminder.getReminderTime().minusMinutes(checkInWindowMinutes / 2);
            LocalDateTime checkInWindowEnd = reminder.getReminderTime().plusMinutes(checkInWindowMinutes / 2);
              // System.out.println("Service: 提醒ID: " + reminder.getId() + ", 提醒时间: " + reminder.getReminderTime() + ", 事件标题: " + reminder.getEvent().getTitle());
            // System.out.println("Service: 签到窗口开始时间: " + checkInWindowStart + ", 签到窗口结束时间: " + checkInWindowEnd);
            logger.info("Service: 提醒ID: {}, 提醒时间: {}, 事件标题: {}", reminder.getId(), reminder.getReminderTime(), reminder.getEvent().getTitle()); // 修改为 logger.info
            logger.info("Service: 签到窗口开始时间: {}, 签到窗口结束时间: {}", checkInWindowStart, checkInWindowEnd); // 修改为 logger.info

            // 3. 检查当前时间是否在签到窗口内
            //if (now.isAfter(checkInWindowStart) && now.isBefore(checkInWindowEnd)) {
            if(true) { // 这里暂时不检查时间，直接获取所有待签到事件
                
                // System.out.println("Service: 事件 " + reminder.getEvent().getTitle() + " 在签到窗口内");
                logger.info("Service: 事件 {} 在签到窗口内", reminder.getEvent().getTitle()); // 修改为 logger.info
                // 4. 检查该事件提醒是否已经签到
                boolean alreadyCheckedIn = checkInRepository
                    .findByEventReminderIdAndUserId(reminder.getId(), Long.valueOf(userId))
                    .isPresent();
                  // System.out.println("Service: 事件 " + reminder.getEvent().getTitle() + " 是否已签到: " + alreadyCheckedIn);
                logger.info("Service: 事件 {} 是否已签到: {}", reminder.getEvent().getTitle(), alreadyCheckedIn); // 修改为 logger.info

                if (!alreadyCheckedIn) {
                    // System.out.println("Service: 添加事件 " + reminder.getEvent().getTitle() + " 到待签到列表");
                    logger.info("Service: 添加事件 {} 到待签到列表", reminder.getEvent().getTitle()); // 修改为 logger.info
                      // 创建 EventReminderDTO
                    EventCheckInService.EventReminderDTO reminderDTO = new EventCheckInService.EventReminderDTO(
                        reminder.getId(),
                        reminder.getEvent().getTitle(),
                        reminder.getMessage(), // 添加 message 字段
                        reminder.getReminderTime(),
                        reminder.getRequiresCheckIn(),
                        reminder.getCheckInWindowMinutes()
                    );
                      pendingCheckIns.add(new PendingCheckInDTO(
                        reminder.getEvent().getId(),
                        reminder.getEvent().getTitle(),
                        reminderDTO
                    ));
                }
            }
        }
        
        // System.out.println("========== Service: 结束获取待签到事件, 数量: " + pendingCheckIns.size() + " ==========");
        logger.info("========== Service: 结束获取待签到事件, 数量: {} ==========", pendingCheckIns.size()); // 修改为 logger.info
        return pendingCheckIns;
    }

    @Override
    public SimpleStatistics getSimpleCheckInStatistics(String userId) {
        Long userIdLong = convertUserIdStringToLong(userId);
        LocalDateTime now = LocalDateTime.now();
        
        // 今日签到数
        LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
        LocalDateTime todayEnd = now.toLocalDate().atTime(23, 59, 59);
        long todayCount = checkInRepository.countByUserIdAndTimeRange(
            userIdLong, todayStart, todayEnd);
        
        // 本周签到数
        LocalDateTime weekStart = now.toLocalDate().minusDays(now.getDayOfWeek().getValue() - 1).atStartOfDay();
        long weekCount = checkInRepository.countByUserIdAndTimeRange(
            userIdLong, weekStart, now);
        
        // 本月签到数
        LocalDateTime monthStart = now.toLocalDate().withDayOfMonth(1).atStartOfDay();
        long monthCount = checkInRepository.countByUserIdAndTimeRange(
            userIdLong, monthStart, now);
        
        // 总签到数
        long totalCount = checkInRepository.countByUserId(userIdLong);
        
        return new SimpleStatistics(todayCount, weekCount, monthCount, totalCount);
    }

    @Override
    public PagedCheckInHistory getPagedCheckInHistory(String userId, String filter, int page, int size) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime;
        
        // 根据过滤条件确定时间范围
        switch (filter) {
            case "today":
                startTime = endTime.toLocalDate().atStartOfDay();
                break;
            case "week":
                startTime = endTime.toLocalDate().minusDays(endTime.getDayOfWeek().getValue() - 1).atStartOfDay();
                break;
            case "month":
                startTime = endTime.toLocalDate().withDayOfMonth(1).atStartOfDay();
                break;
            default: // "all"
                startTime = endTime.minusYears(1); // 默认查询一年内的记录
                break;        }
          // 创建分页请求
        Pageable pageable = PageRequest.of(page, size);
        
        // 转换用户ID
        Long userIdLong = convertUserIdStringToLong(userId);
        
        // 获取分页数据
        Page<EventCheckIn> checkInPage = checkInRepository.findByUserIdAndTimeRangeOrderByCheckInTimeDesc(
            userIdLong, startTime, endTime, pageable);
        
        List<EventCheckInDTO> content = checkInPage.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return new PagedCheckInHistory(content, checkInPage.getTotalPages(), checkInPage.getTotalElements());
    }

    @Override
    public EventCheckInDTO checkInByEvent(Long eventId, String userId, String notes, Double latitude, Double longitude) {
        // 查找事件相关的需要签到的提醒
        List<EventReminder> reminders = reminderRepository.findByEventIdAndRequiresCheckInTrue(eventId);
        
        if (reminders.isEmpty()) {
            throw new IllegalStateException("该事件没有需要签到的提醒");
        }
        
        // 找到第一个可以签到的提醒
        EventReminder targetReminder = null;
        for (EventReminder reminder : reminders) {
            if (canCheckIn(reminder.getId(), userId)) {
                targetReminder = reminder;
                break;
            }
        }
          if (targetReminder == null) {
            throw new IllegalStateException("当前不在任何签到时间窗口内或已经签到过");
        }
        
        // 转换用户ID
        Long userIdLong = convertUserIdStringToLong(userId);
        
        // 创建签到记录
        EventCheckIn checkIn = new EventCheckIn(targetReminder, userIdLong);
        if (notes != null && !notes.trim().isEmpty()) {
            checkIn.setNotes(notes);
        }
        if (latitude != null && longitude != null) {
            checkIn.setLatitude(latitude);
            checkIn.setLongitude(longitude);
        }
        
        EventCheckIn savedCheckIn = checkInRepository.save(checkIn);        return convertToDTO(savedCheckIn);
    }

    /**
     * 将用户名转换为用户ID
     * 如果输入的是数字字符串，直接返回该数字
     * 如果输入的是用户名，查找对应的用户ID
     */
    private Long convertUserIdStringToLong(String userIdOrUsername) {
        if (userIdOrUsername == null) {
            throw new IllegalArgumentException("用户ID或用户名不能为空");
        }
        
        // 尝试将字符串转换为数字
        try {
            return Long.valueOf(userIdOrUsername);
        } catch (NumberFormatException e) {
            // 如果转换失败，说明是用户名，需要查找用户ID
            return userService.findUserByUsername(userIdOrUsername)
                .map(User::getUserId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userIdOrUsername));
        }
    }
}
