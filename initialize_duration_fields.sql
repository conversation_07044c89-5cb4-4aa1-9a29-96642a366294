-- 初始化现有任务的累计工期和剩余工期
-- 执行日期: 2025年7月28日
-- 说明：该脚本用于为已经存在的任务计算并填充累计工期和剩余工期字段

-- 注意：此脚本应该在应用程序停止时执行，或者确保应用程序不会同时修改这些字段

-- 为了安全起见，先备份相关数据
-- 建议在执行前备份Tasks表

-- 1. 首先为所有任务设置默认值（如果字段为NULL）
UPDATE Tasks 
SET cumulative_duration_days = 0.00 
WHERE cumulative_duration_days IS NULL;

UPDATE Tasks 
SET remaining_duration_days = COALESCE(rated_duration_days, 0.00)
WHERE remaining_duration_days IS NULL;

-- 2. 为有工时记录的任务计算累计工期
-- 这里需要根据WorkHoursLog表的结构来更新
-- 假设WorkHoursLog表有以下字段：business_type, business_id, days_actual
-- 其中 business_type = '任务', business_id = taskId

-- 更新累计工期（基于工时记录表的最新存量）
UPDATE Tasks t
SET cumulative_duration_days = COALESCE((
    SELECT hours_inventory 
    FROM WorkHoursLog w 
    WHERE w.business_type = '任务' 
      AND w.business_id = CAST(t.task_id AS INTEGER)
    ORDER BY w.created_time DESC 
    LIMIT 1
), 0.00)
WHERE EXISTS (
    SELECT 1 
    FROM WorkHoursLog w 
    WHERE w.business_type = '任务' 
      AND w.business_id = CAST(t.task_id AS INTEGER)
);

-- 3. 重新计算剩余工期（额定工期 - 累计工期）
-- 允许剩余工期为负数
UPDATE Tasks 
SET remaining_duration_days = COALESCE(rated_duration_days, 0.00) - COALESCE(cumulative_duration_days, 0.00);

-- 4. 记录初始化完成的任务数量
-- 可以通过查询验证结果
SELECT 
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN cumulative_duration_days > 0 THEN 1 END) as tasks_with_cumulative_duration,
    COUNT(CASE WHEN remaining_duration_days != rated_duration_days THEN 1 END) as tasks_with_modified_remaining,
    AVG(cumulative_duration_days) as avg_cumulative,
    AVG(remaining_duration_days) as avg_remaining
FROM Tasks
WHERE rated_duration_days IS NOT NULL AND rated_duration_days > 0;

-- 提示信息
-- 初始化完成后，重启应用程序以确保新的计算逻辑生效
-- 之后每次保存任务时，累计工期和剩余工期将自动更新
