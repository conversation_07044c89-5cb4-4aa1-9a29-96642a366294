package com.mylog.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mylog.model.EventReminder;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.Duration;

/**
 * 事件提醒DTO
 */
public class EventReminderDTO {
    
    private Long id;
      @NotNull(message = "事件ID不能为空")
    private Long eventId;
    
    // 提醒时间（前端提交的分钟数，可选）
    private Integer time;
      
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reminderTime;
    
    private EventReminder.ReminderType reminderType = EventReminder.ReminderType.NOTIFICATION;
    
    private Boolean isSent = false;
      private String message;
    
    private Boolean requiresCheckIn = false;
    
    private Integer checkInWindowMinutes;
    
    private String recipients;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    // 构造函数
    public EventReminderDTO() {}
      public EventReminderDTO(EventReminder reminder) {
        this.id = reminder.getId();
        this.eventId = reminder.getEvent().getId();
        this.reminderTime = reminder.getReminderTime();
        this.reminderType = reminder.getReminderType();
        this.isSent = reminder.getIsSent();
        this.message = reminder.getMessage();
        this.createdTime = reminder.getCreatedTime();
          // 计算提醒时间差（分钟数）
        if (reminder.getReminderTime() != null && reminder.getEvent().getStartTime() != null) {
            Duration duration = Duration.between(reminder.getReminderTime(), reminder.getEvent().getStartTime());
            this.time = (int) duration.toMinutes();
        }
        
        // 填充签到相关信息
        this.requiresCheckIn = reminder.getRequiresCheckIn();
        this.checkInWindowMinutes = reminder.getCheckInWindowMinutes();
        this.recipients = reminder.getRecipients();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getEventId() {
        return eventId;
    }
    
    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }
    
    public Integer getTime() {
        return time;
    }
    
    public void setTime(Integer time) {
        this.time = time;
    }
    
    public LocalDateTime getReminderTime() {
        return reminderTime;
    }
    
    public void setReminderTime(LocalDateTime reminderTime) {
        this.reminderTime = reminderTime;
    }
    
    public EventReminder.ReminderType getReminderType() {
        return reminderType;
    }
    
    public void setReminderType(EventReminder.ReminderType reminderType) {
        this.reminderType = reminderType;
    }
    
    public Boolean getIsSent() {
        return isSent;
    }
    
    public void setIsSent(Boolean isSent) {
        this.isSent = isSent;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
      public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public Boolean getRequiresCheckIn() {
        return requiresCheckIn;
    }
    
    public void setRequiresCheckIn(Boolean requiresCheckIn) {
        this.requiresCheckIn = requiresCheckIn;
    }
    
    public Integer getCheckInWindowMinutes() {
        return checkInWindowMinutes;
    }
    
    public void setCheckInWindowMinutes(Integer checkInWindowMinutes) {
        this.checkInWindowMinutes = checkInWindowMinutes;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }
}
