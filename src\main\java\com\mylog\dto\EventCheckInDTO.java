package com.mylog.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mylog.model.EventCheckIn;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 事件签到DTO
 */
public class EventCheckInDTO {
    
    private Long id;
    
    @NotNull(message = "事件提醒ID不能为空")
    private Long eventReminderId;
    
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkInTime;
    
    private Double latitude;
    
    private Double longitude;
    
    private String notes;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    // 关联信息
    private String eventTitle;
    private String reminderMessage;
    private LocalDateTime eventStartTime;
    private LocalDateTime reminderTime;
    
    // 构造函数
    public EventCheckInDTO() {}
    
    public EventCheckInDTO(EventCheckIn checkIn) {
        this.id = checkIn.getId();
        this.eventReminderId = checkIn.getEventReminder().getId();
        this.userId = checkIn.getUserId();
        this.checkInTime = checkIn.getCheckInTime();
        this.latitude = checkIn.getLatitude();
        this.longitude = checkIn.getLongitude();
        this.notes = checkIn.getNotes();
        this.createdTime = checkIn.getCreatedTime();
        
        // 填充关联信息
        if (checkIn.getEventReminder() != null) {
            this.reminderMessage = checkIn.getEventReminder().getMessage();
            this.reminderTime = checkIn.getEventReminder().getReminderTime();
            
            if (checkIn.getEventReminder().getEvent() != null) {
                this.eventTitle = checkIn.getEventReminder().getEvent().getTitle();
                this.eventStartTime = checkIn.getEventReminder().getEvent().getStartTime();
            }
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getEventReminderId() {
        return eventReminderId;
    }
    
    public void setEventReminderId(Long eventReminderId) {
        this.eventReminderId = eventReminderId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCheckInTime() {
        return checkInTime;
    }
    
    public void setCheckInTime(LocalDateTime checkInTime) {
        this.checkInTime = checkInTime;
    }
    
    public Double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    
    public Double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public String getEventTitle() {
        return eventTitle;
    }
    
    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle;
    }
    
    public String getReminderMessage() {
        return reminderMessage;
    }
    
    public void setReminderMessage(String reminderMessage) {
        this.reminderMessage = reminderMessage;
    }
    
    public LocalDateTime getEventStartTime() {
        return eventStartTime;
    }
    
    public void setEventStartTime(LocalDateTime eventStartTime) {
        this.eventStartTime = eventStartTime;
    }
    
    public LocalDateTime getReminderTime() {
        return reminderTime;
    }
    
    public void setReminderTime(LocalDateTime reminderTime) {
        this.reminderTime = reminderTime;
    }
}
