package com.mylog.service;

import com.mylog.model.user.User;

import java.util.List;
import java.util.Optional;

public interface UserService {
    
    List<User> findAllUsers();
    
    Optional<User> findUserById(Long id);
    
    Optional<User> findUserByUsername(String username);
    
    User saveUser(User user);
    
    void deleteUser(Long id);
    
    boolean existsByUsername(String username);
    
    boolean changePassword(Long userId, String currentPassword, String newPassword);
} 