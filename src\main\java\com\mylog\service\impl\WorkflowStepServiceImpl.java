package com.mylog.service.impl;

import com.mylog.model.workflow.WorkflowStep;
import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.repository.workflow.WorkflowStepRepository;
import com.mylog.repository.workflow.WorkflowTemplateRepository;
import com.mylog.service.WorkflowStepService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 流程步骤服务实现类
 */
@Service
public class WorkflowStepServiceImpl implements WorkflowStepService {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowStepServiceImpl.class);

    @Autowired
    private WorkflowStepRepository stepRepository;

    @Autowired
    private WorkflowTemplateRepository templateRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowStep> findAllSteps() {
        return stepRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowStep> findStepById(Long id) {
        return stepRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowStep> findStepsByTemplateId(Long templateId) {
        logger.info("开始查询模板ID: {} 的步骤列表", templateId);

        // 首先尝试使用JPQL查询
        List<WorkflowStep> steps = stepRepository.findAllStepsByTemplateId(templateId);
        logger.info("使用JPQL查询步骤，模板ID: {}, 找到步骤数量: {}", templateId, steps.size());

        // 如果没有找到步骤，尝试使用标准方法查询
        if (steps.isEmpty()) {
            logger.info("使用JPQL未找到步骤，尝试使用标准方法查询");
            steps = stepRepository.findByTemplateTemplateIdOrderByStepOrderAsc(templateId);
            logger.info("使用标准方法查询找到步骤数量: {}", steps.size());
        }

        // 如果仍然没有找到步骤，尝试直接从数据库查询
        if (steps.isEmpty()) {
            logger.info("尝试使用原生SQL查询步骤");
            // 获取模板对象
            Optional<WorkflowTemplate> templateOpt = templateRepository.findById(templateId);
            if (templateOpt.isPresent()) {
                WorkflowTemplate template = templateOpt.get();
                // 强制初始化步骤列表
                if (template.getSteps() != null) {
                    // 使用Hibernate.initialize强制初始化集合
                    org.hibernate.Hibernate.initialize(template.getSteps());
                    steps = new ArrayList<>(template.getSteps());
                    logger.info("从模板对象中获取步骤列表，数量: {}", steps.size());
                }
            }
        }

        // 记录每个步骤的详细信息
        if (!steps.isEmpty()) {
            for (int i = 0; i < steps.size(); i++) {
                WorkflowStep step = steps.get(i);
                logger.info("步骤 #{}: ID={}, 名称={}, 顺序={}, 审批人类型={}",
                    i+1, step.getStepId(), step.getStepName(), step.getStepOrder(), step.getApproverType());
            }
        } else {
            logger.warn("未找到模板ID: {} 的任何步骤", templateId);
        }

        // 确保步骤按顺序排序
        if (steps.size() > 1) {
            steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));
        }

        return steps;
    }

    @Override
    @Transactional
    public WorkflowStep saveStep(WorkflowStep step) {
        // 如果是新建步骤，设置创建时间和步骤顺序
        if (step.getStepId() == null) {
            step.setCreatedDateTime(LocalDateTime.now());

            // 如果没有设置步骤顺序，自动分配下一个顺序
            if (step.getStepOrder() == null) {
                step.setStepOrder(getNextStepOrder(step.getTemplate().getTemplateId()));
            }
        } else {
            // 如果是更新步骤，设置最后修改时间
            step.setLastModifiedDateTime(LocalDateTime.now());

            // 获取现有步骤，保留创建时间和创建人
            Optional<WorkflowStep> existingStep = stepRepository.findById(step.getStepId());
            if (existingStep.isPresent()) {
                WorkflowStep existing = existingStep.get();
                step.setCreatedDate(existing.getCreatedDate());
                step.setCreatedBy(existing.getCreatedBy());
            }
        }

        return stepRepository.save(step);
    }

    @Override
    @Transactional
    public List<WorkflowStep> saveAllSteps(List<WorkflowStep> steps) {
        LocalDateTime now = LocalDateTime.now();

        // 处理每个步骤
        for (WorkflowStep step : steps) {
            if (step.getStepId() == null) {
                step.setCreatedDateTime(now);
            } else {
                step.setLastModifiedDateTime(now);

                // 获取现有步骤，保留创建时间和创建人
                Optional<WorkflowStep> existingStep = stepRepository.findById(step.getStepId());
                if (existingStep.isPresent()) {
                    WorkflowStep existing = existingStep.get();
                    step.setCreatedDate(existing.getCreatedDate());
                    step.setCreatedBy(existing.getCreatedBy());
                }
            }
        }

        return stepRepository.saveAll(steps);
    }

    @Override
    @Transactional
    public void deleteStep(Long id) {
        stepRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowStep> findNextStep(Long templateId, Integer currentOrder) {
        List<WorkflowStep> nextSteps = stepRepository.findNextSteps(templateId, currentOrder);
        return nextSteps.isEmpty() ? Optional.empty() : Optional.of(nextSteps.get(0));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowStep> findPreviousStep(Long templateId, Integer currentOrder) {
        List<WorkflowStep> prevSteps = stepRepository.findPreviousSteps(templateId, currentOrder);
        return prevSteps.isEmpty() ? Optional.empty() : Optional.of(prevSteps.get(0));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowStep> findFirstStep(Long templateId) {
        List<WorkflowStep> firstSteps = stepRepository.findFirstStep(templateId);
        return firstSteps.isEmpty() ? Optional.empty() : Optional.of(firstSteps.get(0));
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getNextStepOrder(Long templateId) {
        Integer maxOrder = stepRepository.findMaxStepOrder(templateId);
        return (maxOrder == null) ? 1 : maxOrder + 1;
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowStep> findStepsByTemplateIdDirect(Long templateId) {
        logger.info("使用EntityManager直接查询步骤，模板ID: {}", templateId);

        try {
            // 使用原生SQL查询
            String sql = "SELECT s.* FROM workflow_steps s WHERE s.template_id = :templateId ORDER BY s.step_order ASC";
            Query query = entityManager.createNativeQuery(sql, WorkflowStep.class);
            query.setParameter("templateId", templateId);

            @SuppressWarnings("unchecked")
            List<WorkflowStep> steps = query.getResultList();

            logger.info("使用EntityManager直接查询找到步骤数量: {}", steps.size());

            // 记录每个步骤的详细信息
            for (int i = 0; i < steps.size(); i++) {
                WorkflowStep step = steps.get(i);
                logger.info("直接查询步骤 #{}: ID={}, 名称={}, 顺序={}",
                    i+1, step.getStepId(), step.getStepName(), step.getStepOrder());
            }

            return steps;
        } catch (Exception e) {
            logger.error("使用EntityManager直接查询步骤时出错: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public void reorderSteps(Long templateId, Long stepId, Integer newOrder) {
        // 获取所有步骤
        List<WorkflowStep> steps = stepRepository.findByTemplateTemplateIdOrderByStepOrderAsc(templateId);

        // 找到要移动的步骤
        Optional<WorkflowStep> targetStepOpt = steps.stream()
                .filter(s -> s.getStepId().equals(stepId))
                .findFirst();

        if (targetStepOpt.isPresent()) {
            WorkflowStep targetStep = targetStepOpt.get();
            Integer oldOrder = targetStep.getStepOrder();

            // 如果新旧顺序相同，不需要调整
            if (oldOrder.equals(newOrder)) {
                return;
            }

            // 调整其他步骤的顺序
            for (WorkflowStep step : steps) {
                if (step.getStepId().equals(stepId)) {
                    // 目标步骤设置为新顺序
                    step.setStepOrder(newOrder);
                } else if (oldOrder < newOrder) {
                    // 向下移动：中间步骤顺序减1
                    if (step.getStepOrder() > oldOrder && step.getStepOrder() <= newOrder) {
                        step.setStepOrder(step.getStepOrder() - 1);
                    }
                } else {
                    // 向上移动：中间步骤顺序加1
                    if (step.getStepOrder() >= newOrder && step.getStepOrder() < oldOrder) {
                        step.setStepOrder(step.getStepOrder() + 1);
                    }
                }

                // 更新最后修改时间
                step.setLastModifiedDateTime(LocalDateTime.now());
            }

            // 保存所有步骤
            stepRepository.saveAll(steps);
        }
    }
}
