package com.mylog.repository;

import com.mylog.model.PersonnelStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PersonnelStatusRepository extends JpaRepository<PersonnelStatus, Long> {
    
    Optional<PersonnelStatus> findByName(String name);
    
    List<PersonnelStatus> findByStatus(String status);
    
    List<PersonnelStatus> findByLocation(String location);
    
    @Query("SELECT DISTINCT p.status FROM PersonnelStatus p")
    List<String> findAllDistinctStatuses();
    
    @Query("SELECT DISTINCT p.location FROM PersonnelStatus p")
    List<String> findAllDistinctLocations();
}
