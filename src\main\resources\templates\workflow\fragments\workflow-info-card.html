<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<body>                <!-- 流程基本信息卡片片段 -->
    <div th:fragment="info-card(instance, currentStep, totalSteps, stepLabels, stepComments, stepTimes)" class="card">
        <div class="card-header">
            <h5 class="mb-0">基本信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- 第一列 -->
                <div class="col-md-6">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th style="width: 30%">流程标题</th>
                                <td th:text="${instance.title}">流程标题</td>
                            </tr>
                            <tr>
                                <th>流程模板</th>
                                <td th:text="${instance.template != null ? instance.template.templateName : '未指定模板'}">流程模板</td>
                            </tr>
                            <tr>
                                <th>状态</th>
                                <td>
                                    <span th:if="${instance.status.name() == 'DRAFT'}" class="badge bg-secondary">草稿</span>
                                    <span th:if="${instance.status.name() == 'PROCESSING'}" class="badge bg-primary">处理中</span>
                                    <span th:if="${instance.status.name() == 'APPROVED'}" class="badge bg-success">已批准</span>
                                    <span th:if="${instance.status.name() == 'REJECTED'}" class="badge bg-danger">已拒绝</span>
                                    <span th:if="${instance.status.name() == 'CANCELED'}" class="badge bg-warning">已取消</span>
                                    <span th:if="${instance.status.name() == 'TERMINATED'}" class="badge bg-dark">已终止</span>
                                </td>
                            </tr>
                            <tr>
                                <th>发起人</th>
                                <td th:text="${instance.initiator}">发起人</td>
                            </tr>
                            <tr>
                                <th>待审批人</th>
                                <td th:text="${instance.currentApprover ?: '-'}">待审批人</td>
                            </tr>
                            <tr>
                                <th>业务类型</th>
                                <td th:text="${instance.businessType ?: '-'}">业务类型</td>
                            </tr>
                           
                            <tr>
                                <th>出发地</th>
                                <td th:text="${instance.startLocation ?: '-'}">出发地</td>
                            </tr>
                            <tr>
                                <th>目的地</th>
                                <td th:text="${instance.endLocation ?: '-'}">目的地</td>
                            </tr>

                            <tr>
                                <th>人员</th>
                                <td th:text="${instance.staff ?: '-'}">人员</td>
                            </tr>
                            <tr>
                                <th>流程说明</th>
                                <td>
                                    <span style="color: red; " th:text="${instance.description ?: '无'}">流程说明</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 第二列 -->
                <div class="col-md-6">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th>业务ID</th>
                                <td>
                                    <!-- 当业务类型为"任务"时，显示链接 -->
                                    <a th:if="${instance.businessType == '任务' && instance.businessId != null}"
                                        th:href="@{/tasks/{id}(id=${instance.businessId})}" th:text="${instance.businessId}"
                                        title="点击查看任务详情" target="_blank">业务ID</a>
                                    <!-- 其他情况显示普通文本 -->
                                    <span th:unless="${instance.businessType == '任务' && instance.businessId != null}"
                                        th:text="${instance.businessId ?: '-'}">业务ID</span>
                                </td>
                            </tr>
                            <tr>
                                <th>创建时间</th>
                                <td
                                    th:text="${instance.createdDateTime != null ? #temporals.format(instance.createdDateTime, 'yyyy-MM-dd HH:mm:ss') : '-'}">
                                    创建时间</td>
                            </tr>
                           
                            <tr>
                                <th style="width: 30%">提交时间</th>
                                <td
                                    th:text="${instance.submittedDateTime != null ? #temporals.format(instance.submittedDateTime, 'yyyy-MM-dd HH:mm:ss') : '-'}">
                                    提交时间</td>
                            </tr>
                            <tr>
                                <th>完成时间</th>
                                <td
                                    th:text="${instance.completedDateTime != null ? #temporals.format(instance.completedDateTime, 'yyyy-MM-dd HH:mm:ss') : '-'}">
                                    完成时间</td>
                            </tr>                            <tr>
                                <th>开始时间</th>
                                <td th:text="${instance.startDateTime != null ? #temporals.format(instance.startDateTime, 'yyyy-MM-dd HH:mm:ss') : (instance.startTime ?: '-')}">开始时间</td>
                            </tr>
                            <tr>
                                <th>结束时间</th>
                                <td th:text="${instance.endDateTime != null ? #temporals.format(instance.endDateTime, 'yyyy-MM-dd HH:mm:ss') : (instance.endTime ?: '-')}">结束时间</td>
                            </tr>
                            <tr>
                                <th>备注1</th>
                                <td th:text="${instance.remark1 ?: '-'}">备注1</td>
                            </tr>

                            <tr>
                                <th>备注2</th>
                                <td th:text="${instance.remark2 ?: '-'}">备注2</td>
                            </tr>
                            <tr>
                                <th>备注3</th>
                                <td th:text="${instance.remark3 ?: '-'}">备注3</td>
                            </tr>
                            <tr>
                                <th>备注4</th>
                                <td th:text="${instance.remark4 ?: '-'}">备注4</td>
                            </tr>
                            
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 流程进度 - 保持全宽 -->
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">流程进度</h5>
                        </div>
                        <div class="card-body">
                            <span class="badge bg-info mb-2" th:text="${currentStep + '/' + (totalSteps) + ' 步'}">0/0 步</span>
                            <!-- 带审批人信息的进度条 - 始终显示 -->
                            <div class="workflow-progress mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <th:block th:each="i, iStat : ${#numbers.sequence(1, totalSteps)}">                                        <div class="workflow-step" th:classappend="${i <= currentStep ? 'active' : ''}">
                                            <span class="step-number" th:text="${i}">1</span>
                                            <!-- 使用stepLabels获取步骤对应的审批人信息，若不存在则显示默认文本 -->
                                            <span class="step-name"
                                                th:text="${stepLabels != null && stepLabels.containsKey(i) ? stepLabels.get(i) : ('步骤' + i)}">步骤</span>
                                            <!-- 显示审批意见 -->
                                            <span class="step-comment" 
                                                th:if="${stepComments != null && stepComments.containsKey(i)}"
                                                th:text="${stepComments.get(i)}"
                                                th:title="${stepComments.get(i)}">意见</span>
                                            <!-- 显示步骤时间 -->
                                            <span class="step-time" 
                                                th:if="${stepTimes != null && stepTimes.containsKey(i)}"
                                                th:text="${stepTimes.get(i)}">时间</span>
                                        </div>
                                        <!-- 添加连接线，最后一个步骤不需要 -->
                                        <div th:if="${!iStat.last}" class="workflow-connector"
                                            th:classappend="${i < currentStep ? 'active' : ''}"></div>
                                    </th:block>
                                </div>
                            </div>

                            <style>
                                .workflow-progress {
                                    padding: 10px 0;
                                }

                                .workflow-step {
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    position: relative;
                                    z-index: 1;
                                    padding: 0 5px;
                                    min-width: 50px;
                                    max-width: 150px;
                                }

                                .workflow-step .step-number {
                                    width: 30px;
                                    height: 30px;
                                    border-radius: 50%;
                                    background-color: #ccc;
                                    color: #fff;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    margin-bottom: 5px;
                                    transition: all 0.3s ease;
                                }                                .workflow-step .step-name {
                                    font-size: 12px;
                                    color: #6c757d;
                                    text-align: center;
                                    transition: all 0.3s ease;
                                    word-break: break-word;
                                }                                .workflow-step .step-time {
                                    font-size: 10px;
                                    color: #6c757d;
                                    text-align: center;
                                    margin-top: 2px;
                                    opacity: 0.8;
                                }

                                .workflow-step .step-comment {
                                    font-size: 9px;
                                    color: #495057;
                                    text-align: center;
                                    margin-top: 2px;
                                    background-color: #f8f9fa;
                                    border-radius: 3px;
                                    padding: 4px 6px;
                                    max-width: 150px;
                                    min-width: 80px;
                                    overflow-wrap: break-word;
                                    word-wrap: break-word;
                                    word-break: break-all;
                                    cursor: help;
                                    line-height: 1.3;
                                    min-height: 20px;
                                    display: block;
                                }

                                .workflow-step.active .step-number {
                                    background-color: #0d6efd;
                                    box-shadow: 0 0 10px rgba(13, 110, 253, 0.5);
                                }

                                .workflow-step.active .step-name {
                                    color: #0d6efd;
                                    font-weight: bold;
                                }

                                .workflow-connector {
                                    flex-grow: 1;
                                    height: 3px;
                                    background-color: #ccc;
                                    margin-top: 15px;
                                    transition: all 0.3s ease;
                                }

                                .workflow-connector.active {
                                    background-color: #0d6efd;
                                }
                            </style>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>