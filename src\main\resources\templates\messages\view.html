﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('查看消息')}">
    <meta charset="UTF-8">
    <title>查看消息</title>
    <style>
        .message-content {
            white-space: pre-line;
        }
    </style>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                    <h1 class="h2">查看消息</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a th:href="@{/messages}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>

                <!-- 消息详情 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" th:text="${message.messageTitle}">消息标题</h5>
                        <small th:text="${message.createdDateStr}">2023-01-01 12:00:00</small>
                    </div>
                    <div class="card-body">
                        <div class="mb-4 message-content" th:text="${message.messageContent}">消息内容...</div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-secondary me-2">
                                    <span th:if="${message.relatedType == 'Project'}">相关项目</span>
                                    <span th:if="${message.relatedType == 'Task'}">相关任务</span>
                                    <span th:if="${message.relatedType == 'SubTask'}">相关任务评论</span>
                                </span>
                                <span class="badge bg-success" th:if="${message.read}">已读</span>
                                <span class="badge bg-primary" th:unless="${message.read}">未读</span>
                            </div>
                            <div>
                                <a th:if="${message.relatedType == 'Project'}"
                                   th:href="@{/projects/{id}(id=${message.relatedId})}"
                                   class="btn btn-sm btn-outline-primary me-2">
                                    <i class="bi bi-link-45deg"></i> 查看相关项目
                                </a>
                                <a th:if="${message.relatedType == 'Task'}"
                                   th:href="@{/tasks/{id}(id=${message.relatedId})}"
                                   class="btn btn-sm btn-outline-primary me-2">
                                    <i class="bi bi-link-45deg"></i> 查看相关任务
                                </a>
                                <form th:action="@{/messages/delete}" method="post" class="d-inline">
                                    <input type="hidden" name="messageId" th:value="${message.messageId}">
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                            onclick="return confirm('确定要删除这条消息吗？')">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
    </div>
</body>
</html>