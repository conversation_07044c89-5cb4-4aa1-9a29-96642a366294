/**
 * 页面历史记录跟踪器
 * 用于跟踪用户访问的页面历史，并提供智能返回功能
 * 确保永远不会返回到新建任务和编辑任务页面
 */

// 页面加载时记录当前URL到历史堆栈
window.addEventListener('load', function() {
    try {
        // 检查当前页面是否是表单页面，如果是，则不添加到历史记录中
        if (sessionStorage.getItem('isFormPage') === 'true') {
            console.log('Current page is a form page, not adding to history stack');
            // 重置表单页面标记，防止影响后续页面
            sessionStorage.removeItem('isFormPage');
            return;
        }
        
        var historyStack = [];
        var storedHistory = sessionStorage.getItem('pageHistory');
        
        if (storedHistory) {
            try {
                historyStack = JSON.parse(storedHistory);
                // 确保历史堆栈是一个数组
                if (!Array.isArray(historyStack)) {
                    console.error('History stack is not an array, resetting');
                    historyStack = [];
                }
            } catch (e) {
                console.error('Error parsing history stack, resetting', e);
                historyStack = [];
            }
        }
        
        // 检查当前页面是否已经在历史堆栈中
        var currentUrl = window.location.href;
        
        // 如果当前URL包含success=true参数，则不添加到历史记录中
        if (currentUrl.includes('success=true')) {
            console.log('Current page has success=true parameter, not adding to history stack');
            return;
        }
        
        // 检查是否是从表单页面返回的
        if (document.referrer && isFormPage(document.referrer)) {
            console.log('Coming from a form page, not adding current page to history stack');
            return;
        }
        
        // 如果历史堆栈为空或最后一个URL不是当前URL，则添加当前URL到历史堆栈
        if (historyStack.length === 0 || historyStack[historyStack.length - 1] !== currentUrl) {
            // 添加当前页面到历史堆栈
            historyStack.push(currentUrl);
            
            // 限制历史堆栈大小，防止过大
            if (historyStack.length > 20) {
                historyStack = historyStack.slice(-20);
            }
            
            // 保存更新后的历史堆栈
            sessionStorage.setItem('pageHistory', JSON.stringify(historyStack));
        }
        
        console.log('Current history stack:', historyStack);
    } catch (e) {
        console.error('Error updating history in sessionStorage:', e);
    }
});

/**
 * 检查URL是否是表单页面
 * @param {string} url - 要检查的URL
 * @returns {boolean} - 如果是表单页面则返回true，否则返回false
 */
function isFormPage(url) {
    // 定义表单页面的URL模式
    var formPatterns = [
        '/tasks/new',
        '/tasks/edit',
        '/tasks/.*/edit',
        '/subtasks/new',
        '/projects/new',
        '/projects/.*/edit'
    ];
    
    // 检查URL是否匹配任何表单页面模式
    for (var i = 0; i < formPatterns.length; i++) {
        var pattern = new RegExp(formPatterns[i]);
        if (pattern.test(url)) {
            return true;
        }
    }
    
    return false;
}

/**
 * 在表单页面加载时设置标记
 * 应该在所有表单页面调用此函数
 */
function markAsFormPage() {
    sessionStorage.setItem('isFormPage', 'true');
}

/**
 * 智能返回函数，忽略所有编辑和创建页面
 * 可以在任何页面调用此函数来实现智能返回
 */
function handleSmartBack() {
    // 获取历史记录
    var historyStack = [];
    try {
        const storedHistory = sessionStorage.getItem('pageHistory');
        if (storedHistory) {
            historyStack = JSON.parse(storedHistory);
        }
    } catch (e) {
        console.error('读取历史记录出错:', e);
    }
    
    // 如果没有历史记录或历史记录为空，则返回到首页
    if (!historyStack || historyStack.length <= 1) {
        window.location.href = '/dashboard';
        return;
    }
    
    // 定义需要忽略的页面路径模式
    var ignorePatterns = [
        '/tasks/new',
        '/tasks/edit',
        '/tasks/.*/edit',
        '/subtasks/new',
        '/projects/new',
        '/projects/.*/edit'
    ];
    
    // 移除当前页面
    historyStack.pop();
    
    // 查找最近的非编辑/创建页面
    var targetUrl = null;
    while (historyStack.length > 0) {
        var lastUrl = historyStack[historyStack.length - 1];
        var shouldIgnore = ignorePatterns.some(pattern => {
            // 将模式转换为正则表达式
            var regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(new URL(lastUrl).pathname);
        });
        
        if (!shouldIgnore) {
            targetUrl = lastUrl;
            break;
        }
        historyStack.pop();
    }
    
    if (targetUrl) {
        // 更新历史记录，只保留到目标URL为止的记录
        var newHistoryStack = historyStack.slice(0, historyStack.indexOf(targetUrl) + 1);
        sessionStorage.setItem('pageHistory', JSON.stringify(newHistoryStack));
        
        // 如果目标URL是新建任务页面，需要返回到项目详情页
        if (targetUrl.includes('/tasks/new')) {
            // 从URL中获取projectId参数
            const urlParams = new URLSearchParams(new URL(targetUrl).search);
            const projectId = urlParams.get('projectId');
            if (projectId) {
                window.location.href = '/projects/' + projectId;
                return;
            }
        }
        
        window.location.href = targetUrl;
    } else {
        // 如果没有找到有效页面，则返回到首页
        sessionStorage.removeItem('pageHistory');
        window.location.href = '/dashboard';
    }
}