package com.mylog.controller.workflow;

import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.service.WorkflowStepService;
import com.mylog.service.WorkflowTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 流程模板REST控制器
 */
@RestController
@RequestMapping("/workflow/templates")
public class WorkflowTemplateRestController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowTemplateRestController.class);

    @Autowired
    private WorkflowStepService stepService;
    
    @Autowired
    private WorkflowTemplateService templateService;

    /**
     * 获取模板的步骤数量
     */
    @GetMapping(value = "/{templateId}/step-count", produces = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE,
            MediaType.ALL_VALUE
    })
    public ResponseEntity<Map<String, Integer>> getTemplateStepCount(@PathVariable Long templateId) {
        try {
            int stepCount = stepService.findStepsByTemplateId(templateId).size();
            Map<String, Integer> response = new HashMap<>();
            response.put("stepCount", stepCount);
            logger.debug("返回模板 ID {} 的步骤数量: {}", templateId, stepCount);
            
            // 明确设置内容类型为APPLICATION_JSON
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
        } catch (Exception e) {
            logger.error("获取模板步骤数量时出错，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            Map<String, Integer> errorResponse = new HashMap<>();
            errorResponse.put("stepCount", 0);
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(errorResponse);
        }
    }
    
    /**
     * 获取模板详细信息
     */
    @GetMapping(value = "/{templateId}/detail", produces = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE,
            MediaType.ALL_VALUE
    })
    public ResponseEntity<?> getTemplateDetail(@PathVariable Long templateId) {
        try {
            logger.debug("获取模板详细信息，ID: {}", templateId);
            Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(templateId);
            
            if (templateOpt.isPresent()) {
                WorkflowTemplate template = templateOpt.get();
                Map<String, String> response = new HashMap<>();
                response.put("templateTitle", template.getTemplateTitle());
                response.put("applicableScope", template.getApplicableScope());
                response.put("description", template.getDescription());
                // 为了与前端兼容，添加templateCode
                response.put("templateCode", template.getTemplateTitle());
                logger.debug("返回模板详情: {}", response);
                
                // 明确设置内容类型为APPLICATION_JSON
                return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);
            } else {
                logger.warn("未找到模板，ID: {}", templateId);
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "模板不存在");
                
                // 即使是错误响应也要设置内容类型
                return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
            }
        } catch (Exception e) {
            logger.error("获取模板详情时出错，模板ID: {}, 错误: {}", templateId, e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取模板详情失败: " + e.getMessage());
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(errorResponse);
        }
    }
}