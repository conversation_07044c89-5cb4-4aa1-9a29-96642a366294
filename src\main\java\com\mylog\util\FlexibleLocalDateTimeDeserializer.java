package com.mylog.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 灵活的LocalDateTime反序列化器
 * 支持多种格式的日期时间字符串反序列化
 */
public class FlexibleLocalDateTimeDeserializer extends LocalDateTimeDeserializer {

    private static final long serialVersionUID = 1L;
    
    /**
     * 标准格式: yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter STANDARD_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * ISO格式: yyyy-MM-dd'T'HH:mm:ss
     */
    private static final DateTimeFormatter ISO_FORMATTER = 
            DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    public FlexibleLocalDateTimeDeserializer() {
        super(STANDARD_FORMATTER);
    }

    @Override
    public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        String text = parser.getText().trim();
        
        // 如果为空，直接返回null
        if (text == null || text.isEmpty()) {
            return null;
        }
        
        try {
            // 尝试使用标准格式解析
            if (text.contains(" ")) {
                return LocalDateTime.parse(text, STANDARD_FORMATTER);
            }
            
            // 尝试使用ISO格式解析
            if (text.contains("T")) {
                return LocalDateTime.parse(text, ISO_FORMATTER);
            }
            
            // 如果以上格式都不匹配，使用默认反序列化逻辑
            return super.deserialize(parser, context);
            
        } catch (DateTimeParseException e) {
            // 捕获解析异常，尝试使用其他格式
            try {
                // 如果标准格式解析失败，尝试ISO格式
                if (text.contains(" ")) {
                    String isoText = text.replace(" ", "T");
                    return LocalDateTime.parse(isoText, ISO_FORMATTER);
                }
                
                // 如果ISO格式解析失败，尝试标准格式
                if (text.contains("T")) {
                    String stdText = text.replace("T", " ");
                    return LocalDateTime.parse(stdText, STANDARD_FORMATTER);
                }
            } catch (DateTimeParseException ex) {
                // 所有尝试都失败，继续抛出异常
                throw new IOException("无法解析日期时间字符串: " + text, ex);
            }
            
            // 如果所有尝试都失败，继续抛出异常
            throw new IOException("无法解析日期时间字符串: " + text, e);
        }
    }
}
