package com.mylog.controller;

import com.mylog.model.UserActivityLog;
import com.mylog.model.user.User;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/admin/activity-logs")
@PreAuthorize("hasRole('ADMIN')")
public class UserActivityLogController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserActivityLogController.class);
    
    @Autowired
    private UserActivityLogService logService;
    
    @Autowired
    private UserService userService;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 显示所有用户活动日志
     */
    @GetMapping
    public String listLogs(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "sort", defaultValue = "createdDate") String sort,
            @RequestParam(value = "direction", defaultValue = "desc") String direction,
            Model model) {
        
        logger.info("查询用户活动日志列表，页码: {}, 大小: {}, 排序: {}, 方向: {}", page, size, sort, direction);
        
        try {
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, Math.min(size, 20), Sort.by(sortDirection, sort));
              Page<UserActivityLog> logPage = logService.findAllLogs(pageable);
            
            // 检查当前页码是否超过总页数，如果是则重定向到最后一页
            if (page > 0 && logPage.getTotalPages() > 0 && page >= logPage.getTotalPages()) {
                logger.info("当前页码 {} 超过总页数 {}，重定向到最后一页", page + 1, logPage.getTotalPages());
                String redirectUrl = "/admin/activity-logs?page=" + (logPage.getTotalPages() - 1) + 
                                   "&size=" + size + "&sort=" + sort + "&direction=" + direction;
                return "redirect:" + redirectUrl;
            }
            
            // 格式化每个日志的时间戳
            formatLogTimestamps(logPage.getContent());

            model.addAttribute("logPage", logPage);
            model.addAttribute("activeMenu", "activitylogs");
            model.addAttribute("activityTypes", UserActivityLog.ActivityType.values());
            model.addAttribute("isSearch", false);
            
            // 获取所有用户列表，用于筛选
            List<User> users = userService.findAllUsers();
            model.addAttribute("users", users);
            
            // 常用实体类型列表
            model.addAttribute("entityTypes", Arrays.asList("Project", "User", "Task", "SubTask", "Message", "Submit2"));
            
            // 添加分页相关的属性
            model.addAttribute("currentPage", page);
            model.addAttribute("pageSize", size);
            model.addAttribute("totalPages", logPage.getTotalPages());
            model.addAttribute("totalItems", logPage.getTotalElements());
            model.addAttribute("sortField", sort);
            model.addAttribute("sortDirection", direction);
            
            return "admin/activity-logs/list";
        } catch (Exception e) {
            logger.error("获取活动日志列表时发生错误", e);
            model.addAttribute("error", "获取活动日志时出现系统错误，请稍后再试或联系管理员");
            
            // 添加最小必要数据，确保页面能够加载
            model.addAttribute("logPage", Page.empty());
            model.addAttribute("activeMenu", "activitylogs");
            model.addAttribute("activityTypes", UserActivityLog.ActivityType.values());
            model.addAttribute("users", userService.findAllUsers());
            model.addAttribute("currentPage", 0);
            model.addAttribute("pageSize", 20);
            model.addAttribute("totalPages", 0);
            model.addAttribute("totalItems", 0);
            model.addAttribute("sortField", sort);
            model.addAttribute("sortDirection", direction);
            
            return "admin/activity-logs/list";
        }
    }
    
    /**
     * 高级搜索日志
     */
    @GetMapping("/search")
    public String searchLogs(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "activityType", required = false) String activityType,
            @RequestParam(value = "entityType", required = false) String entityType,
            @RequestParam(value = "entityId", required = false) Long entityId,
            @RequestParam(value = "startTime", required = false) 
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(value = "endTime", required = false) 
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "sort", defaultValue = "createdDate") String sort,
            @RequestParam(value = "direction", defaultValue = "desc") String direction,
            Model model) {
        
        logger.info("搜索用户活动日志，用户ID: {}, 用户名: {}, 活动类型: {}, 实体类型: {}, 实体ID: {}, 开始时间: {}, 结束时间: {}", 
                  userId, username, activityType, entityType, entityId, startTime, endTime);
        
        try {
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, Math.min(size, 20), Sort.by(sortDirection, sort));
            
            // 构建搜索条件
            Map<String, Object> searchCriteria = new HashMap<>();
            if (userId != null) {
                searchCriteria.put("userId", userId);
            }
            if (username != null && !username.trim().isEmpty()) {
                searchCriteria.put("username", username.trim());
            }
            if (activityType != null && !activityType.trim().isEmpty()) {
                try {
                    UserActivityLog.ActivityType type = UserActivityLog.ActivityType.valueOf(activityType.trim());
                    searchCriteria.put("activityType", type);
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的活动类型: {}", activityType);
                }
            }
            if (entityType != null && !entityType.trim().isEmpty()) {
                searchCriteria.put("entityType", entityType.trim());
            }
            if (entityId != null) {
                searchCriteria.put("entityId", entityId);
            }
            if (startTime != null) {
                searchCriteria.put("startTime", startTime);
            }
            if (endTime != null) {
                searchCriteria.put("endTime", endTime);
            }
              Page<UserActivityLog> logPage = logService.searchLogs(searchCriteria, pageable);
            
            // 检查当前页码是否超过总页数，如果是则重定向到最后一页
            if (page > 0 && logPage.getTotalPages() > 0 && page >= logPage.getTotalPages()) {
                logger.info("当前页码 {} 超过总页数 {}，重定向到最后一页", page + 1, logPage.getTotalPages());
                // 构建重定向URL，保持所有搜索参数
                StringBuilder redirectUrl = new StringBuilder("/admin/activity-logs/search?page=")
                    .append(logPage.getTotalPages() - 1)
                    .append("&size=").append(size)
                    .append("&sort=").append(sort)
                    .append("&direction=").append(direction);
                
                if (userId != null) redirectUrl.append("&userId=").append(userId);
                if (username != null && !username.trim().isEmpty()) redirectUrl.append("&username=").append(username.trim());
                if (activityType != null && !activityType.trim().isEmpty()) redirectUrl.append("&activityType=").append(activityType.trim());
                if (entityType != null && !entityType.trim().isEmpty()) redirectUrl.append("&entityType=").append(entityType.trim());
                if (entityId != null) redirectUrl.append("&entityId=").append(entityId);
                if (startTime != null) redirectUrl.append("&startTime=").append(startTime.toString());
                if (endTime != null) redirectUrl.append("&endTime=").append(endTime.toString());
                
                return "redirect:" + redirectUrl.toString();
            }
            
            // 格式化每个日志的时间戳
            formatLogTimestamps(logPage.getContent());

            model.addAttribute("logPage", logPage);
            model.addAttribute("activeMenu", "activitylogs");
            model.addAttribute("activityTypes", UserActivityLog.ActivityType.values());
            model.addAttribute("isSearch", true);
            
            // 回显搜索条件
            model.addAttribute("userId", userId);
            model.addAttribute("username", username);
            model.addAttribute("activityType", activityType);
            model.addAttribute("entityType", entityType);
            model.addAttribute("entityId", entityId);
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            
            // 获取所有用户列表，用于筛选
            List<User> users = userService.findAllUsers();
            model.addAttribute("users", users);
            
            // 常用实体类型列表
            model.addAttribute("entityTypes", Arrays.asList("Project", "User", "Task", "SubTask", "Message", "Submit2"));
            
            // 添加分页相关的属性
            model.addAttribute("currentPage", page);
            model.addAttribute("pageSize", size);
            model.addAttribute("totalPages", logPage.getTotalPages());
            model.addAttribute("totalItems", logPage.getTotalElements());
            model.addAttribute("sortField", sort);
            model.addAttribute("sortDirection", direction);
            
            return "admin/activity-logs/list";
        } catch (Exception e) {
            logger.error("搜索活动日志时发生错误", e);
            model.addAttribute("error", "搜索活动日志时出现系统错误，请稍后再试或联系管理员");
            
            // 添加最小必要数据，确保页面能够加载
            model.addAttribute("logPage", Page.empty());
            model.addAttribute("activeMenu", "activitylogs");
            model.addAttribute("activityTypes", UserActivityLog.ActivityType.values());
            model.addAttribute("isSearch", true);
            model.addAttribute("users", userService.findAllUsers());
            model.addAttribute("entityTypes", Arrays.asList("Project", "User", "Task", "SubTask", "Message", "Submit2"));
            model.addAttribute("currentPage", 0);
            model.addAttribute("pageSize", 20);
            model.addAttribute("totalPages", 0);
            model.addAttribute("totalItems", 0);
            model.addAttribute("sortField", sort);
            model.addAttribute("sortDirection", direction);
            
            // 回显搜索条件
            model.addAttribute("userId", userId);
            model.addAttribute("username", username);
            model.addAttribute("activityType", activityType);
            model.addAttribute("entityType", entityType);
            model.addAttribute("entityId", entityId);
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            
            return "admin/activity-logs/list";
        }
    }
    
    /**
     * 格式化日志时间戳的辅助方法
     */
    private void formatLogTimestamps(List<UserActivityLog> logs) {
        logs.forEach(log -> {
            // 我们现在使用 createdDate 字段，无需额外格式化
            // 但为了保持兼容性，我们设置 formattedTimestamp
            if (log.getCreatedDate() != null) {
                log.setFormattedTimestamp(log.getCreatedDate());
            } else {
                // 如果 createdDate 为空但 timestamp 可以获取到值（通过兼容方法）
                try {
                    LocalDateTime timestamp = log.getTimestamp();
                    if (timestamp != null) {
                        log.setFormattedTimestamp(timestamp.format(DATE_FORMATTER));
                    } else {
                        log.setFormattedTimestamp("未知时间");
                    }
                } catch (Exception e) {
                    logger.warn("格式化日志时间戳失败: {}", e.getMessage());
                    log.setFormattedTimestamp("日期格式错误");
                }
            }
        });
    }
    
    /**
     * 查看日志详情
     */
    @GetMapping("/{id}")
    public String viewLog(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        logger.info("查看日志详情，ID: {}", id);
        
        return logService.findLogById(id)
            .map(log -> {
                model.addAttribute("log", log);
                model.addAttribute("activeMenu", "activitylogs");
                
                // 尝试查找关联的用户
                userService.findUserById(log.getUserId()).ifPresent(user -> 
                    model.addAttribute("user", user)
                );
                
                return "admin/activity-logs/view";
            })
            .orElseGet(() -> {
                redirectAttributes.addFlashAttribute("error", "日志不存在");
                return "redirect:/admin/activity-logs";
            });
    }
    
    /**
     * 清理日志
     */
    @PostMapping("/clean")
    public String cleanLogs(
            @RequestParam("days") Integer days,
            RedirectAttributes redirectAttributes) {
        
        logger.info("清理 {} 天之前的用户活动日志", days);
        
        if (days == null || days <= 0) {
            redirectAttributes.addFlashAttribute("error", "请指定有效的天数");
            return "redirect:/admin/activity-logs";
        }
        
        try {
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
            long deletedCount = logService.deleteLogsBefore(beforeTime);
            
            redirectAttributes.addFlashAttribute("message", String.format("成功清理 %d 条 %d 天之前的日志", deletedCount, days));
        } catch (Exception e) {
            logger.error("清理日志时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "清理日志时出错: " + e.getMessage());
        }
        
        return "redirect:/admin/activity-logs";
    }
}