<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('进度跟踪报表')}">
    <meta charset="UTF-8">
    <title>进度跟踪报表</title>
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">进度跟踪报表</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToPDF()">导出PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">导出Excel</button>
                        </div>
                        <a th:href="@{/reports}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">筛选条件</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/reports/progress-tracking}" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 图表展示区域 -->
                <div class="row">
                    <!-- 项目整体进度 -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">项目整体进度</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="projectProgressChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 任务完成率对比 -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">任务完成率对比</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="taskCompletionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 进度延迟任务 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">进度延迟任务</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning" th:if="${#lists.isEmpty(delayedTasks)}">
                            <i class="bi bi-info-circle me-2"></i>当前没有进度延迟的任务
                        </div>
                        <div class="table-responsive" th:if="${not #lists.isEmpty(delayedTasks)}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>任务名称</th>
                                        <th>负责人</th>
                                        <th>开始时间</th>
                                        <th>已延迟天数</th>
                                        <th>完成率</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${delayedTasks}">
                                        <td th:text="${task.taskName}">任务名称</td>
                                        <td th:text="${task.responsible}">负责人</td>
                                        <td th:text="${#temporals.format(task.actualStartDateTime, 'yyyy-MM-dd')}">开始时间</td>
                                        <td>
                                            <span class="badge bg-danger"
                                                  th:text="${T(java.time.temporal.ChronoUnit).DAYS.between(task.actualStartDateTime, T(java.time.LocalDateTime).now()) + '天'}">
                                                30天
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                     th:style="'width: ' + ${task.ratio != null ? task.ratio * 100 : 0} + '%'"
                                                     th:text="${#numbers.formatPercent(task.ratio != null ? task.ratio : 0, 0, 0)}">
                                                    0%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a th:href="@{/tasks/edit/{id}(id=${task.taskId})}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
    </div>
    <script th:inline="javascript">
        // 从后端获取数据
        const projectProgressData = /*[[${projectProgressData}]]*/ {};
        const taskCompletionData = /*[[${taskCompletionData}]]*/ {};

        // 项目整体进度图表
        const projectProgressCtx = document.getElementById('projectProgressChart').getContext('2d');
        new Chart(projectProgressCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(projectProgressData),
                datasets: [{
                    label: '项目进度',
                    data: Object.values(projectProgressData).map(value => value * 100),
                    backgroundColor: '#0d6efd'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '项目整体进度 (%)'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y.toFixed(1) + '%';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });

        // 任务完成率对比图表
        const taskCompletionCtx = document.getElementById('taskCompletionChart').getContext('2d');

        // 限制显示的任务数量，避免图表过于拥挤
        const taskNames = Object.keys(taskCompletionData);
        const taskValues = Object.values(taskCompletionData);

        // 如果任务数量超过10个，只显示前10个
        const maxTasksToShow = 10;
        let displayTaskNames = taskNames;
        let displayTaskValues = taskValues;

        if (taskNames.length > maxTasksToShow) {
            displayTaskNames = taskNames.slice(0, maxTasksToShow);
            displayTaskValues = taskValues.slice(0, maxTasksToShow);
        }

        new Chart(taskCompletionCtx, {
            type: 'bar',
            data: {
                labels: displayTaskNames,
                datasets: [{
                    label: '完成率',
                    data: displayTaskValues.map(value => value * 100),
                    backgroundColor: '#20c997'
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '任务完成率 (%)'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.x.toFixed(1) + '%';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });

        // 导出PDF函数
        function exportToPDF() {
            alert('导出PDF功能将在后续实现');
        }

        // 导出Excel函数
        function exportToExcel() {
            alert('导出Excel功能将在后续实现');
        }
    </script>
</body>
</html>