<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程模板管理')}">
    <meta charset="UTF-8">
    <title>流程模板管理</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">流程模板管理</h1>
            <div>
                <a th:href="@{/workflow}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                <a th:href="@{/workflow/templates/create}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> 创建流程模板
                </a>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">搜索条件</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <form th:action="@{/workflow/templates}" method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="keyword" class="form-label">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" th:value="${keyword}" placeholder="模板名称、标题或描述">
                                </div>
                                <div class="col-md-3">
                                    <label for="scope" class="form-label">适用范围</label>
                                    <select class="form-select" id="scope" name="scope">
                                        <option value="">全部</option>
                                        <option value="项目" th:selected="${scope == '项目'}">项目</option>
                                        <option value="任务" th:selected="${scope == '任务'}">任务</option>
                                        <option value="通用" th:selected="${scope == '通用'}">通用</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="enabled" class="form-label">状态</label>
                                    <select class="form-select" id="enabled" name="enabled">
                                        <option value="">全部</option>
                                        <option value="true" th:selected="${enabled != null && enabled}">启用</option>
                                        <option value="false" th:selected="${enabled != null && !enabled}">禁用</option>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流程模板列表 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">流程模板列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 成功/错误消息 -->
                        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <span th:text="${message}">操作成功</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                        <!-- 模板列表表格 -->
                        <div class="table-responsive" th:if="${allTemplates != null && !allTemplates.isEmpty()}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>模板名称</th>
                                        <th>模板标题</th>
                                        <th>适用范围</th>
                                        <th>状态</th>
                                        <th>创建人</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 直接使用allTemplates列表 -->
                                    <tr th:each="template : ${allTemplates}">
                                        <td th:text="${template.templateName}">模板名称</td>
                                        <!-- 修改这里，将templateCode改为templateTitle -->
                                        <td th:text="${template.templateTitle}">模板标题</td>
                                        <td th:text="${template.applicableScope}">适用范围</td>
                                        <td>
                                            <span th:if="${template.enabled}" class="badge bg-success">启用</span>
                                            <span th:unless="${template.enabled}" class="badge bg-secondary">禁用</span>
                                        </td>
                                        <td th:text="${template.createdBy}">创建人</td>
                                        <td th:text="${template.createdDateTime != null ? #temporals.format(template.createdDateTime, 'yyyy-MM-dd') : '-'}">创建时间</td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:href="@{/workflow/steps/template/{id}(id=${template.templateId})}" class="btn btn-sm btn-outline-primary" title="管理步骤">
                                                    <i class="bi bi-list-ol"></i>
                                                </a>
                                                <a th:href="@{/workflow/templates/{id}/edit(id=${template.templateId})}" class="btn btn-sm btn-outline-secondary" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a th:href="@{/workflow/templates/{id}/toggle-status(id=${template.templateId})}" class="btn btn-sm btn-outline-success" title="切换状态">
                                                    <i class="bi" th:classappend="${template.enabled ? 'bi-toggle-on' : 'bi-toggle-off'}"></i>
                                                </a>
                                                <a th:href="@{/workflow/templates/delete(templateId=${template.templateId})}" class="btn btn-sm btn-outline-danger" title="删除"
                                                   onclick="return confirm('确定要删除流程模板 \'' + [[${template.templateName}]] + '\' 吗？此操作不可逆，删除后将无法恢复。')">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 备用：使用templatePage.content -->
                        <div class="table-responsive" th:if="${allTemplates == null && templatePage != null && !templatePage.empty}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>模板名称</th>
                                        <th>模板标题</th>
                                        <th>适用范围</th>
                                        <th>状态</th>
                                        <th>创建人</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="template : ${templatePage.content}">
                                        <td th:text="${template.templateName}">模板名称</td>
                                        <!-- 修改这里，将templateCode改为templateTitle -->
                                        <td th:text="${template.templateTitle}">模板标题</td>
                                        <td th:text="${template.applicableScope}">适用范围</td>
                                        <td>
                                            <span th:if="${template.enabled}" class="badge bg-success">启用</span>
                                            <span th:unless="${template.enabled}" class="badge bg-secondary">禁用</span>
                                        </td>
                                        <td th:text="${template.createdBy}">创建人</td>
                                        <td th:text="${template.createdDateTime != null ? #temporals.format(template.createdDateTime, 'yyyy-MM-dd') : '-'}">创建时间</td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:href="@{/workflow/steps/template/{id}(id=${template.templateId})}" class="btn btn-sm btn-outline-primary" title="管理步骤">
                                                    <i class="bi bi-list-ol"></i>
                                                </a>
                                                <a th:href="@{/workflow/templates/{id}/edit(id=${template.templateId})}" class="btn btn-sm btn-outline-secondary" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a th:href="@{/workflow/templates/{id}/toggle-status(id=${template.templateId})}" class="btn btn-sm btn-outline-success" title="切换状态">
                                                    <i class="bi" th:classappend="${template.enabled ? 'bi-toggle-on' : 'bi-toggle-off'}"></i>
                                                </a>
                                                <a th:href="@{/workflow/templates/delete(templateId=${template.templateId})}" class="btn btn-sm btn-outline-danger" title="删除"
                                                   onclick="return confirm('确定要删除流程模板 \'' + [[${template.templateName}]] + '\' 吗？此操作不可逆，删除后将无法恢复。')">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="text-center py-3" th:if="${(allTemplates == null || allTemplates.isEmpty()) && (templatePage == null || templatePage.empty)}">
                            <p class="text-muted mb-0">暂无流程模板</p>
                        </div>
                    </div>
                    <!-- 分页控件 -->
                    <div class="card-footer" th:if="${templatePage != null && templatePage.totalPages > 0}">
                        <div th:replace="~{fragments/pagination :: pagination(${templatePage}, @{/workflow/templates})}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除流程模板 "<span id="deleteTemplateName"></span>" 吗？</p>
                    <p class="text-danger">此操作不可逆，删除后将无法恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" th:action="@{/workflow/templates/delete}" method="post">
                        <input type="hidden" id="deleteTemplateId" name="templateId">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态切换确认模态框 -->
    <div class="modal fade" id="toggleStatusModal" tabindex="-1" aria-labelledby="toggleStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="toggleStatusModalLabel">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要<span id="toggleAction"></span>流程模板 "<span id="toggleTemplateName"></span>" 吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="toggleStatusForm" method="post">
                        <input type="hidden" name="_csrf" th:value="${_csrf.token}" />
                        <button type="submit" class="btn btn-primary">确认</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function confirmDelete(templateId, templateName) {
            document.getElementById('deleteTemplateId').value = templateId;
            document.getElementById('deleteTemplateName').textContent = templateName;

            // 显示模态框
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        function toggleStatus(templateId, templateName, currentStatus) {
            document.getElementById('toggleTemplateName').textContent = templateName;
            document.getElementById('toggleAction').textContent = currentStatus ? '禁用' : '启用';

            // 设置表单提交地址
            document.getElementById('toggleStatusForm').action = /*[[@{/}]]*/ '' + 'workflow/templates/' + templateId + '/toggle-status';

            // 显示模态框
            var toggleStatusModal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
            toggleStatusModal.show();
        }
    </script>
</body>
</html>
