package com.mylog.controller;

import com.mylog.model.ProjectTask;
import com.mylog.service.TaskService;
import com.mylog.service.ProjectService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class TaskControllerTest {

    @Mock
    private TaskService taskService;

    @Mock
    private ProjectService projectService;

    @Mock
    private RedirectAttributes redirectAttributes;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    private TaskController taskController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        taskController = new TaskController();
        // Use reflection to set the private fields
        try {
            java.lang.reflect.Field taskServiceField = TaskController.class.getDeclaredField("taskService");
            taskServiceField.setAccessible(true);
            taskServiceField.set(taskController, taskService);

            java.lang.reflect.Field projectServiceField = TaskController.class.getDeclaredField("projectService");
            projectServiceField.setAccessible(true);
            projectServiceField.set(taskController, projectService);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");
        
        Collection<GrantedAuthority> authorities = Collections.singletonList(new SimpleGrantedAuthority("ROLE_ADMIN"));
        when(authentication.getAuthorities()).thenReturn((Collection) authorities);
    }

    @Test
    void testSaveTask_WithNullResponsible_ShouldPreserveExistingResponsible() {
        // Arrange
        ProjectTask existingTask = new ProjectTask();
        existingTask.setTaskId(1L);
        existingTask.setResponsible("原负责人");
        existingTask.setStatus("进行中");
        existingTask.setCreatedDate("2024-01-01 10:00:00");
        existingTask.setCreatedBy("creator");

        ProjectTask taskToSave = new ProjectTask();
        taskToSave.setTaskId(1L);
        taskToSave.setResponsible(null); // This simulates the null responsible issue
        taskToSave.setStatus("进行中");
        taskToSave.setProgress(50);

        when(taskService.findTaskById(1L)).thenReturn(Optional.of(existingTask));
        when(taskService.saveTask(any(ProjectTask.class))).thenReturn(taskToSave);

        // Act
        String result = taskController.saveTask(taskToSave, redirectAttributes, null);

        // Assert
        assertEquals("原负责人", taskToSave.getResponsible(), "Responsible should be preserved from existing task");
        verify(taskService).saveTask(taskToSave);
    }

    @Test
    void testSaveTask_WithEmptyResponsible_ShouldPreserveExistingResponsible() {
        // Arrange
        ProjectTask existingTask = new ProjectTask();
        existingTask.setTaskId(1L);
        existingTask.setResponsible("原负责人");
        existingTask.setStatus("进行中");
        existingTask.setCreatedDate("2024-01-01 10:00:00");
        existingTask.setCreatedBy("creator");

        ProjectTask taskToSave = new ProjectTask();
        taskToSave.setTaskId(1L);
        taskToSave.setResponsible("   "); // This simulates empty/whitespace responsible
        taskToSave.setStatus("进行中");
        taskToSave.setProgress(50);

        when(taskService.findTaskById(1L)).thenReturn(Optional.of(existingTask));
        when(taskService.saveTask(any(ProjectTask.class))).thenReturn(taskToSave);

        // Act
        String result = taskController.saveTask(taskToSave, redirectAttributes, null);

        // Assert
        assertEquals("原负责人", taskToSave.getResponsible(), "Responsible should be preserved from existing task");
        verify(taskService).saveTask(taskToSave);
    }

    @Test
    void testSaveTask_NewTaskWithNullResponsible_ShouldReturnError() {
        // Arrange
        ProjectTask taskToSave = new ProjectTask();
        taskToSave.setTaskId(null); // New task
        taskToSave.setResponsible(null);
        taskToSave.setStatus("未开始");
        taskToSave.setProgress(0);

        // Act
        String result = taskController.saveTask(taskToSave, redirectAttributes, null);

        // Assert
        assertTrue(result.contains("redirect:"), "Should redirect due to validation error");
        verify(redirectAttributes).addFlashAttribute(eq("error"), contains("责任人不能为空"));
        verify(taskService, never()).saveTask(any(ProjectTask.class));
    }

    @Test
    void testSaveTask_WithValidResponsible_ShouldSaveSuccessfully() {
        // Arrange
        ProjectTask existingTask = new ProjectTask();
        existingTask.setTaskId(1L);
        existingTask.setResponsible("原负责人");
        existingTask.setStatus("进行中");
        existingTask.setCreatedDate("2024-01-01 10:00:00");
        existingTask.setCreatedBy("creator");

        ProjectTask taskToSave = new ProjectTask();
        taskToSave.setTaskId(1L);
        taskToSave.setResponsible("新负责人");
        taskToSave.setStatus("进行中");
        taskToSave.setProgress(50);

        when(taskService.findTaskById(1L)).thenReturn(Optional.of(existingTask));
        when(taskService.saveTask(any(ProjectTask.class))).thenReturn(taskToSave);

        // Act
        String result = taskController.saveTask(taskToSave, redirectAttributes, null);

        // Assert
        assertEquals("新负责人", taskToSave.getResponsible(), "Responsible should be updated to new value");
        verify(taskService).saveTask(taskToSave);
    }
}
