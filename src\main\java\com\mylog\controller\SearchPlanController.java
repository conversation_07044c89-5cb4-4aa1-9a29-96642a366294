package com.mylog.controller;

import com.mylog.model.SearchPlan;
import com.mylog.service.SearchPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping(value = "/api/search-plans", produces = "application/json;charset=UTF-8")
public class SearchPlanController {
    
    private static final Logger logger = LoggerFactory.getLogger(SearchPlanController.class);
    
    @Autowired
    private SearchPlanService searchPlanService;
    
    /**
     * 保存搜索方案
     */
    @PostMapping
    public ResponseEntity<?> saveSearchPlan(@RequestBody Map<String, Object> payload) {
        try {
            // 获取当前用户
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String username = auth.getName();
            
            logger.info("用户 {} 尝试保存搜索方案", username);
            
            String name = (String) payload.get("name");
            String description = (String) payload.get("description");
            String conditions = (String) payload.get("conditions");
            
            if (name == null || name.trim().isEmpty()) {
                logger.warn("保存搜索方案失败: 方案名称为空");
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "方案名称不能为空"));
            }
            
            if (conditions == null || conditions.trim().isEmpty()) {
                logger.warn("保存搜索方案失败: 搜索条件为空");
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "搜索条件不能为空"));
            }
            
            // 检查方案名称是否已存在
            List<SearchPlan> existingPlans = searchPlanService.getSearchPlansByUsername(username)
                    .stream()
                    .filter(plan -> plan.getName().equals(name))
                    .toList();
                    
            boolean exists = !existingPlans.isEmpty();
            SearchPlan searchPlan;
            
            if (exists) {
                // 更新已有方案
                searchPlan = existingPlans.get(0);
                searchPlan.setDescription(description);
                searchPlan.setConditions(conditions);
                searchPlan.setCreatedDateTime(LocalDateTime.now());
                logger.info("更新已有搜索方案: {}", name);
            } else {
                // 创建新方案
                searchPlan = new SearchPlan();
                searchPlan.setName(name);
                searchPlan.setDescription(description);
                searchPlan.setConditions(conditions);
                searchPlan.setUsername(username);
                searchPlan.setCreatedDateTime(LocalDateTime.now());
                logger.info("创建新搜索方案: {}", name);
            }
            
            SearchPlan savedPlan = searchPlanService.saveSearchPlan(searchPlan);
            logger.info("搜索方案已保存: {}, ID: {}", savedPlan.getName(), savedPlan.getPlanId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", exists ? "搜索方案已更新" : "搜索方案已保存");
            response.put("plan", savedPlan);
            
            // 设置响应头，明确指定返回JSON
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("保存搜索方案失败", e);
            // 同样设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "保存失败：" + e.getMessage());
            
            return new ResponseEntity<>(errorResponse, headers, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取当前用户的所有搜索方案
     */
    @GetMapping
    public ResponseEntity<?> getUserSearchPlans() {
        try {
            // 获取当前用户
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String username = auth.getName();
            
            List<SearchPlan> plans = searchPlanService.getSearchPlansByUsername(username);
            
            // 设置响应头，明确指定返回JSON
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("plans", plans);
            
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("获取搜索方案失败", e);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取失败：" + e.getMessage());
            
            return new ResponseEntity<>(errorResponse, headers, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取指定ID的搜索方案
     */
    @GetMapping("/{planId}")
    public ResponseEntity<?> getSearchPlan(@PathVariable Long planId) {
        try {
            // 获取当前用户
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String username = auth.getName();
            
            Optional<SearchPlan> planOpt = searchPlanService.getSearchPlanById(planId);
            
            // 设置响应头，明确指定返回JSON
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            if (planOpt.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "未找到该搜索方案");
                return new ResponseEntity<>(errorResponse, headers, HttpStatus.NOT_FOUND);
            }
            
            SearchPlan plan = planOpt.get();
            
            // 检查是否是当前用户的搜索方案
            if (!plan.getUsername().equals(username)) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "无权访问该搜索方案");
                return new ResponseEntity<>(errorResponse, headers, HttpStatus.FORBIDDEN);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("plan", plan);
            
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("获取搜索方案失败", e);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取失败：" + e.getMessage());
            
            return new ResponseEntity<>(errorResponse, headers, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 删除搜索方案
     */
    @DeleteMapping("/{planId}")
    public ResponseEntity<?> deleteSearchPlan(@PathVariable Long planId) {
        try {
            // 获取当前用户
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String username = auth.getName();
            
            Optional<SearchPlan> planOpt = searchPlanService.getSearchPlanById(planId);
            
            // 设置响应头，明确指定返回JSON
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            if (planOpt.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "未找到该搜索方案");
                return new ResponseEntity<>(errorResponse, headers, HttpStatus.NOT_FOUND);
            }
            
            SearchPlan plan = planOpt.get();
            
            // 检查是否是当前用户的搜索方案
            if (!plan.getUsername().equals(username)) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "无权删除该搜索方案");
                return new ResponseEntity<>(errorResponse, headers, HttpStatus.FORBIDDEN);
            }
            
            searchPlanService.deleteSearchPlan(planId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "搜索方案已删除");
            
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("删除搜索方案失败", e);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除失败：" + e.getMessage());
            
            return new ResponseEntity<>(errorResponse, headers, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
} 