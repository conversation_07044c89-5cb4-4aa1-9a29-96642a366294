package com.mylog.service.impl;

import com.mylog.model.WorkHoursLog;
import com.mylog.repository.WorkHoursLogRepository;
import com.mylog.service.WorkHoursLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * 工期登记服务实现类
 */
@Service
@Transactional
public class WorkHoursLogServiceImpl implements WorkHoursLogService {

    private static final Logger logger = LoggerFactory.getLogger(WorkHoursLogServiceImpl.class);

    @Autowired
    private WorkHoursLogRepository workHoursLogRepository;

    @Override
    public WorkHoursLog saveWorkHoursLog(WorkHoursLog workHoursLog) {
        try {
            logger.info("保存工期记录: {}", workHoursLog);
            return workHoursLogRepository.save(workHoursLog);
        } catch (Exception e) {
            logger.error("保存工期记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存工期记录失败", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkHoursLog> findWorkHoursLogById(Long id) {
        return workHoursLogRepository.findById(id);
    }

    @Override
    public void deleteWorkHoursLog(Long id) {
        try {
            logger.info("删除工期记录，ID: {}", id);
            workHoursLogRepository.deleteById(id);
        } catch (Exception e) {
            logger.error("删除工期记录失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            throw new RuntimeException("删除工期记录失败", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkHoursLog> findAllWorkHoursLogs() {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        return workHoursLogRepository.findAll(sort);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkHoursLog> findAllWorkHoursLogs(Pageable pageable) {
        // 确保按创建时间降序排序
        if (pageable.getSort().isUnsorted()) {
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "createdTime"));
        }
        return workHoursLogRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkHoursLog> findWorkHoursLogsByBusinessType(String businessType) {
        return workHoursLogRepository.findByBusinessTypeOrderByCreatedTimeDesc(businessType);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkHoursLog> findWorkHoursLogsByBusinessType(String businessType, Pageable pageable) {
        return workHoursLogRepository.findByBusinessTypeOrderByCreatedTimeDesc(businessType, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkHoursLog> findWorkHoursLogsByBusinessId(Integer businessId) {
        return workHoursLogRepository.findByBusinessIdOrderByCreatedTimeDesc(businessId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkHoursLog> findWorkHoursLogsByBusinessTypeAndBusinessId(String businessType, Integer businessId) {
        return workHoursLogRepository.findByBusinessTypeAndBusinessIdOrderByCreatedTimeDesc(businessType, businessId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkHoursLog> findWorkHoursLogsByBusinessTypeAndBusinessId(String businessType, Integer businessId,
            Pageable pageable) {
        return workHoursLogRepository.findByBusinessTypeAndBusinessIdOrderByCreatedTimeDesc(businessType, businessId,
                pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkHoursLog> findWorkHoursLogsByTimeRange(String startTime, String endTime) {
        return workHoursLogRepository.findByCreatedTimeRangeOrderByCreatedTimeDesc(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkHoursLog> findWorkHoursLogsByTimeRange(String startTime, String endTime, Pageable pageable) {
        return workHoursLogRepository.findByCreatedTimeRangeOrderByCreatedTimeDesc(startTime, endTime, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkHoursLog> findWorkHoursLogsByBusinessTypeAndTimeRange(String businessType, String startTime,
            String endTime) {
        return workHoursLogRepository.findByBusinessTypeAndCreatedTimeRangeOrderByCreatedTimeDesc(businessType,
                startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countWorkHoursLogsByBusinessTypeAndBusinessId(String businessType, Integer businessId) {
        return workHoursLogRepository.countByBusinessTypeAndBusinessId(businessType, businessId);
    }

    @Override
    @Transactional(readOnly = true)
    public Double getLatestHoursInventory(String businessType, Integer businessId) {
        Double inventory = workHoursLogRepository.getLatestHoursInventory(businessType, businessId);
        return inventory != null ? inventory : 0.0;
    }

    @Override
    @Transactional(readOnly = true)
    public Double calculateTotalHoursChange(String businessType, Integer businessId) {
        return workHoursLogRepository.calculateTotalHoursChange(businessType, businessId);
    }

    @Override
    public WorkHoursLog addWorkHours(String businessType, Integer businessId, Double hoursChange, Double daysRated,
            String reason, String creator, String remark, String responsiblePerson) {
        try {
            // 获取当前的工期存量
            Double currentInventory = getLatestHoursInventory(businessType, businessId);

            // 计算新的存量
            Double newInventory = currentInventory + hoursChange;

            // 按公式计算绩效分：(额定工期*3 - 实际工期) * 50
            double computedBonus = ((daysRated != null ? daysRated : 0.0) * 3
                    - (newInventory != null ? newInventory : 0.0)) * 50.0;
            Double bonus = BigDecimal.valueOf(computedBonus).setScale(2, RoundingMode.HALF_UP).doubleValue();

            // 创建新的工期记录（包含所有字段和责任人）
            WorkHoursLog workHoursLog = new WorkHoursLog(businessType, businessId, newInventory, hoursChange, daysRated,
                    reason, creator, remark, bonus, responsiblePerson);

            logger.info("添加工期记录: 业务类型={}, 业务ID={}, 存量从{}变为{}, 变化={}, 额定天数={}, 原因={}, 创建人={}, 备注={}, 责任人={}, 绩效分={}",
                    businessType, businessId, currentInventory, newInventory, hoursChange, daysRated, reason, creator,
                    remark, responsiblePerson, bonus);

            return saveWorkHoursLog(workHoursLog);
        } catch (Exception e) {
            logger.error("添加工期记录失败: 业务类型={}, 业务ID={}, 工期变化={}, 额定天数={}, 原因={}, 创建人={}, 备注={}, 责任人={}, 错误: {}",
                    businessType, businessId, hoursChange, daysRated, reason, creator, remark, responsiblePerson,
                    e.getMessage(), e);
            throw new RuntimeException("添加工期记录失败", e);
        }
    }

    @Override
    public WorkHoursLog addWorkHours(String businessType, Integer businessId, Double hoursChange, Double daysRated,
            String reason, String creator, String remark, Double bonus, String responsiblePerson) {
        try {
            // 获取当前的工期存量
            Double currentInventory = getLatestHoursInventory(businessType, businessId);

            // 计算新的存量
            Double newInventory = currentInventory + hoursChange;

            // 如果外部未提供bonus或提供为null，则按统一公式计算
            Double finalBonus = bonus;
            if (finalBonus == null) {
                double computed = ((daysRated != null ? daysRated : 0.0) * 3
                        - (newInventory != null ? newInventory : 0.0)) * 50.0;
                finalBonus = BigDecimal.valueOf(computed).setScale(2, RoundingMode.HALF_UP).doubleValue();
            } else {
                // 规范化保留两位小数
                finalBonus = BigDecimal.valueOf(finalBonus).setScale(2, RoundingMode.HALF_UP).doubleValue();
            }

            // 创建新的工期记录（包含指定的奖金或计算后的奖金）
            WorkHoursLog workHoursLog = new WorkHoursLog(businessType, businessId, newInventory, hoursChange, daysRated,
                    reason, creator, remark, finalBonus, responsiblePerson);

            logger.info("添加工期记录(含奖金): 业务类型={}, 业务ID={}, 存量从{}变为{}, 变化={}, 额定天数={}, 原因={}, 创建人={}, 备注={}, 奖金={}, 责任人={}",
                    businessType, businessId, currentInventory, newInventory, hoursChange, daysRated, reason, creator,
                    remark, finalBonus, responsiblePerson);

            return saveWorkHoursLog(workHoursLog);
        } catch (Exception e) {
            logger.error(
                    "添加工期记录失败(含奖金): 业务类型={}, 业务ID={}, 工期变化={}, 额定天数={}, 原因={}, 创建人={}, 备注={}, 奖金={}, 责任人={}, 错误: {}",
                    businessType, businessId, hoursChange, daysRated, reason, creator, remark, bonus, responsiblePerson,
                    e.getMessage(), e);
            throw new RuntimeException("添加工期记录失败", e);
        }
    }

    @Override
    public void logProjectRatedDurationChange(Long projectId, java.math.BigDecimal oldRatedDuration,
            java.math.BigDecimal newRatedDuration, String username, String responsiblePerson) {
        try {
            Double oldDays = oldRatedDuration != null ? oldRatedDuration.doubleValue() : 0.0;
            Double newDays = newRatedDuration != null ? newRatedDuration.doubleValue() : 0.0;
            Double daysChange = newDays - oldDays;

            String oldRatedDaysStr = oldRatedDuration != null ? String.format("%.2f天", oldRatedDuration.doubleValue())
                    : "未设置";
            String newRatedDaysStr = newRatedDuration != null ? String.format("%.2f天", newRatedDuration.doubleValue())
                    : "未设置";

            String reason = String.format("项目额定工期从%s变更为%s", oldRatedDaysStr, newRatedDaysStr);
            String remark = String.format("项目额定工期变更");

            addWorkHours(
                    "项目",
                    projectId.intValue(),
                    0.0,
                    newDays,
                    reason,
                    username,
                    remark,
                    responsiblePerson != null ? responsiblePerson : username);

            logger.info("已记录项目 {} 的额定工期变更: 从 {} 变更为 {}, 变化 {} 天",
                    projectId, oldRatedDaysStr, newRatedDaysStr, daysChange);
        } catch (Exception e) {
            logger.error(
                    "记录项目额定工期变更日志失败: projectId={}, oldRatedDuration={}, newRatedDuration={}, username={}, responsiblePerson={}, 错误: {}",
                    projectId, oldRatedDuration, newRatedDuration, username, responsiblePerson, e.getMessage(), e);
            throw new RuntimeException("记录项目额定工期变更日志失败", e);
        }
    }

    @Override
    public void logProjectStatusChange(Long projectId, String oldStatus, String newStatus,
            java.math.BigDecimal projectDurationDays, java.math.BigDecimal ratedDurationDays, String username) {
        try {
            // 检查是否是需要记录工期的状态变更
            boolean shouldLogWorkHours = false;
            String reason = "";
            Double hoursChange = 0.0;

            // 检查是否从"未开始"或"进行中"变更到"已取消"、"已暂停"或"已完成"
            if (("未开始".equals(oldStatus) || "进行中".equals(oldStatus)) &&
                    ("已取消".equals(newStatus) || "已暂停".equals(newStatus) || "已完成".equals(newStatus))) {

                shouldLogWorkHours = true;

                // 如果项目有实际工期，使用实际工期作为工期变化
                if (projectDurationDays != null && projectDurationDays.compareTo(java.math.BigDecimal.ZERO) > 0) {
                    hoursChange = projectDurationDays.doubleValue();
                    reason = String.format("项目状态从[%s]变更为[%s]，记录实际工期", oldStatus, newStatus);
                } else if (ratedDurationDays != null && ratedDurationDays.compareTo(java.math.BigDecimal.ZERO) > 0) {
                    // 如果没有实际工期但有额定工期，使用额定工期
                    hoursChange = 0.0; // 记录0工期
                    reason = String.format("项目状态从[%s]变更为[%s]，使用额定工期记录", oldStatus, newStatus);
                } else {
                    // 如果都没有，记录0工期，但仍然记录状态变更
                    hoursChange = 0.0;
                    reason = String.format("项目状态从[%s]变更为[%s]，无实际工期和额定工期", oldStatus, newStatus);
                }
            }

            if (shouldLogWorkHours) {
                Double ratedDays = ratedDurationDays != null ? ratedDurationDays.doubleValue() : 0.0;
                String remark = String.format("项目状态变更：%s → %s", oldStatus, newStatus);
                addWorkHours(
                        "项目",
                        projectId.intValue(),
                        hoursChange,
                        ratedDays,
                        reason,
                        username,
                        remark,
                        username);

                logger.info("已记录项目 {} 的状态变更工期: 从[{}]变更为[{}]，工期变化 {} 天",
                        projectId, oldStatus, newStatus, hoursChange);
            } else {
                logger.info("项目 {} 状态从[{}]变更为[{}]，不需要记录工期", projectId, oldStatus, newStatus);
            }
        } catch (Exception e) {
            logger.error("记录项目状态变更工期日志失败: projectId={}, oldStatus={}, newStatus={}, username={}, 错误: {}",
                    projectId, oldStatus, newStatus, username, e.getMessage(), e);
            throw new RuntimeException("记录项目状态变更工期日志失败", e);
        }
    }
}
