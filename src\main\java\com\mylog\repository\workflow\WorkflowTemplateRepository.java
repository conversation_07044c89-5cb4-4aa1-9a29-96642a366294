package com.mylog.repository.workflow;

import com.mylog.model.workflow.WorkflowTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 流程模板数据访问接口
 */
@Repository
public interface WorkflowTemplateRepository extends JpaRepository<WorkflowTemplate, Long> {

    /**
     * 根据模板标题查找模板
     */
    Optional<WorkflowTemplate> findByTemplateTitle(String templateTitle);

    /**
     * 根据模板名称和标题查找模板
     */
    List<WorkflowTemplate> findByTemplateNameAndTemplateTitle(String templateName, String templateTitle);

    /**
     * 根据适用范围查找模板
     */
    List<WorkflowTemplate> findByApplicableScope(String applicableScope);

    /**
     * 根据启用状态查找模板
     */
    List<WorkflowTemplate> findByEnabled(Boolean enabled);

    /**
     * 根据适用范围和启用状态查找模板
     */
    List<WorkflowTemplate> findByApplicableScopeAndEnabled(String applicableScope, Boolean enabled);

    /**
     * 搜索模板
     */
    @Query("SELECT t FROM WorkflowTemplate t WHERE " +
           "(:keyword IS NULL OR t.templateName LIKE %:keyword% OR t.templateTitle LIKE %:keyword% OR t.description LIKE %:keyword%) AND " +
           "(:scope IS NULL OR t.applicableScope = :scope) AND " +
           "(:enabled IS NULL OR t.enabled = :enabled)")
    Page<WorkflowTemplate> searchTemplates(
        @Param("keyword") String keyword, 
        @Param("scope") String applicableScope,
        @Param("enabled") Boolean enabled, 
        Pageable pageable
    );

    /**
     * 使用原生SQL查询所有模板
     */
    @Query(value = "SELECT * FROM workflow_templates", nativeQuery = true)
    List<WorkflowTemplate> findAllByNativeQuery();

    /**
     * 查询当前最大的模板ID
     */
    @Query(value = "SELECT MAX(template_id) FROM workflow_templates", nativeQuery = true)
    Long findMaxTemplateId();
}
