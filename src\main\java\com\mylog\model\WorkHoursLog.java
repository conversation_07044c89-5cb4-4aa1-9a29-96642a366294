package com.mylog.model;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 工期登记实体类
 * 用于记录工期的变化和存量信息
 */
@Entity
@Table(name = "work_days_log")
@Data
public class WorkHoursLog {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 业务类型
     */
    @Column(name = "business_type", nullable = false)
    private String businessType;

    /**
     * 业务ID（整数）
     */
    @Column(name = "business_id", nullable = false)
    private Integer businessId;    /**
     * 累计工期（double）
     */
    @Column(name = "days_actual", nullable = false)
    private Double daysActual;

    /**
     * 工期变化（double）
     */
    @Column(name = "days_change", nullable = false)
    private Double daysChange;

    /**
     * 额定天数（double）
     */
    @Column(name = "days_rated", nullable = false)
    private Double daysRated;

    /**
     * 原因（字符串）
     */
    @Column(name = "reason", nullable = false, columnDefinition = "TEXT")
    private String reason;

    /**
     * 创建时间（字符串格式为2025-03-03 08:07:05）
     */
    @Column(name = "created_time", nullable = false, columnDefinition = "TEXT")
    private String createdTime;    /**
     * 创建人（字符串）
     */
    @Column(name = "creator", columnDefinition = "TEXT")
    private String creator;

    /**
     * 责任人（字符串）
     */
    @Column(name = "responsible_person", columnDefinition = "TEXT")
    private String responsiblePerson;

    /**
     * 备注（字符串）
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    /**
     * 奖金（double）
     */
    @Column(name = "bonus")
    private Double bonus;

    /**
     * 默认构造函数
     */
    public WorkHoursLog() {
        this.createdTime = LocalDateTime.now().format(DATE_FORMATTER);
    }
    
    /**
     * 构造函数
     */
    public WorkHoursLog(String businessType, Integer businessId, Double daysActual,
                       Double daysChange, String reason) {
        this();
        this.businessType = businessType;
        this.businessId = businessId;
        this.daysActual = daysActual;
        this.daysChange = daysChange;
        this.reason = reason;
    }

    /**
     * 包含额定天数的构造函数
     */
    public WorkHoursLog(String businessType, Integer businessId, Double daysActual,
                       Double daysChange, Double daysRated, String reason) {
        this();
        this.businessType = businessType;
        this.businessId = businessId;
        this.daysActual = daysActual;
        this.daysChange = daysChange;
        this.daysRated = daysRated;
        this.reason = reason;
    }    /**
     * 完整构造函数（包含创建人和备注）
     */
    public WorkHoursLog(String businessType, Integer businessId, Double daysActual,
                       Double daysChange, String reason, String creator, String remark) {
        this();
        this.businessType = businessType;
        this.businessId = businessId;
        this.daysActual = daysActual;
        this.daysChange = daysChange;
        this.reason = reason;
        this.creator = creator;
        this.remark = remark;
    }  
      /**
    /**
     * 包含奖金的完整构造函数
     */
    public WorkHoursLog(String businessType, Integer businessId, Double daysActual,
                       Double daysChange, Double daysRated, String reason, String creator, String remark, Double bonus, String responsiblePerson) {
        this();
        this.businessType = businessType;
        this.businessId = businessId;
        this.daysActual = daysActual;
        this.daysChange = daysChange;
        this.daysRated = daysRated;
        this.reason = reason;
        this.creator = creator;
        this.remark = remark;
        this.bonus = bonus;
        this.responsiblePerson = responsiblePerson;
    }
    

    /**
     * 持久化前的回调，设置创建时间
     */
    @PrePersist
    protected void onCreate() {
        if (this.createdTime == null) {
            this.createdTime = LocalDateTime.now().format(DATE_FORMATTER);
        }
    }

    // 添加别名getter方法，兼容模板和Repository中的字段名
    /**
     * 获取累计工期 - 别名方法
     * @return 累计工期
     */
    public Double getHoursInventory() {
        return this.daysActual;
    }

    /**
     * 设置累计工期 - 别名方法
     * @param hoursInventory 累计工期
     */
    public void setHoursInventory(Double hoursInventory) {
        this.daysActual = hoursInventory;
    }

    /**
     * 获取工期变化 - 别名方法
     * @return 工期变化
     */
    public Double getHoursChange() {
        return this.daysChange;
    }

    /**
     * 设置工期变化 - 别名方法
     * @param hoursChange 工期变化
     */
    public void setHoursChange(Double hoursChange) {
        this.daysChange = hoursChange;
    }    @Override
    public String toString() {
        return "WorkHoursLog{" +
                "id=" + id +
                ", businessType='" + businessType + '\'' +
                ", businessId=" + businessId +
                ", daysActual=" + daysActual +
                ", daysChange=" + daysChange +
                ", daysRated=" + daysRated +
                ", reason='" + reason + '\'' +
                ", createdTime='" + createdTime + '\'' +
                ", creator='" + creator + '\'' +
                ", responsiblePerson='" + responsiblePerson + '\'' +
                ", remark='" + remark + '\'' +
                ", bonus=" + bonus +
                '}';
    }
}
