package com.mylog.model.user;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 用户实体类，只映射到UserManagement数据库
 */
@Entity
@Table(name = "Users")
@Data
public class User {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public enum UserRole {
        OPERATOR,
        MANAGER,
        ADMIN
    }

    public enum ThemeStyle {
        DEFAULT,
        DARK,
        LIGHT,
        BLUE
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userId;

    @Column(nullable = false, unique = true)
    private String username;

    @Column
    private String weixinID;

    @Column(name = "webhook_url")
    private String webhookUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=afd06d9c-9713-47f2-972d-b1b1eb473744";

    @Column(nullable = false)
    private String password;

    @Enumerated(EnumType.ORDINAL)
    @Column(nullable = false)
    private UserRole role = UserRole.OPERATOR;

    @Enumerated(EnumType.STRING)
    @Column(name = "theme_style")
    private ThemeStyle themeStyle = ThemeStyle.DEFAULT;

    @Column(name = "CreatedDate")
    private String createdDate;

    @Column(name = "last_modified_time")
    private String lastModifiedTimeStr;

    @Transient
    private LocalDateTime lastModifiedTime;

    public LocalDateTime getCreatedDate() {
        if (createdDate != null) {
            try {
                return LocalDateTime.parse(createdDate, DATE_FORMATTER);
            } catch (Exception e) {
                // 如果解析失败，返回null
                return null;
            }
        }
        return null;
    }

    public void setCreatedDate(LocalDateTime date) {
        if (date != null) {
            this.createdDate = date.format(DATE_FORMATTER);
        } else {
            this.createdDate = null;
        }
    }

    public String getCreatedDateStr() {
        return this.createdDate;
    }

    public void setCreatedDateStr(String dateStr) {
        this.createdDate = dateStr;
    }

    public LocalDateTime getLastModifiedTime() {
        if (lastModifiedTime == null && lastModifiedTimeStr != null) {
            try {
                lastModifiedTime = LocalDateTime.parse(lastModifiedTimeStr, DATE_FORMATTER);
            } catch (Exception e) {
                return null;
            }
        }
        return lastModifiedTime;
    }

    public void setLastModifiedTime(LocalDateTime date) {
        this.lastModifiedTime = date;
        if (date != null) {
            this.lastModifiedTimeStr = date.format(DATE_FORMATTER);
        }
    }

    // 权限检查方法
    public boolean canEditProjects() {
        return role == UserRole.ADMIN || role == UserRole.MANAGER;
    }

    public boolean canDeleteProjects() {
        return role == UserRole.ADMIN || role == UserRole.MANAGER;
    }

    public boolean canExportProjects() {
        return role == UserRole.ADMIN || role == UserRole.MANAGER;
    }

    public boolean canManageUsers() {
        return role == UserRole.ADMIN;
    }

    public boolean canEditLogs() {
        return role == UserRole.ADMIN || role == UserRole.MANAGER;
    }

    public boolean canDeleteLogs() {
        return role == UserRole.ADMIN;
    }

    public boolean canExportLogs() {
        return role == UserRole.ADMIN || role == UserRole.MANAGER;
    }
}