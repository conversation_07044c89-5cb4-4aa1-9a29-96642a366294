package com.mylog.repository.workflow;

import com.mylog.model.workflow.WorkflowStep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 流程步骤存储库接口
 */
@Repository
public interface WorkflowStepRepository extends JpaRepository<WorkflowStep, Long> {

    /**
     * 根据模板ID查找步骤列表，按步骤顺序排序
     */
    List<WorkflowStep> findByTemplateTemplateIdOrderByStepOrderAsc(Long templateId);

    /**
     * 根据模板ID查找所有步骤（使用JPQL查询）
     * 使用JOIN FETCH解决懒加载问题
     */
    @Query("SELECT DISTINCT s FROM WorkflowStep s JOIN FETCH s.template t WHERE t.templateId = :templateId ORDER BY s.stepOrder ASC")
    List<WorkflowStep> findAllStepsByTemplateId(@Param("templateId") Long templateId);

    /**
     * 查找模板的下一个步骤
     */
    @Query("SELECT s FROM WorkflowStep s WHERE s.template.templateId = :templateId AND s.stepOrder > :currentOrder ORDER BY s.stepOrder ASC")
    List<WorkflowStep> findNextSteps(@Param("templateId") Long templateId, @Param("currentOrder") Integer currentOrder);

    /**
     * 查找模板的上一个步骤
     */
    @Query("SELECT s FROM WorkflowStep s WHERE s.template.templateId = :templateId AND s.stepOrder < :currentOrder ORDER BY s.stepOrder DESC")
    List<WorkflowStep> findPreviousSteps(@Param("templateId") Long templateId, @Param("currentOrder") Integer currentOrder);

    /**
     * 查找模板的第一个步骤
     */
    @Query("SELECT s FROM WorkflowStep s WHERE s.template.templateId = :templateId ORDER BY s.stepOrder ASC")
    List<WorkflowStep> findFirstStep(@Param("templateId") Long templateId);

    /**
     * 查找模板中最大的步骤顺序
     */
    @Query("SELECT MAX(s.stepOrder) FROM WorkflowStep s WHERE s.template.templateId = :templateId")
    Integer findMaxStepOrder(@Param("templateId") Long templateId);
}
