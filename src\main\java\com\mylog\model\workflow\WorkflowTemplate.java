package com.mylog.model.workflow;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 流程模板实体类
 * 定义流程的基本信息和步骤
 */
@Entity
@Table(name = "workflow_templates")
@Data
public class WorkflowTemplate implements Serializable {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowTemplate.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final ZoneId SYSTEM_ZONE = ZoneId.systemDefault();
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "template_id") // 修改为下划线命名法
    private Long templateId;
    
    /**
     * 流程模板名称
     */
    @Column(name = "templateName", nullable = false, length = 100)
    private String templateName;
    
    /**
     * 流程模板标题
     */
    @Column(name = "templateTitle", nullable = false, length = 50)
    private String templateTitle;
    
    /**
     * 流程模板描述
     */
    @Column(name = "description")
    private String description;
    
    /**
     * 适用范围，如：项目、任务等
     */
    @Column(name = "applicableScope", length = 50)
    private String applicableScope;
    
    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;
    
    /**
     * 创建时间 - 变更为字符串格式存储
     */
    @Column(name = "createdDate", nullable = false)
    private String createdDate;
    
    /**
     * 最后修改时间 - 变更为字符串格式存储
     */
    @Column(name = "lastModifiedDate")
    private String lastModifiedDate;
    
    /**
     * 创建人
     */
    @Column(name = "createdBy", length = 50)
    private String createdBy;
    
    /**
     * 最后修改人
     */
    @Column(name = "lastModifiedBy", length = 50)
    private String lastModifiedBy;
    
    /**
     * 流程步骤列表
     */
    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("stepOrder ASC")
    private List<WorkflowStep> steps = new ArrayList<>();
    
    /**
     * 流程实例列表
     */
    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<WorkflowInstance> instances = new ArrayList<>();
    
    /**
     * 将LocalDateTime格式化为标准日期时间字符串：yyyy-MM-dd HH:mm:ss
     */
    public String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        try {
            return dateTime.format(DATE_FORMATTER);
        } catch (Exception e) {
            logger.error("格式化日期时间出错: {}", e.getMessage(), e);
            return LocalDateTime.now().format(DATE_FORMATTER); // 默认返回当前时间的格式化字符串
        }
    }
    
    /**
     * 解析标准日期时间字符串为LocalDateTime
     */
    public LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.error("解析日期时间字符串失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 设置创建时间
     */
    public void setCreatedDateTime(LocalDateTime dateTime) {
        if (dateTime != null) {
            try {
                this.createdDate = formatDateTime(dateTime);
            } catch (Exception e) {
                logger.error("设置创建时间时出错: {}", e.getMessage(), e);
                // 出错时使用当前时间
                this.createdDate = formatDateTime(LocalDateTime.now());
            }
        }
    }
    
    /**
     * 获取创建时间
     */
    public LocalDateTime getCreatedDateTime() {
        try {
            if (this.createdDate != null) {
                return parseDateTime(this.createdDate);
            }
            return null;
        } catch (Exception e) {
            logger.error("获取创建时间时出错: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 设置最后修改时间
     */
    public void setLastModifiedDateTime(LocalDateTime dateTime) {
        if (dateTime != null) {
            try {
                this.lastModifiedDate = formatDateTime(dateTime);
            } catch (Exception e) {
                logger.error("设置最后修改时间时出错: {}", e.getMessage(), e);
                // 出错时使用当前时间
                this.lastModifiedDate = formatDateTime(LocalDateTime.now());
            }
        }
    }
    
    /**
     * 获取最后修改时间
     */
    public LocalDateTime getLastModifiedDateTime() {
        try {
            if (this.lastModifiedDate != null) {
                return parseDateTime(this.lastModifiedDate);
            }
            return null;
        } catch (Exception e) {
            logger.error("获取最后修改时间时出错: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取创建时间（兼容性方法）
     * 该方法提供与getCreatedDateTime()相同的功能，但使用不同的命名约定
     * @return 创建时间的LocalDateTime对象
     */
    public LocalDateTime getCreatedAt() {
        return getCreatedDateTime();
    }
    
    /**
     * 确保创建时间有值
     */
    @PrePersist
    public void prePersist() {
        if (this.createdDate == null) {
            this.createdDate = formatDateTime(LocalDateTime.now());
        }
    }
}
