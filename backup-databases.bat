@echo off
chcp 65001 >nul

:: 设置脚本目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

:: 设置源目录和目标目录
set SOURCE_DIR=%SCRIPT_DIR%data
set BACKUP_DIR=%SCRIPT_DIR%dataBk

:: 确保备份目录存在
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%"
)

:: 获取当前日期时间 (格式: YYYYMMDD_HHMMSS)
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do if not "%%I"=="" set datetime=%%I
set DATE_SUFFIX=%datetime:~0,8%_%datetime:~8,6%

:: 获取当前时间用于日志
set TIMESTAMP=%datetime:~0,4%-%datetime:~4,2%-%datetime:~6,2% %datetime:~8,2%:%datetime:~10,2%:%datetime:~12,2%

:: 日志文件
set LOG_FILE=%BACKUP_DIR%\backup.log

echo [%TIMESTAMP%] 开始数据库备份任务 >> "%LOG_FILE%"

:: 备份 UserManagement.db
if exist "%SOURCE_DIR%\UserManagement.db" (
    copy "%SOURCE_DIR%\UserManagement.db" "%BACKUP_DIR%\UserManagement_%DATE_SUFFIX%.db" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [%TIMESTAMP%] 成功备份 UserManagement.db 到 UserManagement_%DATE_SUFFIX%.db >> "%LOG_FILE%"
    ) else (
        echo [%TIMESTAMP%] 错误: 备份 UserManagement.db 失败 >> "%LOG_FILE%"
    )
) else (
    echo [%TIMESTAMP%] 警告: UserManagement.db 文件不存在 >> "%LOG_FILE%"
)

:: 备份 LogManagement.db
if exist "%SOURCE_DIR%\LogManagement.db" (
    copy "%SOURCE_DIR%\LogManagement.db" "%BACKUP_DIR%\LogManagement_%DATE_SUFFIX%.db" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [%TIMESTAMP%] 成功备份 LogManagement.db 到 LogManagement_%DATE_SUFFIX%.db >> "%LOG_FILE%"
    ) else (
        echo [%TIMESTAMP%] 错误: 备份 LogManagement.db 失败 >> "%LOG_FILE%"
    )
) else (
    echo [%TIMESTAMP%] 警告: LogManagement.db 文件不存在 >> "%LOG_FILE%"
)

:: 清理超过30天的备份文件
echo [%TIMESTAMP%] 开始清理超过30天的备份文件 >> "%LOG_FILE%"
forfiles /p "%BACKUP_DIR%" /s /m *.db /d -30 /c "cmd /c del @path && echo [%TIMESTAMP%] 删除过期备份文件: @file" 2>nul >> "%LOG_FILE%"

echo [%TIMESTAMP%] 数据库备份任务完成 >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"
