package com.mylog.config;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.time.ZoneId;
import java.util.TimeZone;

/**
 * 时区配置类
 * 确保应用启动时设置正确的时区为Asia/Shanghai (UTC+8)
 */
@Configuration
public class TimezoneConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(TimezoneConfig.class);
    
    @PostConstruct
    public void init() {
        // 设置JVM默认时区为中国时区 (UTC+8)
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        
        // 记录时区设置
        logger.info("应用时区已设置为: {} ({})", 
                    TimeZone.getDefault().getID(), 
                    ZoneId.systemDefault());
        logger.info("当前时区偏移量: {} 小时", 
                    TimeZone.getDefault().getRawOffset() / (1000 * 60 * 60));
    }
}