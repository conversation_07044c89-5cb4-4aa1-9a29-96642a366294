package com.mylog.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 处理异步响应过程中的异常
 */
@ControllerAdvice
public class AsyncResponseExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(AsyncResponseExceptionHandler.class);
    
    @ExceptionHandler(AsyncRequestTimeoutException.class)
    public void handleAsyncRequestTimeoutException(AsyncRequestTimeoutException ex, HttpServletResponse response) {
        logger.error("异步请求超时: {}", ex.getMessage());
        response.setStatus(HttpServletResponse.SC_REQUEST_TIMEOUT);
    }
}
