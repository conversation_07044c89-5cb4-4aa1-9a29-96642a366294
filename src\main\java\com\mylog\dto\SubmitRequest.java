package com.mylog.dto;

/**
 * 任务提交请求DTO
 */
public class SubmitRequest {

    /**
     * 默认构造函数
     */
    public SubmitRequest() {
        // 默认构造函数
    }
    private Long taskId;
    private String remarks;
    private String submitName;
    private String tempFilePath1;
    private String tempFilePath2;
    private String fileName1;
    private String fileName2;
    private Boolean needApproval;
    private String taskCompletionStatus; // 新增字段：任务完成状态 ("completed" 或 "paused")

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSubmitName() {
        return submitName;
    }

    public void setSubmitName(String submitName) {
        this.submitName = submitName;
    }

    public String getTempFilePath1() {
        return tempFilePath1;
    }

    public void setTempFilePath1(String tempFilePath1) {
        this.tempFilePath1 = tempFilePath1;
    }

    public String getTempFilePath2() {
        return tempFilePath2;
    }

    public void setTempFilePath2(String tempFilePath2) {
        this.tempFilePath2 = tempFilePath2;
    }

    public String getFileName1() {
        return fileName1;
    }

    public void setFileName1(String fileName1) {
        this.fileName1 = fileName1;
    }

    public String getFileName2() {
        return fileName2;
    }

    public void setFileName2(String fileName2) {
        this.fileName2 = fileName2;
    }

    public Boolean getNeedApproval() {
        return needApproval;
    }

    public void setNeedApproval(Boolean needApproval) {
        this.needApproval = needApproval;
    }

    public String getTaskCompletionStatus() {
        return taskCompletionStatus;
    }

    public void setTaskCompletionStatus(String taskCompletionStatus) {
        this.taskCompletionStatus = taskCompletionStatus;
    }

    @Override
    public String toString() {
        return "SubmitRequest{" +
                "taskId=" + taskId +
                ", remarks='" + remarks + '\'' +
                ", submitName='" + submitName + '\'' +
                ", tempFilePath1='" + tempFilePath1 + '\'' +
                ", tempFilePath2='" + tempFilePath2 + '\'' +
                ", fileName1='" + fileName1 + '\'' +
                ", fileName2='" + fileName2 + '\'' +
                ", needApproval=" + needApproval +
                '}';
    }
}
