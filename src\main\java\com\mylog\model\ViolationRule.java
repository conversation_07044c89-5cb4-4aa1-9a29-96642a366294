package com.mylog.model;

import jakarta.persistence.*;
import lombok.Data;

/**
 * 违规任务规则实体类
 * 用于存储违规任务的筛选条件
 */
@Entity
@Table(name = "violation_rules")
@Data
public class ViolationRule {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long ruleId;

    /**
     * 规则名称
     */
    @Column(nullable = false, length = 100)
    private String ruleName;

    /**
     * 规则描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 任务状态条件
     */
    @Column(length = 50)
    private String taskStatus;

    /**
     * 任务名称包含条件（用于排除）
     * 多个条件用逗号分隔
     */
    @Column(columnDefinition = "TEXT")
    private String taskNameExcludes;

    /**
     * 任务名称包含条件（用于包含）
     * 多个条件用逗号分隔
     */
    @Column(columnDefinition = "TEXT")
    private String taskNameIncludes;

    /**
     * 评论天数阈值
     */
    @Column
    private Double commentDaysThreshold;

    /**
     * 特殊任务评论天数阈值
     */
    @Column
    private Double specialTaskCommentDaysThreshold;

    /**
     * 特殊任务标识（任务名称包含的字符串）
     */
    @Column(length = 50)
    private String specialTaskIdentifier;

    /**
     * 是否启用
     */
    @Column
    private Boolean enabled = true;

    /**
     * 规则优先级（数字越小优先级越高）
     */
    @Column
    private Integer priority = 0;

    /**
     * 违规扣分（违规时扣除的分数）
     */
    @Column
    private Integer score = 0;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    private java.time.LocalDateTime createdDateTime;

    /**
     * 最后修改时间
     */
    @Column
    private java.time.LocalDateTime lastModifiedDateTime;

    /**
     * 创建人
     */
    @Column(length = 50)
    private String createdBy;

    /**
     * 最后修改人
     */
    @Column(length = 50)
    private String lastModifiedBy;

    /**
     * 默认构造函数
     */
    public ViolationRule() {
        this.createdDateTime = java.time.LocalDateTime.now();
    }
}
