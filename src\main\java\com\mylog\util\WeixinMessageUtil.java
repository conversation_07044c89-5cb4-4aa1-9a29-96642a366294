package com.mylog.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.mylog.model.user.User;
import com.mylog.service.UserService;

/**
 * 企业微信消息工具类
 * 用于发送企业微信机器人消息
 */
@Component
public class WeixinMessageUtil {

    private static final Logger log = LoggerFactory.getLogger(WeixinMessageUtil.class);

    private final RestTemplate restTemplate;
    private final UserService userService;

    // 用于查找webhook URL的目标用户名
    private static final String TARGET_USERNAME = "姚强";

    public WeixinMessageUtil(RestTemplate restTemplate, UserService userService) {
        this.restTemplate = restTemplate;
        this.userService = userService;
    }

    /**
     * 获取企业微信 Webhook URL
     * 从数据库中查找用户 "姚强" 的 webhook_url 字段
     * 
     * @return webhook URL，如果未找到或为空则返回 null
     */
    private String getWebhookUrl() {
        try {
            Optional<User> userOptional = userService.findUserByUsername(TARGET_USERNAME);
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                String webhookUrl = user.getWebhookUrl();
                if (webhookUrl != null && !webhookUrl.trim().isEmpty()) {
                    log.debug("成功获取用户 {} 的 webhook URL", TARGET_USERNAME);
                    return webhookUrl.trim();
                } else {
                    log.warn("用户 {} 的 webhook_url 字段为空", TARGET_USERNAME);
                }
            } else {
                log.warn("未找到用户: {}", TARGET_USERNAME);
            }
        } catch (Exception e) {
            log.error("获取用户 {} 的 webhook URL 失败: {}", TARGET_USERNAME, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 发送企业微信机器人消息（Markdown格式）
     *
     * @param title         消息标题，会在消息内容前显示
     * @param content       消息内容，支持Markdown格式
     * @param mentionedList 需要@的用户ID列表，可以传null或空列表
     */
    public void sendWeixinMessage(String title, String content, List<String> mentionedList) {
        sendWeixinMessage(title, content, mentionedList, null);
    }

    /**
     * 发送企业微信机器人消息（Markdown格式）
     *
     * @param title              消息标题，会在消息内容前显示
     * @param content            消息内容，支持Markdown格式
     * @param mentionedList      需要@的用户ID列表，可以传null或空列表
     * @param mentionedMobileList 需要@的手机号码列表，可以传null或空列表
     */
    public void sendWeixinMessage(String title, String content, List<String> mentionedList, List<String> mentionedMobileList) {
        try {
            // 获取动态的 webhook URL
            String webhookUrl = getWebhookUrl();
            if (webhookUrl == null) {
                log.error("无法获取有效的 webhook URL，消息发送失败");
                return;
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> markdown = new HashMap<>();

            // 如果有标题，将标题和内容组合成Markdown格式
            String messageContent = (title != null && !title.isEmpty())
                    ? "### " + title + "\n" + content
                    : content;

            // 将消息内容转换为UTF-8编码
            try {
                byte[] utf8Bytes = messageContent.getBytes("UTF-8");
                String utf8MessageContent = new String(utf8Bytes, "UTF-8");
                markdown.put("content", utf8MessageContent);
                log.debug("消息内容已转换为UTF-8编码");
            } catch (Exception e) {
                log.warn("转换消息内容到UTF-8编码失败", e);
                // 如果转换失败，使用原始值
                markdown.put("content", messageContent);
            }

            // 如果有需要@的用户ID，添加到mentioned_list
            if (mentionedList != null && !mentionedList.isEmpty()) {
                // 将用户ID列表转换为UTF-8编码
                List<String> utf8MentionedList = new ArrayList<>();
                for (String userId : mentionedList) {
                    try {
                        // 对于@all特殊处理，保持原样
                        if ("@all".equals(userId)) {
                            utf8MentionedList.add(userId);
                        } else {
                            // 先转换为字节数组，再转回字符串，确保UTF-8编码
                            byte[] utf8Bytes = userId.getBytes("UTF-8");
                            String utf8String = new String(utf8Bytes, "UTF-8");
                            utf8MentionedList.add(utf8String);
                        }
                    } catch (Exception e) {
                        log.warn("转换用户ID到UTF-8编码失败: {}", userId, e);
                        // 如果转换失败，使用原始值
                        utf8MentionedList.add(userId);
                    }
                }
                markdown.put("mentioned_list", utf8MentionedList);
                log.debug("添加UTF-8编码的@用户ID列表: {}", utf8MentionedList);
            }

            // 如果有需要@的手机号码，添加到mentioned_mobile_list
            if (mentionedMobileList != null && !mentionedMobileList.isEmpty()) {
                // 将手机号码列表转换为UTF-8编码
                List<String> utf8MentionedMobileList = new ArrayList<>();
                for (String mobile : mentionedMobileList) {
                    try {
                        // 对于@all特殊处理，保持原样
                        if ("@all".equals(mobile)) {
                            utf8MentionedMobileList.add(mobile);
                        } else {
                            // 先转换为字节数组，再转回字符串，确保UTF-8编码
                            byte[] utf8Bytes = mobile.getBytes("UTF-8");
                            String utf8String = new String(utf8Bytes, "UTF-8");
                            utf8MentionedMobileList.add(utf8String);
                        }
                    } catch (Exception e) {
                        log.warn("转换手机号码到UTF-8编码失败: {}", mobile, e);
                        // 如果转换失败，使用原始值
                        utf8MentionedMobileList.add(mobile);
                    }
                }
                markdown.put("mentioned_mobile_list", utf8MentionedMobileList);
                log.debug("添加UTF-8编码的@手机号码列表: {}", utf8MentionedMobileList);
            }

            // requestBody.put("msgtype", "text");
            // requestBody.put("text", markdown);
            requestBody.put("msgtype", "markdown");
            requestBody.put("markdown", markdown);

            // 创建 HTTP 请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            log.debug("发送企业微信消息: {}", messageContent);
            @SuppressWarnings("unchecked")
            Map<String, Object> response = restTemplate.postForObject(webhookUrl, requestEntity, Map.class);
            log.debug("企业微信响应: {}", response);

        } catch (Exception e) {
            log.error("发送企业微信消息失败: {}", e.getMessage(), e);
        }
    }
}
