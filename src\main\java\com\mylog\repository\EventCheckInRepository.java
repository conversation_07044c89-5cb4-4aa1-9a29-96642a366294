package com.mylog.repository;

import com.mylog.model.EventCheckIn;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 事件签到Repository接口
 */
@Repository
public interface EventCheckInRepository extends JpaRepository<EventCheckIn, Long> {
    
    /**
     * 根据事件提醒ID查找签到记录
     */
    List<EventCheckIn> findByEventReminderIdOrderByCheckInTimeDesc(Long eventReminderId);
    
    /**
     * 根据用户ID查找签到记录
     */
    List<EventCheckIn> findByUserIdOrderByCheckInTimeDesc(Long userId);
    
    /**
     * 根据事件提醒ID和用户ID查找签到记录
     */
    Optional<EventCheckIn> findByEventReminderIdAndUserId(Long eventReminderId, Long userId);
    
    /**
     * 检查用户是否已经为某个提醒签到
     */
    boolean existsByEventReminderIdAndUserId(Long eventReminderId, Long userId);
    
    /**
     * 根据时间范围查找签到记录
     */
    @Query("SELECT c FROM EventCheckIn c WHERE c.checkInTime >= :startTime AND c.checkInTime <= :endTime ORDER BY c.checkInTime DESC")
    List<EventCheckIn> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                      @Param("endTime") LocalDateTime endTime);
      /**
     * 根据用户ID和时间范围查找签到记录
     */
    @Query("SELECT c FROM EventCheckIn c WHERE c.userId = :userId AND c.checkInTime >= :startTime AND c.checkInTime <= :endTime ORDER BY c.checkInTime DESC")
    List<EventCheckIn> findByUserIdAndTimeRange(@Param("userId") Long userId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据用户ID和时间范围统计签到次数
     */
    @Query("SELECT COUNT(c) FROM EventCheckIn c WHERE c.userId = :userId AND c.checkInTime >= :startTime AND c.checkInTime <= :endTime")
    long countByUserIdAndTimeRange(@Param("userId") Long userId,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户的签到次数
     */
    long countByUserId(Long userId);
    
    /**
     * 统计某个提醒的签到次数
     */
    long countByEventReminderId(Long eventReminderId);
    
    /**
     * 获取最近的签到记录
     */
    @Query("SELECT c FROM EventCheckIn c WHERE c.userId = :userId ORDER BY c.checkInTime DESC")
    List<EventCheckIn> findRecentCheckInsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID和时间范围分页查询签到历史记录
     */
    @Query("SELECT c FROM EventCheckIn c WHERE c.userId = :userId " +
           "AND c.checkInTime >= :startTime AND c.checkInTime <= :endTime " +
           "ORDER BY c.checkInTime DESC")
    Page<EventCheckIn> findByUserIdAndTimeRangeOrderByCheckInTimeDesc(@Param("userId") Long userId,
                                                                     @Param("startTime") LocalDateTime startTime,
                                                                     @Param("endTime") LocalDateTime endTime,                                                                     Pageable pageable);
}
