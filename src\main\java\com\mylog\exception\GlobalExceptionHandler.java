package com.mylog.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.UnsatisfiedDependencyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理文件上传大小超出限制的异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        logger.warn("文件上传大小超出限制: {}", e.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "文件大小超出限制，最大允许50MB");
        response.put("error", "MaxUploadSizeExceededException");

        // 返回200状态码但带有错误信息，避免前端触发error事件
        return ResponseEntity.ok(response);
    }

    /**
     * 处理HTTP媒体类型不可接受的异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(HttpMediaTypeNotAcceptableException.class)
    public ResponseEntity<Map<String, Object>> handleHttpMediaTypeNotAcceptableException(HttpMediaTypeNotAcceptableException e) {
        logger.warn("媒体类型不可接受: {}", e.getMessage());
        
        // 获取更多详细信息用于诊断
        String supportedTypesStr = e.getSupportedMediaTypes() != null ? 
                e.getSupportedMediaTypes().toString() : "无支持的媒体类型";
                
        // 记录详细信息但不依赖于RequestContextHolder，避免可能的空指针异常
        try {
            var requestAttributes = org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
            if (requestAttributes != null) {
                String acceptHeader = ((org.springframework.web.context.request.ServletRequestAttributes) requestAttributes)
                        .getRequest().getHeader("Accept");
                logger.debug("请求的Accept头: {}，服务器支持: {}", acceptHeader, supportedTypesStr);
            } else {
                logger.debug("无法获取请求信息，服务器支持: {}", supportedTypesStr);
            }
        } catch (Exception ex) {
            logger.debug("获取请求信息异常: {}", ex.getMessage());
        }

        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "请求的媒体类型不可接受，服务器将以JSON格式返回数据");
        response.put("error", "HttpMediaTypeNotAcceptableException");
        if (e.getSupportedMediaTypes() != null) {
            response.put("supportedMediaTypes", e.getSupportedMediaTypes().toString());
        }
        
        // 强制使用APPLICATION_JSON_VALUE，并返回200状态码而非406
        // 这样可以确保客户端能正常接收响应，即使Accept头不匹配
        return ResponseEntity
            .status(HttpStatus.OK)  // 使用200状态码而不是406
            .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
            .body(response);
    }

    /**
     * 处理HTTP媒体类型不支持的异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<Map<String, Object>> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e) {
        logger.warn("媒体类型不支持: {}", e.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "请求的媒体类型不支持");
        response.put("error", "HttpMediaTypeNotSupportedException");
        response.put("contentType", e.getContentType());
        response.put("supportedMediaTypes", e.getSupportedMediaTypes());

        return ResponseEntity.ok(response);
    }

    /**
     * 处理HTTP消息不可读的异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<Map<String, Object>> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        logger.warn("消息不可读: {}", e.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "请求消息不可读，可能是JSON格式错误");
        response.put("error", "HttpMessageNotReadableException");
        response.put("details", e.getMessage());

        return ResponseEntity.ok(response);
    }

    /**
     * 处理Bean创建异常
     * 
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(BeanCreationException.class)
    public ResponseEntity<Map<String, Object>> handleBeanCreationException(BeanCreationException e) {
        logger.error("Bean创建异常", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "Bean创建失败: " + e.getBeanName());
        response.put("error", "BeanCreationException");
        response.put("details", e.getMessage());
        
        // 记录更详细的异常信息，包括根因
        Throwable rootCause = e.getRootCause();
        if (rootCause != null) {
            logger.error("根本原因: {}", rootCause.getMessage(), rootCause);
            response.put("rootCause", rootCause.getMessage());
        }
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理依赖注入不满足的异常
     * 
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(UnsatisfiedDependencyException.class)
    public ResponseEntity<Map<String, Object>> handleUnsatisfiedDependencyException(UnsatisfiedDependencyException e) {
        logger.error("依赖注入不满足异常", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "Bean依赖注入失败: " + e.getBeanName());
        response.put("error", "UnsatisfiedDependencyException");
        response.put("details", e.getMessage());
        
        // 记录更详细的异常信息，包括根因
        Throwable rootCause = e.getRootCause();
        if (rootCause != null) {
            logger.error("根本原因: {}", rootCause.getMessage(), rootCause);
            response.put("rootCause", rootCause.getMessage());
        }
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理静态资源未找到异常（通常是浏览器开发工具请求）
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<Map<String, Object>> handleNoResourceFoundException(NoResourceFoundException e) {
        // 对于Chrome DevTools和其他浏览器工具的请求，只记录debug级别日志
        String resourcePath = e.getResourcePath();
        if (resourcePath != null && (resourcePath.contains(".well-known") || 
                                   resourcePath.contains("devtools") || 
                                   resourcePath.contains("favicon"))) {
            logger.debug("浏览器工具请求不存在的资源: {}", resourcePath);
        } else {
            logger.warn("静态资源未找到: {}", resourcePath);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "请求的资源不存在");
        response.put("error", "NoResourceFoundException");
        response.put("resourcePath", resourcePath);

        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
            .body(response);
    }

    /**
     * 处理所有其他类型的异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        logger.error("未处理的异常", e);

        Map<String, Object> response = new HashMap<>();
        response.put("success", Boolean.FALSE);
        response.put("message", "服务器内部错误");
        response.put("error", e.getClass().getSimpleName());
        response.put("details", e.getMessage());

        // 强制指定内容类型为 JSON，并返回200状态码以避免进一步的内容协商问题
        return ResponseEntity
            .status(HttpStatus.OK)  // 使用200状态码，避免触发其他异常处理逻辑
            .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
            .body(response);
    }
}
