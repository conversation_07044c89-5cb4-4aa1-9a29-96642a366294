<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('我的难点焦点任务')}">
    <meta charset="UTF-8">
    <title>我的难点焦点任务</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">我的难点焦点任务 <small class="fs-6">（<span class="badge bg-primary rounded-pill" th:text="'进行中 ' + ${inProgressDifficultTaskCount}">进行中 23</span>/<span th:text="${taskPage.totalElements}">52</span>）</small></h1>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">任务列表</h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#taskListCollapse" aria-expanded="true" aria-controls="taskListCollapse">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="taskListCollapse">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%;">任务名称</th>
                                    <th style="width: 25%;">所属项目</th>
                                    <th style="width: 7%;">风险</th>
                                    <th style="width: 15%;">进度</th>
                                    <th style="width: 8%;">状态</th>
                                    <th style="width: 7%;">评论</th>
                                    <th style="width: 13%;">创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 无数据提示 -->
                                <tr th:if="${taskPage.content.empty}">
                                    <td colspan="7" class="text-center">暂无任务数据</td>
                                </tr>

                                <!-- 数据行 -->
                                <tr th:if="${!taskPage.content.empty}" th:each="task : ${taskPage.content}">
                                    <td>
                                        <a th:href="@{/tasks/{id}(id=${task.taskId})}" th:text="${task.taskName}">任务名称</a>
                                    </td>
                                    <td>
                                        <a th:if="${task.project != null}" th:href="@{/projects/{id}(id=${task.projectId})}" th:text="${task.project.projectName ?: '未命名项目'}">项目名称</a>
                                        <span th:if="${task.project == null && task.projectId != null}" class="text-danger">项目不存在</span>
                                        <span th:if="${task.projectId == null}" class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:class="${'badge ' +
                                            (task.risk == '高' ? 'bg-danger' :
                                            (task.risk == '中' ? 'bg-warning' : 'bg-success'))}"
                                            th:text="${task.risk}">风险</span>
                                    </td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar"
                                                 th:style="'width: ' + (${task.progressPercentage} ?: 0) + '%'"
                                                 th:class="${'progress-bar ' +
                                                    ((task.progressPercentage ?: 0) <= 30 ? 'bg-danger' :
                                                    ((task.progressPercentage ?: 0) <= 70 ? 'bg-warning' : 'bg-success'))}"
                                                 th:text="(${task.progressPercentage} ?: 0) + '%'">0%</div>
                                        </div>
                                    </td>
                                    <td>                                        <span th:class="${'badge ' +
                                            (task.status == '进行中' ? 'bg-primary' :
                                            (task.status == '已完成' ? 'bg-success' :
                                            (task.status == '未开始' ? 'bg-secondary' :
                                            (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                            th:text="${task.status}">状态</span>
                                    </td>
                                    <td>
                                        <span th:if="${task.commentDays != null}"
                                              th:class="${'badge ' +
                                                (task.commentDays >= 7 ? 'bg-danger' :
                                                (task.commentDays >= 4 ? 'bg-warning' :
                                                (task.commentDays >= 0 ? 'bg-success' : 'bg-dark')))}"
                                              th:text="${task.commentDays}">0.0</span>
                                        <span th:unless="${task.commentDays != null}">-</span>
                                    </td>
                                    <td th:text="${task.createdDateTime != null ? #temporals.format(task.createdDateTime, 'yyyy-MM-dd') : '-'}" style="white-space: nowrap;">2025-01-01</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- 分页控件 -->
                <div class="card-footer" th:if="${taskPage.totalPages > 0}">
                    <nav aria-label="Page navigation" class="d-flex justify-content-center">
                        <ul class="pagination mb-0">
                            <li class="page-item" th:classappend="${taskPage.first ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/tasks/difficult(page=0, size=${taskPage.size})}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:classappend="${taskPage.first ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/tasks/difficult(page=${taskPage.number - 1}, size=${taskPage.size})}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, taskPage.totalPages - 1)}"
                                th:classappend="${i == taskPage.number ? 'active' : ''}">
                                <a class="page-link" th:href="@{/tasks/difficult(page=${i}, size=${taskPage.size})}" th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item" th:classappend="${taskPage.last ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/tasks/difficult(page=${taskPage.number + 1}, size=${taskPage.size})}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:classappend="${taskPage.last ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/tasks/difficult(page=${taskPage.totalPages - 1}, size=${taskPage.size})}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>