package com.mylog.service;

import com.mylog.model.PersonnelStatus;
import com.mylog.model.ProjectTask;
import com.mylog.repository.PersonnelStatusRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class PersonnelStatusService {
    private static final Logger logger = LoggerFactory.getLogger(PersonnelStatusService.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private PersonnelStatusRepository personnelStatusRepository;

    @Autowired
    private OptionsService optionsService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private TaskService taskService;

    /**
     * 初始化人员状态数据
     * 从OptionsService获取所有人员，并为每个人员创建初始状态记录
     */
    @Transactional
    public void initializePersonnelStatus() {
        // 检查表是否已有数据
        long count = personnelStatusRepository.count();
        if (count > 0) {
            logger.info("人员状态表已存在数据，跳过初始化，当前记录数: {}", count);
            return;
        }

        // 从optionsService获取所有人员列表
        List<String> allPersonnel = optionsService.getPersonnel();
        logger.info("从OptionsService获取到人员数量: {}", allPersonnel.size());

        // 获取当前时间
        String currentTime = LocalDateTime.now().format(FORMATTER);

        // 为每个人员创建状态记录
        for (String name : allPersonnel) {
            PersonnelStatus status = new PersonnelStatus();
            status.setName(name);
            status.setStatus("厂内上班");
            status.setLocation("公司");
            status.setCreateTime(currentTime);
            status.setEffectiveTime(currentTime);

            personnelStatusRepository.save(status);
        }

        logger.info("成功初始化所有人员状态数据，总计: {}", allPersonnel.size());
    }

    /**
     * 获取所有人员状态
     */
    public List<PersonnelStatus> getAllPersonnelStatus() {
        List<PersonnelStatus> allStatus = personnelStatusRepository.findAll();

        // 为每条记录填充进行中的项目和任务数以及半年内新建和完成数据
        for (PersonnelStatus status : allStatus) {
            fillInProgressCounts(status);
            fillSixMonthCounts(status);
            fillCompletedSixMonthCounts(status);
        }

        return allStatus;
    }

    /**
     * 根据ID获取人员状态
     */
    public Optional<PersonnelStatus> getPersonnelStatusById(Long id) {
        return personnelStatusRepository.findById(id);
    }

    /**
     * 根据名称获取人员状态
     */
    public Optional<PersonnelStatus> getPersonnelStatusByName(String name) {
        return personnelStatusRepository.findByName(name);
    }

    /**
     * 保存或更新人员状态
     */
    @Transactional
    public PersonnelStatus saveOrUpdate(PersonnelStatus status) {
        // 设置当前时间为创建/更新时间
        status.setCreateTime(LocalDateTime.now().format(FORMATTER));
        return personnelStatusRepository.save(status);
    }

    /**
     * 更新人员状态信息
     */
    @Transactional
    public PersonnelStatus updatePersonnelStatus(Long id, String status, String location) {
        Optional<PersonnelStatus> statusOpt = personnelStatusRepository.findById(id);
        if (statusOpt.isPresent()) {
            PersonnelStatus personnelStatus = statusOpt.get();
            personnelStatus.setStatus(status);
            personnelStatus.setLocation(location);
            personnelStatus.setCreateTime(LocalDateTime.now().format(FORMATTER));
            personnelStatus.setEffectiveTime(LocalDateTime.now().format(FORMATTER));
            return personnelStatusRepository.save(personnelStatus);
        }
        return null;
    }

    /**
     * 获取所有不同的状态值
     */
    public List<String> getAllDistinctStatuses() {
        return personnelStatusRepository.findAllDistinctStatuses();
    }

    /**
     * 获取所有不同的地点值
     */
    public List<String> getAllDistinctLocations() {
        return personnelStatusRepository.findAllDistinctLocations();
    }

    /**
     * 根据流程实例的审批通过来更新人员状态
     * 
     * @param initiator          流程发起人姓名
     * @param templateName       流程模板名称
     * @param startLocation      出发地/开始位置
     * @param endLocation        目的地/结束位置
     * @param startTime          生效时间
     * @param endTime            结束时间
     * @param workflowInstanceId 流程实例ID
     */
    @Transactional
    public void updatePersonnelStatusAfterWorkflowApproved(String initiator, String templateName,
            String startLocation, String endLocation, String startTime, String endTime, Long workflowInstanceId) {
        logger.info("根据流程审批更新人员状态 - 发起人: {}, 模板: {}, 流程实例ID: {}", initiator, templateName, workflowInstanceId);

        // 获取当前时间作为创建时间
        String currentTime = LocalDateTime.now().format(FORMATTER);

        // 创建新的状态记录
        PersonnelStatus newStatus = new PersonnelStatus();
        newStatus.setName(initiator);
        newStatus.setCreateTime(currentTime);
        newStatus.setWorkflowInstanceId(workflowInstanceId);

        // 根据模板类型设置不同的状态和位置
        if (templateName != null && templateName.contains("换岗")) {
            // 换岗流程
            String status;
            if (endLocation != null && endLocation.contains("公司")) {
                status = "厂内上班";
            } else {
                status = "出差中";
            }

            newStatus.setStatus(status);
            newStatus.setLocation(endLocation);
            newStatus.setEffectiveTime(endTime != null ? endTime : currentTime);

            logger.info("换岗流程更新 - 人员: {}, 状态: {}, 位置: {}", initiator, status, endLocation);

        } else if (templateName != null && templateName.contains("请假")) {
            // 请假流程 - 第一条记录：请假中
            newStatus.setStatus("请假中");
            newStatus.setLocation(endLocation);
            newStatus.setEffectiveTime(startTime != null ? startTime : currentTime);

            personnelStatusRepository.save(newStatus);

            logger.info("请假流程更新 - 人员: {}, 状态: 请假中, 位置: {}", initiator, startLocation);            // 请假结束 - 第二条记录：根据endLocation判断状态
            PersonnelStatus endStatus = new PersonnelStatus();
            endStatus.setName(initiator);
            endStatus.setCreateTime(currentTime);
            endStatus.setWorkflowInstanceId(workflowInstanceId);
            
            // 根据endLocation判断状态
            String endStatusValue;
            if (endLocation != null && endLocation.contains("公司")) {
                endStatusValue = "厂内上班";
            } else {
                endStatusValue = "出差中";
            }
            
            endStatus.setStatus(endStatusValue);
            endStatus.setLocation(endLocation != null ? endLocation : startLocation);
            endStatus.setEffectiveTime(endTime != null ? endTime : currentTime);            
            personnelStatusRepository.save(endStatus);
            logger.info("请假结束更新 - 人员: {}, 状态: {}, 位置: {}", initiator, endStatusValue, endLocation != null ? endLocation : startLocation);
        } else {
            logger.info("模板名称 [{}] 不包含换岗或请假，不更新人员状态", templateName);
            return;
        }

        // 保存新状态
        personnelStatusRepository.save(newStatus);
        logger.info("成功添加新的人员状态记录");
    }

    /**
     * 获取有效的人员状态记录列表
     * 
     * 有效记录规则：
     * 1. 记录中的姓名必须属于optionsService.getPersonnel()中的人员
     * 2. 记录中的生效时间必须小于当前时间
     * 3. 有效记录中同姓名的记录只能出现最多一次
     * 
     * @param maxResults 最大返回记录数，默认为10
     * @return 符合条件的有效人员状态记录列表
     */
    public List<PersonnelStatus> getValidPersonnelStatusRecords(int maxResults) {
        logger.info("获取有效的人员状态记录，最大返回数量: {}", maxResults);

        // 获取所有人员列表
        List<String> validPersonnel = optionsService.getPersonnel();
        logger.debug("有效人员列表，总计: {}", validPersonnel.size());

        // 获取所有状态记录，按ID降序排序
        List<PersonnelStatus> allRecords = personnelStatusRepository.findAll()
                .stream()
                .sorted((a, b) -> b.getId().compareTo(a.getId())) // ID从大到小排序
                .collect(Collectors.toList());

        logger.debug("获取到的所有记录数: {}", allRecords.size());

        // 当前时间，用于比较生效时间
        String currentTime = LocalDateTime.now().format(FORMATTER);

        // 用于跟踪已处理过的人员名称
        Map<String, Boolean> processedNames = new HashMap<>();
        List<PersonnelStatus> validRecords = new ArrayList<>();

        // 过滤有效记录
        for (PersonnelStatus record : allRecords) {
            // 如果已达到最大返回数量，退出循环
            if (validRecords.size() >= maxResults) {
                break;
            }

            String name = record.getName();

            // 检查规则1：人员必须在有效名单中
            if (!validPersonnel.contains(name)) {
                logger.debug("记录ID: {}, 姓名: {} 不在有效人员列表中，跳过", record.getId(), name);
                continue;
            }

            // 检查规则3：同一人员只出现一次
            if (processedNames.containsKey(name)) {
                logger.debug("记录ID: {}, 姓名: {} 已经处理过，跳过", record.getId(), name);
                continue;
            }

            // 检查规则2：生效时间必须小于当前时间
            try {
                String effectiveTime = record.getEffectiveTime();
                if (effectiveTime != null && effectiveTime.compareTo(currentTime) > 0) {
                    logger.debug("记录ID: {}, 姓名: {} 生效时间: {} 大于当前时间: {}, 跳过",
                            record.getId(), name, effectiveTime, currentTime);
                    continue;
                } // 填充进行中的项目和任务数以及半年内新建数据
                fillInProgressCounts(record);
                fillSixMonthCounts(record);
                fillCompletedSixMonthCounts(record);

                // 记录符合所有条件，添加到有效记录列表
                validRecords.add(record);
                processedNames.put(name, true);
                logger.debug(
                        "记录ID: {}, 姓名: {} 添加到有效记录列表, 进行中项目数: {}, 进行中任务数: {}, 半年内新建项目数: {}, 半年内新建任务数: {}, 半年内完成项目数: {}, 半年内完成任务数: {}",
                        record.getId(), name, record.getInProgressProjectCount(), record.getInProgressTaskCount(),
                        record.getNewProjectsInSixMonths(), record.getNewTasksInSixMonths(),
                        record.getCompletedProjectsInSixMonths(), record.getCompletedTasksInSixMonths());

            } catch (Exception e) {
                logger.warn("处理记录时出错，ID: {}, 姓名: {}, 错误: {}", record.getId(), name, e.getMessage());
            }
        }

        logger.info("返回有效记录数: {}", validRecords.size());
        return validRecords;
    }

    /**
     * 获取有效的人员状态记录列表，默认返回10条记录
     * 
     * @return 符合条件的有效人员状态记录列表
     */
    public List<PersonnelStatus> getValidPersonnelStatusRecords() {
        return getValidPersonnelStatusRecords(10);
    }

    /**
     * 填充人员状态记录的进行中项目和任务数据
     * 
     * @param personnelStatus 人员状态记录
     */
    public void fillInProgressCounts(PersonnelStatus personnelStatus) {
        String personName = personnelStatus.getName();

        try {
            // 获取负责的进行中项目数量
            Long projectCount = projectService.countProjectsByResponsibleAndStatus(personName, "进行中");
            personnelStatus.setInProgressProjectCount(projectCount != null ? projectCount.intValue() : 0);

            // 获取负责的进行中任务数量（排除"分管"和培训类型的任务）
            Long taskCount = 0L;

            // 获取该人员负责的所有任务
            List<ProjectTask> tasks = taskService.findTasksByResponsible(personName);

            // 统计进行中且非分管类型的任务数量
            taskCount = tasks.stream()
                    .filter(task -> "进行中".equals(task.getStatus()) &&
                            (task.getType() == null || !"培训".equals(task.getType())) &&
                            (task.getType() == null || !"分管".equals(task.getType())))
                    .count();

            personnelStatus.setInProgressTaskCount(taskCount.intValue());

            logger.debug("已填充人员 {} 的进行中项目数 {} 和任务数 {} (已排除分管和培训类型任务)",
                    personName, personnelStatus.getInProgressProjectCount(), personnelStatus.getInProgressTaskCount());
        } catch (Exception e) {
            logger.error("获取人员 {} 的进行中项目和任务数时出错: {}", personName, e.getMessage(), e);
            // 如果出错，设置默认值为0
            personnelStatus.setInProgressProjectCount(0);
            personnelStatus.setInProgressTaskCount(0);
        }
    }

    /**
     * 填充人员状态记录的半年内新建项目和任务数据
     * 
     * @param personnelStatus 人员状态记录
     */
    public void fillSixMonthCounts(PersonnelStatus personnelStatus) {
        String personName = personnelStatus.getName();

        try {
            // 计算半年前的日期
            LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);
            String sixMonthsAgoStr = sixMonthsAgo.format(FORMATTER);

            logger.debug("开始计算人员 {} 半年内新建数据，半年前日期: {}", personName, sixMonthsAgoStr);

            // 获取负责的半年内新建项目数量
            Long newProjectCount = projectService.countProjectsByResponsibleAndCreatedDateAfter(personName,
                    sixMonthsAgoStr);
            personnelStatus.setNewProjectsInSixMonths(newProjectCount != null ? newProjectCount.intValue() : 0);

            // 获取负责的半年内新建任务数量
            Long newTaskCount = taskService.countTasksByResponsibleAndCreatedDateAfter(personName, sixMonthsAgoStr);
            personnelStatus.setNewTasksInSixMonths(newTaskCount != null ? newTaskCount.intValue() : 0);
            logger.debug("已填充人员 {} 的半年内新建项目数 {} 和任务数 {}",
                    personName, personnelStatus.getNewProjectsInSixMonths(), personnelStatus.getNewTasksInSixMonths());
        } catch (Exception e) {
            logger.error("获取人员 {} 的半年内新建项目和任务数时出错: {}", personName, e.getMessage(), e);
            // 如果出错，设置默认值为0
            personnelStatus.setNewProjectsInSixMonths(0);
            personnelStatus.setNewTasksInSixMonths(0);
        }
    }

    /**
     * 填充人员状态记录的半年内完成项目和任务数据
     * 
     * @param personnelStatus 人员状态记录
     */
    public void fillCompletedSixMonthCounts(PersonnelStatus personnelStatus) {
        String personName = personnelStatus.getName();

        try {
            // 计算半年前的日期
            LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);
            String sixMonthsAgoStr = sixMonthsAgo.format(FORMATTER);

            logger.debug("开始计算人员 {} 半年内完成数据，半年前日期: {}", personName, sixMonthsAgoStr);

            // 获取负责的半年内完成项目数量
            Long completedProjectCount = projectService.countCompletedProjectsByResponsibleAndEndDateAfter(personName,
                    sixMonthsAgoStr);
            personnelStatus.setCompletedProjectsInSixMonths(
                    completedProjectCount != null ? completedProjectCount.intValue() : 0);

            // 获取负责的半年内完成任务数量
            Long completedTaskCount = taskService.countCompletedTasksByResponsibleAndEndDateAfter(personName,
                    sixMonthsAgoStr);
            personnelStatus
                    .setCompletedTasksInSixMonths(completedTaskCount != null ? completedTaskCount.intValue() : 0);

            logger.debug("已填充人员 {} 的半年内完成项目数 {} 和任务数 {}",
                    personName, personnelStatus.getCompletedProjectsInSixMonths(),
                    personnelStatus.getCompletedTasksInSixMonths());
        } catch (Exception e) {
            logger.error("获取人员 {} 的半年内完成项目和任务数时出错: {}", personName, e.getMessage(), e);
            // 如果出错，设置默认值为0
            personnelStatus.setCompletedProjectsInSixMonths(0);
            personnelStatus.setCompletedTasksInSixMonths(0);
        }
    }

}