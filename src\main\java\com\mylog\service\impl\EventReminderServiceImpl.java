package com.mylog.service.impl;

import com.mylog.dto.EventReminderDTO;
import com.mylog.model.CalendarEvent;
import com.mylog.model.EventReminder;
import com.mylog.repository.CalendarEventRepository;
import com.mylog.repository.EventReminderRepository;
import com.mylog.service.EventReminderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 事件提醒服务实现类
 */
@Service
@Transactional
public class EventReminderServiceImpl implements EventReminderService {
    
    private static final Logger logger = LoggerFactory.getLogger(EventReminderServiceImpl.class);
      @Autowired
    private EventReminderRepository reminderRepository;
    
    @Autowired
    private CalendarEventRepository eventRepository;    @Override
    public EventReminderDTO createReminder(EventReminderDTO reminderDTO) {
        String reminderId = "RMD_" + System.currentTimeMillis() + "_" + Math.random();
        
        logger.debug("🔔 [{}] ========== 开始创建提醒 ==========", reminderId);
        logger.debug("📝 [{}] 事件ID: {}", reminderId, reminderDTO.getEventId());
        logger.debug("🔔 [{}] 提醒类型: {}", reminderId, reminderDTO.getReminderType());
        logger.debug("⏰ [{}] 提醒时间: {}", reminderId, reminderDTO.getReminderTime());
        logger.debug("💬 [{}] 提醒消息: {}", reminderId, reminderDTO.getMessage());
        
        try {
            logger.debug("🔍 [{}] 验证事件是否存在...", reminderId);
            // 检查事件是否存在
            CalendarEvent event = eventRepository.findById(reminderDTO.getEventId())
                .orElseThrow(() -> new IllegalArgumentException("事件不存在"));
            logger.debug("✅ [{}] 事件验证通过: {}", reminderId, event.getTitle());
            
            logger.debug("💾 [{}] 转换并保存提醒实体...", reminderId);
            EventReminder reminder = convertToEntity(reminderDTO, event);
            EventReminder savedReminder = reminderRepository.save(reminder);
            
            logger.debug("✅ [{}] 提醒创建成功: ID={}", reminderId, savedReminder.getId());
            logger.debug("🎉 [{}] ========== 提醒创建完成 ==========", reminderId);
            
            return new EventReminderDTO(savedReminder);
        } catch (Exception e) {
            logger.error("💥 [{}] ========== 提醒创建失败 ==========", reminderId);
            logger.error("❌ [{}] 错误信息: {}", reminderId, e.getMessage());
            logger.error("🔍 [{}] 错误类型: {}", reminderId, e.getClass().getSimpleName());
            throw e;
        }
    }
      @Override
    public EventReminderDTO updateReminder(Long id, EventReminderDTO reminderDTO) {
        EventReminder existingReminder = reminderRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("提醒不存在"));
        
        // 更新字段
        existingReminder.setReminderTime(reminderDTO.getReminderTime());
        existingReminder.setReminderType(reminderDTO.getReminderType());
        existingReminder.setMessage(reminderDTO.getMessage());
        existingReminder.setIsSent(reminderDTO.getIsSent());
        existingReminder.setRequiresCheckIn(reminderDTO.getRequiresCheckIn());
        existingReminder.setCheckInWindowMinutes(reminderDTO.getCheckInWindowMinutes());
        existingReminder.setRecipients(reminderDTO.getRecipients());
        
        EventReminder savedReminder = reminderRepository.save(existingReminder);
        return new EventReminderDTO(savedReminder);
    }
    
    @Override
    public void deleteReminder(Long id) {
        EventReminder reminder = reminderRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("提醒不存在"));
        
        reminderRepository.delete(reminder);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<EventReminderDTO> getReminderById(Long id) {
        return reminderRepository.findById(id)
            .map(EventReminderDTO::new);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<EventReminderDTO> getRemindersByEventId(Long eventId) {
        return reminderRepository.findByEventIdOrderByReminderTimeAsc(eventId)
            .stream()
            .map(EventReminderDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<EventReminderDTO> getPendingReminders(LocalDateTime currentTime) {
        return reminderRepository.findPendingReminders(currentTime)
            .stream()
            .map(EventReminderDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    public void markReminderAsSent(Long reminderId) {
        EventReminder reminder = reminderRepository.findById(reminderId)
            .orElseThrow(() -> new IllegalArgumentException("提醒不存在"));
        
        reminder.setIsSent(true);
        reminderRepository.save(reminder);
    }
    
    @Override
    public void markRemindersAsSent(List<Long> reminderIds) {
        List<EventReminder> reminders = reminderRepository.findAllById(reminderIds);
        reminders.forEach(reminder -> reminder.setIsSent(true));
        reminderRepository.saveAll(reminders);
    }
    
    @Override
    public void sendReminderNotification(EventReminderDTO reminder) {
        try {
            switch (reminder.getReminderType()) {
                case NOTIFICATION:
                    sendSystemNotification(reminder);
                    break;
                case EMAIL:
                    sendEmailNotification(reminder);
                    break;
                case SMS:
                    sendSmsNotification(reminder);
                    break;
                default:
                    logger.warn("未知的提醒类型: {}", reminder.getReminderType());
            }
            
            // 标记为已发送
            markReminderAsSent(reminder.getId());
            logger.info("提醒发送成功: 事件ID={}, 提醒ID={}", reminder.getEventId(), reminder.getId());
            
        } catch (Exception e) {
            logger.error("发送提醒失败: 事件ID={}, 提醒ID={}, 错误: {}", 
                        reminder.getEventId(), reminder.getId(), e.getMessage(), e);
        }
    }
    
    @Override
    public void processPendingReminders() {
        LocalDateTime currentTime = LocalDateTime.now();
        List<EventReminderDTO> pendingReminders = getPendingReminders(currentTime);
        
        //logger.info("开始处理待发送提醒，共{}条", pendingReminders.size());
        
        for (EventReminderDTO reminder : pendingReminders) {
            sendReminderNotification(reminder);
        }
        
        //logger.info("提醒处理完成");
    }    @Override
    public List<EventReminderDTO> createDefaultReminders(Long eventId, LocalDateTime eventStartTime) {
        String defaultId = "DEF_" + System.currentTimeMillis() + "_" + Math.random();
        
        logger.debug("🔔 [{}] ========== 开始创建默认提醒 ==========", defaultId);
        logger.debug("📝 [{}] 事件ID: {}", defaultId, eventId);
        logger.debug("⏰ [{}] 事件开始时间: {}", defaultId, eventStartTime);
        
        List<EventReminderDTO> defaultReminders = new ArrayList<>();
        
        try {            // 提前15分钟提醒
            logger.debug("🔔 [{}] 创建15分钟提醒...", defaultId);
            EventReminderDTO reminder15min = new EventReminderDTO();
            reminder15min.setEventId(eventId);
            reminder15min.setReminderTime(eventStartTime.minusMinutes(15));
            reminder15min.setReminderType(EventReminder.ReminderType.NOTIFICATION);
            reminder15min.setMessage("15分钟后有日程安排");
            reminder15min.setRequiresCheckIn(true); // 默认需要签到
            reminder15min.setCheckInWindowMinutes(30); // 默认30分钟签到窗口
            logger.debug("⏰ [{}] 15分钟提醒时间: {}", defaultId, reminder15min.getReminderTime());
            defaultReminders.add(createReminder(reminder15min));
            logger.debug("✅ [{}] 15分钟提醒创建成功", defaultId);
            
            // 提前1小时提醒（对于重要事件）
            logger.debug("🔔 [{}] 创建1小时提醒...", defaultId);
            EventReminderDTO reminder1hour = new EventReminderDTO();
            reminder1hour.setEventId(eventId);
            reminder1hour.setReminderTime(eventStartTime.minusHours(1));
            reminder1hour.setReminderType(EventReminder.ReminderType.NOTIFICATION);
            reminder1hour.setMessage("1小时后有日程安排");
            reminder1hour.setRequiresCheckIn(true); // 默认需要签到
            reminder1hour.setCheckInWindowMinutes(60); // 1小时提醒使用60分钟签到窗口
            logger.debug("⏰ [{}] 1小时提醒时间: {}", defaultId, reminder1hour.getReminderTime());
            defaultReminders.add(createReminder(reminder1hour));
            logger.debug("✅ [{}] 1小时提醒创建成功", defaultId);
            
            logger.debug("🎉 [{}] 默认提醒创建完成，事件ID={}, 共创建{}条提醒", defaultId, eventId, defaultReminders.size());
            logger.debug("🎉 [{}] ========== 默认提醒创建完成 ==========", defaultId);
            
            return defaultReminders;
        } catch (Exception e) {
            logger.error("💥 [{}] ========== 默认提醒创建失败 ==========", defaultId);
            logger.error("❌ [{}] 错误信息: {}", defaultId, e.getMessage());
            logger.error("🔍 [{}] 错误类型: {}", defaultId, e.getClass().getSimpleName());
            throw e;
        }
    }
    
    @Override
    public void deleteRemindersByEventId(Long eventId) {
        reminderRepository.deleteByEventId(eventId);
    }
    
    /**
     * 发送系统通知
     */
    private void sendSystemNotification(EventReminderDTO reminder) {
        // 实现系统通知逻辑
        // 这里可以集成系统通知服务或消息队列
        logger.info("发送系统通知: {}", reminder.getMessage());
    }
    
    /**
     * 发送邮件通知
     */
    private void sendEmailNotification(EventReminderDTO reminder) {
        // 实现邮件发送逻辑
        // 这里可以集成邮件服务
        logger.info("发送邮件通知: {}", reminder.getMessage());
    }
    
    /**
     * 发送短信通知
     */
    private void sendSmsNotification(EventReminderDTO reminder) {
        // 实现短信发送逻辑
        // 这里可以集成短信服务
        logger.info("发送短信通知: {}", reminder.getMessage());
    }
      /**
     * 将DTO转换为实体
     */    private EventReminder convertToEntity(EventReminderDTO dto, CalendarEvent event) {
        EventReminder reminder = new EventReminder();
        reminder.setEvent(event);
        reminder.setReminderTime(dto.getReminderTime());
        reminder.setReminderType(dto.getReminderType());
        reminder.setMessage(dto.getMessage());
        reminder.setIsSent(dto.getIsSent());
        reminder.setRequiresCheckIn(dto.getRequiresCheckIn());
        reminder.setCheckInWindowMinutes(dto.getCheckInWindowMinutes());
        reminder.setRecipients(dto.getRecipients());
        return reminder;
    }
}
