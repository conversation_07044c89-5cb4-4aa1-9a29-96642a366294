package com.mylog.task;

import com.mylog.service.EventReminderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 日历事件提醒定时任务
 */
@Component
public class CalendarReminderTask {
    
    private static final Logger logger = LoggerFactory.getLogger(CalendarReminderTask.class);
    
    @Autowired
    private EventReminderService reminderService;
    
    /**
     * 每分钟检查并处理待发送的提醒
     */
    @Scheduled(fixedRate = 60000) // 每60秒执行一次
    public void processReminderNotifications() {
        try {
            //logger.debug("开始执行提醒检查任务");
            reminderService.processPendingReminders();
            //logger.debug("提醒检查任务执行完成");
        } catch (Exception e) {
            logger.error("执行提醒检查任务时发生错误", e);
        }
    }
    
    /**
     * 每日清理过期的已发送提醒记录（可选）
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupOldReminders() {
        try {
            logger.info("开始执行过期提醒清理任务");
            // 这里可以实现清理逻辑，删除30天前的已发送提醒记录
            // reminderService.cleanupOldReminders(LocalDateTime.now().minusDays(30));
            logger.info("过期提醒清理任务执行完成");
        } catch (Exception e) {
            logger.error("执行过期提醒清理任务时发生错误", e);
        }
    }
}
