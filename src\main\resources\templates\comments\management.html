<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('评论管理')}">
    <meta charset="UTF-8">
    <title>评论管理</title>
    <style>
        /* 调整表格单元格的内边距到最小 */
        .table td,
        .table th {
            padding: 0.25rem 0.25rem;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-word;
        }

        /* 使所有单元格的内容都能换行显示 */
        .table td {
            max-width: none;
            overflow: visible;
        }

        /* 响应式调整：在小屏幕上进一步优化表格显示 */
        @media (max-width: 992px) {
            .table-responsive {
                overflow-x: auto;
            }

            .table td,
            .table th {
                min-width: 100px;
            }

            /* 评论内容列给予更多空间 */
            .table td:nth-child(3) {
                min-width: 200px;
            }
        }

        /* 评论内容样式 */
        .comment-content {
            max-height: 100px;
            overflow-y: auto;
            word-break: break-all;
        }

        /* 任务链接样式 */
        .task-link {
            color: #0d6efd;
            text-decoration: none;
        }

        .task-link:hover {
            text-decoration: underline;
        }

        /* 任务列样式 - 防止换行 */
        .table td:nth-child(2) {
            white-space: nowrap;
            min-width: 150px;
        }

        /* 创建时间列样式 - 防止换行 */
        .table td:nth-child(5) {
            white-space: nowrap;
            min-width: 120px;
        }
    </style>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">评论管理 <small class="fs-6">（总计 <span class="badge bg-primary rounded-pill"
                        th:text="${totalComments ?: 0}">0</span> 条评论）</small></h1>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">评论搜索</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                            data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <!-- 高级搜索 -->
                            <form th:action="@{/subtasks/advanced-search}" method="get" class="row g-3"
                                id="advancedSearchForm">
                                <!-- 动态搜索条件 -->
                                <div id="searchConditions">
                                    <div class="search-condition row mb-3">
                                        <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                                            <select class="form-select search-field" onchange="updateValueField(this)">
                                                <option value="">选择字段</option>
                                                <option value="taskName">所属任务</option>
                                                <option value="logContent">评论内容</option>
                                                <option value="createdBy">创建人</option>
                                                <option value="createdDate">创建时间</option>
                                            </select>
                                        </div>
                                        <div class="col-10 col-sm-5 col-md-7 value-container">
                                            <!-- 值输入框将根据选择的字段动态生成 -->
                                            <input type="text" class="form-control search-value" disabled
                                                placeholder="请先选择字段">
                                        </div>
                                        <div class="col-2 col-sm-1 col-md-2">
                                            <button type="button" class="btn btn-outline-danger"
                                                onclick="removeCondition(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按钮组 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="addSearchCondition()">
                                                <i class="bi bi-plus"></i> 添加条件
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-search"></i> 应用筛选
                                            </button>
                                            <a th:href="@{/subtasks/management}" class="btn btn-outline-secondary">
                                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评论列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">评论列表</h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse"
                        data-bs-target="#commentListCollapse" aria-expanded="true" aria-controls="commentListCollapse">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="commentListCollapse">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 10%">评论ID</th>
                                    <th style="width: 25%">所属任务</th>
                                    <th style="width: 40%">评论内容</th>
                                    <th style="width: 10%">创建人</th>
                                    <th style="width: 15%">创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="comment : ${commentPage.content}">
                                    <td th:text="${comment.subTaskId}">1</td>
                                    <td>
                                        <a th:if="${comment.task != null}"
                                           th:href="@{/tasks/{id}(id=${comment.taskId})}"
                                           class="task-link"
                                           th:text="${comment.task.taskName}">任务名称（ID:123）</a>
                                        <span th:unless="${comment.task != null}"
                                              th:text="'任务ID: ' + ${comment.taskId}">任务ID: 123</span>
                                    </td>
                                    <td>
                                        <div class="comment-content" th:text="${comment.logContent}">评论内容</div>
                                    </td>
                                    <td th:text="${comment.createdBy ?: '-'}">创建人</td>
                                    <td th:text="${comment.createdDate ?: '-'}">2025-01-01 12:00:00</td>
                                </tr>
                                <tr th:if="${commentPage.empty}">
                                    <td colspan="5" class="text-center">暂无评论</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- 分页控件 -->
                <div class="card-footer" th:if="${commentPage != null && commentPage.totalPages > 0}">
                    <div th:replace="~{fragments/pagination :: pagination(${commentPage}, @{/subtasks/management})}"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script th:inline="javascript">
        // 全局变量：人员列表
        const personnelList = /*[[${personnel}]]*/[];
        console.log('人员列表:', personnelList);
    </script>
    <script>


        // 高级搜索相关函数
        function updateValueField(selectField) {
            const valueContainer = selectField.closest('.search-condition').querySelector('.value-container');
            const selectedField = selectField.value;

            if (!selectedField) {
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            // 获取当前值（如果存在）
            const currentValue = valueContainer.querySelector('input, select')?.value || '';

            let inputHtml = '';

            // 根据选择的字段类型生成不同的输入控件
            switch (selectedField) {
                case 'createdDate':
                    // 创建时间范围搜索
                    inputHtml = `
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">开始</span>
                                    <input type="datetime-local" class="form-control" name="field_${selectedField}_start" placeholder="开始时间">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">结束</span>
                                    <input type="datetime-local" class="form-control" name="field_${selectedField}_end" placeholder="结束时间">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                case 'createdBy':
                    // 创建人下拉选择
                    inputHtml = `
                        <select name="field_${selectedField}" class="form-select search-value">
                            <option value="">请选择创建人</option>
                    `;

                    // 添加人员选项
                    console.log('添加人员选项:', personnelList);
                    personnelList.forEach(person => {
                        inputHtml += `<option value="${person}">${person}</option>`;
                    });

                    inputHtml += `
                        </select>
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
                    break;
                default:
                    inputHtml = `
                        <input type="text" name="field_${selectedField}" class="form-control search-value" placeholder="请输入${selectField.options[selectField.selectedIndex].text}">
                        <input type="hidden" name="fieldNames" value="${selectedField}">
                    `;
            }
            valueContainer.innerHTML = inputHtml;
        }

        function addSearchCondition() {
            const searchConditions = document.getElementById('searchConditions');
            const newCondition = document.createElement('div');
            newCondition.className = 'search-condition row mb-3';
            newCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">
                        <option value="">选择字段</option>
                        <option value="taskName">所属任务</option>
                        <option value="logContent">评论内容</option>
                        <option value="createdBy">创建人</option>
                        <option value="createdDate">创建时间</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            searchConditions.appendChild(newCondition);
        }



        function removeCondition(button) {
            const condition = button.closest('.search-condition');
            condition.remove();
        }

        // 添加带值的搜索条件函数
        function addSearchConditionWithValues(fieldName, fieldValue) {
            console.log(`添加带值的评论搜索条件: 字段=${fieldName}, 值=${fieldValue}`);
            const conditions = document.getElementById('searchConditions');
            const condition = document.createElement('div');
            condition.className = 'search-condition row mb-3';
            condition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">
                        <option value="">选择字段</option>
                        <option value="taskName">所属任务</option>
                        <option value="logContent">评论内容</option>
                        <option value="createdBy">创建人</option>
                        <option value="createdDate">创建时间</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(condition);

            // 设置字段值
            const select = condition.querySelector('.search-field');
            select.value = fieldName;
            updateValueField(select);

            // 设置输入值
            setTimeout(() => {
                // 尝试查找输入框
                const input = condition.querySelector('input[name="field_' + fieldName + '"]');
                if (input) {
                    input.value = fieldValue;
                    return;
                }

                // 尝试查找下拉框（用于创建人字段）
                const select = condition.querySelector('select[name="field_' + fieldName + '"]');
                if (select) {
                    select.value = fieldValue;
                }
            }, 100);
        }

        // 添加带值的时间条件函数
        function addTimeConditionWithValues(startDate, endDate) {
            console.log(`添加带值的时间条件: 开始=${startDate}, 结束=${endDate}`);
            const conditions = document.getElementById('searchConditions');
            const condition = document.createElement('div');
            condition.className = 'search-condition row mb-3';
            condition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select search-field" onchange="updateValueField(this)">
                        <option value="createdDate" selected>创建时间</option>
                    </select>
                </div>
                <div class="col-10 col-sm-5 col-md-7 value-container">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">开始</span>
                                <input type="datetime-local" class="form-control" name="field_createdDate_start" placeholder="开始时间">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">结束</span>
                                <input type="datetime-local" class="form-control" name="field_createdDate_end" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="fieldNames" value="createdDate">
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(condition);

            // 设置时间值
            if (startDate) {
                condition.querySelector('input[name="field_createdDate_start"]').value = startDate;
            }
            if (endDate) {
                condition.querySelector('input[name="field_createdDate_end"]').value = endDate;
            }
        }

        // 恢复搜索条件
        function restoreSearchConditions() {
            console.log('开始恢复评论搜索条件');
            // 检查URL是否包含搜索参数
            const url = new URL(window.location.href);
            const params = url.searchParams;
            const searchParams = {};

            // 收集所有搜索参数
            for (const [key, value] of params.entries()) {
                if (key.startsWith('field_') || key === 'fieldNames') {
                    searchParams[key] = value;
                }
            }

            // 如果没有搜索参数则不处理
            if (Object.keys(searchParams).length === 0) {
                console.log('没有搜索参数，不需要恢复');
                return;
            }

            console.log('恢复搜索条件:', searchParams);

            // 获取所有字段名
            const fieldNames = params.getAll('fieldNames');
            if (!fieldNames || fieldNames.length === 0) {
                console.log('没有字段名参数，不需要恢复');
                return;
            }

            // 移除默认的空搜索条件
            const searchConditions = document.getElementById('searchConditions');
            while (searchConditions.firstChild) {
                searchConditions.removeChild(searchConditions.firstChild);
            }

            // 重建搜索条件
            fieldNames.forEach(fieldName => {
                // 处理时间范围条件
                if (fieldName === 'createdDate') {
                    addTimeConditionWithValues(
                        params.get('field_createdDate_start'),
                        params.get('field_createdDate_end')
                    );
                    return;
                }

                // 处理其他条件
                const fieldValue = params.get('field_' + fieldName);
                if (fieldValue !== null) { // 允许空值
                    addSearchConditionWithValues(fieldName, fieldValue);
                }
            });

            console.log('搜索条件恢复完成');
        }

        // 页面加载完成后恢复搜索条件
        document.addEventListener('DOMContentLoaded', function () {
            console.log('评论管理页面加载完成');

            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 恢复搜索条件
            restoreSearchConditions();
        });
    </script>
</body>

</html>
