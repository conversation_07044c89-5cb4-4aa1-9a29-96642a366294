package com.mylog.service.impl;

import com.mylog.dto.CalendarEventDTO;
import com.mylog.dto.EventReminderDTO;
import com.mylog.model.Calendar;
import com.mylog.model.CalendarEvent;
import com.mylog.repository.CalendarEventRepository;
import com.mylog.repository.CalendarRepository;
import com.mylog.service.CalendarEventService;
import com.mylog.service.EventReminderService;
import com.mylog.util.SecurityUtils;
import com.mylog.util.WeixinMessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.List;
import java.util.Optional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Locale;

/**
 * 日历事件服务实现类
 */
@Service
@Transactional
public class CalendarEventServiceImpl implements CalendarEventService {
    
    private static final Logger logger = LoggerFactory.getLogger(CalendarEventServiceImpl.class);
    
    @Autowired
    private CalendarEventRepository eventRepository;
    
    @Autowired
    private CalendarRepository calendarRepository;
    
    @Autowired
    private EventReminderService reminderService;
    
    @Autowired
    private WeixinMessageUtil weixinMessageUtil;
    
    @Override
    public CalendarEventDTO createEvent(CalendarEventDTO eventDTO) {
        // 生成服务层的跟踪ID
        String serviceId = "SVC_" + System.currentTimeMillis() + "_" + Math.random();
        
        logger.debug("🔄 [{}] ========== 服务层开始创建事件 ==========", serviceId);
        logger.debug("📝 [{}] 事件标题: {}", serviceId, eventDTO.getTitle());
        logger.debug("📂 [{}] 日历ID: {}", serviceId, eventDTO.getCalendarId());
        logger.debug("👤 [{}] 创建者ID: {}", serviceId, eventDTO.getCreatorId());
        logger.debug("⏰ [{}] 开始时间: {}", serviceId, eventDTO.getStartTime());
        logger.debug("⏰ [{}] 结束时间: {}", serviceId, eventDTO.getEndTime());
        
        try {
            // 检查日历是否存在
            logger.debug("🔍 [{}] 检查日历是否存在...", serviceId);
            Calendar calendar = calendarRepository.findById(eventDTO.getCalendarId())
                .orElseThrow(() -> new IllegalArgumentException("日历不存在"));
            logger.debug("✅ [{}] 日历检查通过: {} (用户ID: {})", serviceId, calendar.getName(), calendar.getUserId());
            
            // 检查权限
            logger.debug("🔒 [{}] 检查权限...", serviceId);
            if (!calendar.getUserId().equals(eventDTO.getCreatorId()) && !calendar.getIsShared()) {
                logger.error("❌ [{}] 权限检查失败", serviceId);
                throw new IllegalArgumentException("无权限在此日历中创建事件");
            }
            logger.debug("✅ [{}] 权限检查通过", serviceId);
            
            logger.debug("💾 [{}] 转换并保存事件实体...", serviceId);
            CalendarEvent event = convertToEntity(eventDTO, calendar);
            CalendarEvent savedEvent = eventRepository.save(event);
            logger.debug("✅ [{}] 事件保存成功，ID: {}", serviceId, savedEvent.getId());
            
            // 处理提醒数据
            CalendarEventDTO result = new CalendarEventDTO(savedEvent);
            logger.debug("🔔 [{}] 开始处理提醒数据...", serviceId);
            
            if (eventDTO.getReminders() != null && !eventDTO.getReminders().isEmpty()) {
                // 处理自定义提醒
                logger.debug("🔔 [{}] 处理自定义提醒，共{}条", serviceId, eventDTO.getReminders().size());
                List<EventReminderDTO> savedReminders = new ArrayList<>();
                for (int i = 0; i < eventDTO.getReminders().size(); i++) {
                    EventReminderDTO reminderDTO = eventDTO.getReminders().get(i);
                    logger.debug("🔔 [{}] 处理第{}条提醒: {}", serviceId, i+1, reminderDTO);
                    
                    // 设置事件ID
                    reminderDTO.setEventId(savedEvent.getId());
                    
                    // 如果提醒时间为空，根据提醒间隔计算提醒时间
                    if (reminderDTO.getReminderTime() == null && reminderDTO.getTime() != null) {
                        LocalDateTime reminderTime = savedEvent.getStartTime().minusMinutes(reminderDTO.getTime());
                        reminderDTO.setReminderTime(reminderTime);
                        logger.debug("⏰ [{}] 计算提醒时间: {}", serviceId, reminderTime);
                    }
                    
                    // 创建提醒
                    EventReminderDTO savedReminder = reminderService.createReminder(reminderDTO);
                    savedReminders.add(savedReminder);
                    logger.debug("✅ [{}] 第{}条提醒创建成功，ID: {}", serviceId, i+1, savedReminder.getId());
                }
                result.setReminders(savedReminders);
                logger.debug("🎉 [{}] 自定义提醒创建完成，共{}条", serviceId, savedReminders.size());
            } else {
                // 创建默认提醒
                logger.debug("🔔 [{}] 没有自定义提醒，创建默认提醒", serviceId);
                List<EventReminderDTO> defaultReminders = reminderService.createDefaultReminders(
                    savedEvent.getId(), savedEvent.getStartTime());
                result.setReminders(defaultReminders);
                logger.debug("🎉 [{}] 默认提醒创建完成，共{}条", serviceId, defaultReminders.size());
            }
            
            logger.debug("✅ [{}] ========== 服务层事件创建完成 ==========", serviceId);
            return result;
              } catch (Exception e) {
            logger.error("💥 [{}] ========== 服务层创建事件失败 ==========", serviceId);
            logger.error("❌ [{}] 错误信息: {}", serviceId, e.getMessage());
            logger.error("🔍 [{}] 错误类型: {}", serviceId, e.getClass().getSimpleName());
            logger.error("📊 [{}] 错误堆栈:", serviceId, e);
            
            // 如果是数据库锁定错误，发送企业微信通知
            if (e instanceof CannotAcquireLockException || 
                (e.getMessage() != null && e.getMessage().contains("database is locked"))) {
                try {
                    String errorMsg = String.format(
                        "## <font color=\"warning\">数据库锁定错误</font>\n\n" +
                        "**事件标题**: %s\n" +
                        "**错误信息**: %s\n" +
                        "**发生时间**: %s\n" +
                        "**服务ID**: %s\n\n" +
                        "请检查数据库状态和并发访问情况。",
                        eventDTO.getTitle() != null ? eventDTO.getTitle() : "未知",
                        e.getMessage(),
                        LocalDateTime.now().toString(),
                        serviceId
                    );
                    
                    List<String> mentionedList = new ArrayList<>();
                    mentionedList.add("@all");
                    weixinMessageUtil.sendWeixinMessage("数据库锁定错误", errorMsg, mentionedList);
                    logger.info("✅ [{}] 错误通知已发送至企业微信", serviceId);
                } catch (Exception notifyException) {
                    logger.error("发送企业微信通知时发生错误: {}", notifyException.getMessage(), notifyException);
                }
            }
            
            throw e;
        }
    }
      @Override
    public CalendarEventDTO updateEvent(Long id, CalendarEventDTO eventDTO) {
        CalendarEvent existingEvent = eventRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("事件不存在"));
        
        // 检查权限：只有创建者才能修改事件
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            throw new IllegalArgumentException("用户未登录");
        }
        
        if (!existingEvent.getCreatorId().equals(currentUserId)) {
            throw new IllegalArgumentException("无权限修改此事件");
        }
        
        // 如果更改了日历，需要检查新日历的权限
        if (!existingEvent.getCalendar().getId().equals(eventDTO.getCalendarId())) {
            Calendar newCalendar = calendarRepository.findById(eventDTO.getCalendarId())
                .orElseThrow(() -> new IllegalArgumentException("目标日历不存在"));
            
            if (!newCalendar.getUserId().equals(currentUserId) && !newCalendar.getIsShared()) {
                throw new IllegalArgumentException("无权限将事件移动到此日历");
            }
            
            existingEvent.setCalendar(newCalendar);
        }
        
        // 更新字段
        existingEvent.setTitle(eventDTO.getTitle());
        existingEvent.setDescription(eventDTO.getDescription());
        existingEvent.setStartTime(eventDTO.getStartTime());
        existingEvent.setEndTime(eventDTO.getEndTime());
        existingEvent.setIsAllDay(eventDTO.getIsAllDay());
        existingEvent.setLocation(eventDTO.getLocation());
        existingEvent.setEventType(eventDTO.getEventType());
        existingEvent.setPriority(eventDTO.getPriority());
        existingEvent.setIsRecurring(eventDTO.getIsRecurring());
        existingEvent.setRecurrenceType(eventDTO.getRecurrenceType());
        existingEvent.setRecurrenceInterval(eventDTO.getRecurrenceInterval());        existingEvent.setRecurrenceEndDate(eventDTO.getRecurrenceEndDate());
        
        CalendarEvent savedEvent = eventRepository.save(existingEvent);
          // 处理提醒数据更新
        CalendarEventDTO result = new CalendarEventDTO(savedEvent);
        if (eventDTO.getReminders() != null) {
            List<EventReminderDTO> savedReminders = new ArrayList<>();
            
            // 获取现有的提醒ID列表
            List<EventReminderDTO> existingReminders = reminderService.getRemindersByEventId(savedEvent.getId());
            Set<Long> existingReminderIds = existingReminders.stream()
                .map(EventReminderDTO::getId)
                .collect(Collectors.toSet());
            
            Set<Long> updatedReminderIds = new HashSet<>();
            
            for (EventReminderDTO reminderDTO : eventDTO.getReminders()) {
                // 设置事件ID
                reminderDTO.setEventId(savedEvent.getId());
                
                // 如果提醒时间为空，根据提醒间隔计算提醒时间
                if (reminderDTO.getReminderTime() == null && reminderDTO.getTime() != null) {
                    LocalDateTime reminderTime = savedEvent.getStartTime().minusMinutes(reminderDTO.getTime());
                    reminderDTO.setReminderTime(reminderTime);
                }
                
                EventReminderDTO savedReminder;
                if (reminderDTO.getId() != null && existingReminderIds.contains(reminderDTO.getId())) {
                    // 更新现有提醒
                    savedReminder = reminderService.updateReminder(reminderDTO.getId(), reminderDTO);
                    updatedReminderIds.add(reminderDTO.getId());
                } else {
                    // 创建新提醒
                    savedReminder = reminderService.createReminder(reminderDTO);
                }
                savedReminders.add(savedReminder);
            }
              // 删除不再需要的提醒
            Set<Long> remindersToDelete = existingReminderIds.stream()
                .filter(existingId -> !updatedReminderIds.contains(existingId))
                .collect(Collectors.toSet());
            
            for (Long reminderId : remindersToDelete) {
                reminderService.deleteReminder(reminderId);
            }
            
            result.setReminders(savedReminders);
        }
        
        return result;
    }    @Override
    public void deleteEvent(Long id) {
        CalendarEvent event = eventRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("事件不存在"));
        
        // 检查权限：只有创建者才能删除事件
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            throw new IllegalArgumentException("用户未登录");
        }
        
        if (!event.getCreatorId().equals(currentUserId)) {
            throw new IllegalArgumentException("无权限删除此事件");
        }
        
        // 先删除关联的提醒
        reminderService.deleteRemindersByEventId(id);
        
        // 然后删除事件
        eventRepository.delete(event);
    }
      @Override
    @Transactional(readOnly = true)
    public Optional<CalendarEventDTO> getEventById(Long id) {
        return eventRepository.findById(id)
            .map(event -> {
                // 强制加载提醒信息（因为是懒加载）
                event.getReminders().size(); // 触发懒加载
                return new CalendarEventDTO(event);
            });
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByCalendarId(Long calendarId) {
        return eventRepository.findByCalendarIdOrderByStartTimeAsc(calendarId)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByUserId(Long userId) {
        return eventRepository.findByCreatorIdOrderByStartTimeAsc(userId)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }      @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        // 将LocalDateTime转换为数据库中使用的字符串格式
        String startTimeStr = startTime.toString().replace("T", " ");
        String endTimeStr = endTime.toString().replace("T", " ");
        
        // 使用原生SQL查询来避免时间格式问题
        List<CalendarEvent> events = eventRepository.findByTimeRangeNative(startTimeStr, endTimeStr);
        
        return events.stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
      @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getAccessibleEventsByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取用户可访问的所有日历（包括自己的日历和共享日历）
        List<Calendar> accessibleCalendars = calendarRepository.findByUserIdOrShared(userId);
        
        if (accessibleCalendars.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取日历ID列表
        List<Long> calendarIds = accessibleCalendars.stream()
            .map(Calendar::getId)
            .collect(Collectors.toList());
        
        // 根据可访问的日历ID和时间范围查询事件
        return eventRepository.findByCalendarIdsAndTimeRange(calendarIds, startTime, endTime)
            .stream()
            .map(event -> {
                // 强制加载提醒信息（因为是懒加载）
                event.getReminders().size(); // 触发懒加载
                return new CalendarEventDTO(event);
            })
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByCalendarIdAndTimeRange(Long calendarId, LocalDateTime startTime, LocalDateTime endTime) {
        return eventRepository.findByCalendarIdAndTimeRange(calendarId, startTime, endTime)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return eventRepository.findByUserIdAndTimeRange(userId, startTime, endTime)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByCalendarIdsAndTimeRange(List<Long> calendarIds, LocalDateTime startTime, LocalDateTime endTime) {
        return eventRepository.findByCalendarIdsAndTimeRange(calendarIds, startTime, endTime)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> searchEvents(String keyword) {
        return eventRepository.searchByKeyword(keyword)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> searchEventsByUserId(Long userId, String keyword) {
        return eventRepository.searchByUserIdAndKeyword(userId, keyword)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByType(CalendarEvent.EventType eventType) {
        return eventRepository.findByEventTypeOrderByStartTimeAsc(eventType)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getEventsByPriority(CalendarEvent.Priority priority) {
        return eventRepository.findByPriorityOrderByStartTimeAsc(priority)
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getRecurringEvents() {
        return eventRepository.findByIsRecurringTrueOrderByStartTimeAsc()
            .stream()
            .map(CalendarEventDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<CalendarEventDTO> createRecurringEventInstances(CalendarEventDTO eventDTO, LocalDateTime endDate) {
        List<CalendarEventDTO> instances = new ArrayList<>();
        
        if (!eventDTO.getIsRecurring() || eventDTO.getRecurrenceType() == null) {
            return instances;
        }
        
        LocalDateTime currentStart = eventDTO.getStartTime();
        LocalDateTime currentEnd = eventDTO.getEndTime();
        int interval = eventDTO.getRecurrenceInterval() != null ? eventDTO.getRecurrenceInterval() : 1;
        
        while (currentStart.isBefore(endDate)) {
            CalendarEventDTO instance = new CalendarEventDTO();
            // 复制所有属性
            copyEventProperties(eventDTO, instance);
            instance.setStartTime(currentStart);
            instance.setEndTime(currentEnd);
            instance.setIsRecurring(false); // 实例不是重复事件
            
            instances.add(createEvent(instance));
            
            // 计算下一个实例的时间
            switch (eventDTO.getRecurrenceType()) {
                case DAILY:
                    currentStart = currentStart.plusDays(interval);
                    if (currentEnd != null) {
                        currentEnd = currentEnd.plusDays(interval);
                    }
                    break;
                case WEEKLY:
                    currentStart = currentStart.plusWeeks(interval);
                    if (currentEnd != null) {
                        currentEnd = currentEnd.plusWeeks(interval);
                    }
                    break;
                case MONTHLY:
                    currentStart = currentStart.plusMonths(interval);
                    if (currentEnd != null) {
                        currentEnd = currentEnd.plusMonths(interval);
                    }
                    break;
                case YEARLY:
                    currentStart = currentStart.plusYears(interval);
                    if (currentEnd != null) {
                        currentEnd = currentEnd.plusYears(interval);
                    }
                    break;
            }
        }
        
        return instances;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean hasEventAccess(Long userId, Long eventId) {
        return eventRepository.findById(eventId)
            .map(event -> {
                // 检查是否是创建者
                if (event.getCreatorId().equals(userId)) {
                    return true;
                }
                // 检查是否有日历访问权限
                Calendar calendar = event.getCalendar();
                return calendar.getUserId().equals(userId) || calendar.getIsShared();
            })
            .orElse(false);
    }
      @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getTodayEvents(Long userId) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.plusDays(1).atStartOfDay();
        
        // 使用可访问事件方法，包括共享日历
        return getAccessibleEventsByTimeRange(userId, startOfDay, endOfDay);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getWeekEvents(Long userId) {
        LocalDate today = LocalDate.now();
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        LocalDate startOfWeek = today.with(weekFields.dayOfWeek(), 1);
        LocalDate endOfWeek = startOfWeek.plusDays(7);
        
        LocalDateTime startOfWeekTime = startOfWeek.atStartOfDay();
        LocalDateTime endOfWeekTime = endOfWeek.atStartOfDay();
        
        // 使用可访问事件方法，包括共享日历
        return getAccessibleEventsByTimeRange(userId, startOfWeekTime, endOfWeekTime);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarEventDTO> getMonthEvents(Long userId) {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        LocalDate endOfMonth = startOfMonth.plusMonths(1);
        
        LocalDateTime startOfMonthTime = startOfMonth.atStartOfDay();
        LocalDateTime endOfMonthTime = endOfMonth.atStartOfDay();
        
        // 使用可访问事件方法，包括共享日历
        return getAccessibleEventsByTimeRange(userId, startOfMonthTime, endOfMonthTime);
    }
    
    /**
     * 将DTO转换为实体
     */
    private CalendarEvent convertToEntity(CalendarEventDTO dto, Calendar calendar) {
        CalendarEvent event = new CalendarEvent();
        event.setTitle(dto.getTitle());
        event.setDescription(dto.getDescription());
        event.setStartTime(dto.getStartTime());
        event.setEndTime(dto.getEndTime());
        event.setIsAllDay(dto.getIsAllDay());
        event.setLocation(dto.getLocation());
        event.setEventType(dto.getEventType());
        event.setPriority(dto.getPriority());
        event.setIsRecurring(dto.getIsRecurring());
        event.setRecurrenceType(dto.getRecurrenceType());
        event.setRecurrenceInterval(dto.getRecurrenceInterval());
        event.setRecurrenceEndDate(dto.getRecurrenceEndDate());
        event.setCalendar(calendar);
        event.setCreatorId(dto.getCreatorId());
        return event;
    }
    
    /**
     * 复制事件属性
     */
    private void copyEventProperties(CalendarEventDTO source, CalendarEventDTO target) {
        target.setTitle(source.getTitle());
        target.setDescription(source.getDescription());
        target.setIsAllDay(source.getIsAllDay());
        target.setLocation(source.getLocation());
        target.setEventType(source.getEventType());
        target.setPriority(source.getPriority());
        target.setCalendarId(source.getCalendarId());
        target.setCreatorId(source.getCreatorId());
    }
}
