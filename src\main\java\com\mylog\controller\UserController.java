package com.mylog.controller;

import com.mylog.model.user.User;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Optional;

@Controller
@RequestMapping("/system/users")
@PreAuthorize("hasRole('ADMIN')")
public class UserController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserActivityLogService activityLogService;
    
    @GetMapping
    public String listUsers(Model model) {
        // 重定向到统一的用户管理入口
        return "redirect:/admin/users";
    }
    
    @GetMapping("/new")
    public String newUserForm(Model model) {
        User user = new User();
        model.addAttribute("user", user);
        model.addAttribute("activeMenu", "users");
        model.addAttribute("roles", User.UserRole.values());
        return "admin/users/form";
    }
    
    @GetMapping("/{id}/edit")
    public String editUserForm(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        Optional<User> userOpt = userService.findUserById(id);
        if (userOpt.isPresent()) {
            model.addAttribute("user", userOpt.get());
            model.addAttribute("activeMenu", "users");
            model.addAttribute("roles", User.UserRole.values());
            return "admin/users/form";
        } else {
            redirectAttributes.addFlashAttribute("error", "用户不存在");
            return "redirect:/system/users";
        }
    }
    
    @PostMapping("/save")
    public String saveUser(User user, RedirectAttributes redirectAttributes) {
        try {
            // 检查用户名是否已存在
            if (user.getUserId() == null && userService.existsByUsername(user.getUsername())) {
                redirectAttributes.addFlashAttribute("error", "用户名已存在");
                return "redirect:/system/users/new";
            }
            
            // 如果是编辑现有用户，保留原有的CreatedDate
            if (user.getUserId() != null) {
                Optional<User> existingUserOpt = userService.findUserById(user.getUserId());
                if (existingUserOpt.isPresent()) {
                    User existingUser = existingUserOpt.get();
                    // 保留原有的CreatedDate
                    user.setCreatedDate(existingUser.getCreatedDate());
                    // 如果密码为空，保留原有密码
                    if (user.getPassword() == null || user.getPassword().isEmpty()) {
                        user.setPassword(existingUser.getPassword());
                    }
                }
            }
            
            // 保存用户信息
            User savedUser = userService.saveUser(user);
            
            // 如果提供了新密码，则更新密码
            if (user.getPassword() != null && !user.getPassword().isEmpty()) {
                // 对于管理员修改密码，不需要验证当前密码
                userService.changePassword(savedUser.getUserId(), savedUser.getPassword(), user.getPassword());
            }
            
            redirectAttributes.addFlashAttribute("message", "用户保存成功");
            
            // 记录用户活动
            Optional<User> currentUser = userService.findUserByUsername(user.getUsername());
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                if (user.getUserId() == null) {
                    activityLogService.logCreate(
                        currentUser.get().getUserId(),
                        currentUser.get().getUsername(),
                        "创建新用户: " + user.getUsername(),
                        ipAddress,
                        "User",
                        savedUser.getUserId(),
                        getAccessType()
                    );
                } else {
                    activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUser.get().getUsername(),
                        "更新用户信息: " + user.getUsername(),
                        ipAddress,
                        "User",
                        savedUser.getUserId(),
                        getAccessType()
                    );
                }
            }
            
            return "redirect:/admin/users";
        } catch (Exception e) {
            logger.error("保存用户时发生错误", e);
            redirectAttributes.addFlashAttribute("error", "保存用户失败: " + e.getMessage());
            return "redirect:/system/users/new";
        }
    }
    
    @PostMapping("/delete")
    public String deleteUser(@RequestParam("userId") Long id, RedirectAttributes redirectAttributes) {
        try {
            // 获取要删除的用户信息（用于日志记录）
            Optional<User> userToDelete = userService.findUserById(id);
            if (!userToDelete.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "用户不存在");
                return "redirect:/system/users";
            }
            
            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            
            // 删除用户
            userService.deleteUser(id);
            
            // 记录活动日志
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logDelete(
                    currentUser.get().getUserId(),
                    currentUsername,
                    "删除用户: " + userToDelete.get().getUsername(),
                    ipAddress,
                    "User",
                    id,
                    getAccessType()
                );
            }
            
            redirectAttributes.addFlashAttribute("message", "用户删除成功");
            
        } catch (Exception e) {
            logger.error("删除用户时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除用户时出错: " + e.getMessage());
        }
        return "redirect:/system/users";
    }
} 