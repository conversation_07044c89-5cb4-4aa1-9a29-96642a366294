package com.mylog.config;

import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

@Configuration
@EnableScheduling
public class CacheMonitorConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheMonitorConfig.class);
    
    @Autowired
    private CacheManager cacheManager;
    
    private final ConcurrentHashMap<String, CacheStats> cacheStats = new ConcurrentHashMap<>();
    
    @Scheduled(fixedRate = 3000000) // 每50分钟执行一次
    public void monitorCache() {
        logger.debug("开始监控缓存状态...");
        cacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                CacheStats stats = cacheStats.computeIfAbsent(cacheName, k -> new CacheStats());
                stats.incrementAccess();
                logger.debug("缓存 '{}' 正常运行，访问次数: {}", cacheName, stats.getAccessCount());
            }
        });
    }
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void clearCache() {
        logger.info("开始执行缓存清理...");
        cacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                logger.info("已清理缓存: {}", cacheName);
            }
        });
        logger.info("缓存清理完成");
    }
    
    public Map<String, CacheStats> getCacheStatistics() {
        return new ConcurrentHashMap<>(cacheStats);
    }
    
    public static class CacheStats {
        private long accessCount = 0;
        private long hitCount = 0;
        private long missCount = 0;
        
        public synchronized void incrementAccess() {
            accessCount++;
        }
        
        public synchronized void incrementHit() {
            hitCount++;
            accessCount++;
        }
        
        public synchronized void incrementMiss() {
            missCount++;
            accessCount++;
        }
        
        public long getAccessCount() {
            return accessCount;
        }
        
        public long getHitCount() {
            return hitCount;
        }
        
        public long getMissCount() {
            return missCount;
        }
        
        public double getHitRate() {
            return accessCount == 0 ? 0.0 : (double) hitCount / accessCount;
        }
    }
}
