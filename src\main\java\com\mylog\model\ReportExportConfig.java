package com.mylog.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalTime;
import java.time.LocalDateTime;

@Entity
@Table(name = "report_export_config")
public class ReportExportConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "monday")
    private boolean monday;

    @Column(name = "tuesday")
    private boolean tuesday;

    @Column(name = "wednesday")
    private boolean wednesday;

    @Column(name = "thursday")
    private boolean thursday;

    @Column(name = "friday")
    private boolean friday;

    @Column(name = "saturday")
    private boolean saturday;

    @Column(name = "sunday")
    private boolean sunday;

    @Column(name = "export_time")
    private LocalTime exportTime;

    @Column(name = "enabled")
    private boolean enabled;

    @Column(name = "last_export_time")
    private LocalDateTime lastExportTime;

    @Column(name = "export_path", length = 255)
    private String exportPath = "reports";  // 默认值为 "reports"

    @Column(name = "notification_receiver", length = 50)
    @Size(max = 50, message = "通知接收人名称不能超过50个字符")
    @NotBlank(message = "通知接收人不能为空")
    private String notificationReceiver = "姚强";  // 默认接收人

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isMonday() {
        return monday;
    }

    public void setMonday(boolean monday) {
        this.monday = monday;
    }

    public boolean isTuesday() {
        return tuesday;
    }

    public void setTuesday(boolean tuesday) {
        this.tuesday = tuesday;
    }

    public boolean isWednesday() {
        return wednesday;
    }

    public void setWednesday(boolean wednesday) {
        this.wednesday = wednesday;
    }

    public boolean isThursday() {
        return thursday;
    }

    public void setThursday(boolean thursday) {
        this.thursday = thursday;
    }

    public boolean isFriday() {
        return friday;
    }

    public void setFriday(boolean friday) {
        this.friday = friday;
    }

    public boolean isSaturday() {
        return saturday;
    }

    public void setSaturday(boolean saturday) {
        this.saturday = saturday;
    }

    public boolean isSunday() {
        return sunday;
    }

    public void setSunday(boolean sunday) {
        this.sunday = sunday;
    }

    public LocalTime getExportTime() {
        return exportTime;
    }

    public void setExportTime(LocalTime exportTime) {
        this.exportTime = exportTime;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getLastExportTime() {
        return lastExportTime;
    }

    public void setLastExportTime(LocalDateTime lastExportTime) {
        this.lastExportTime = lastExportTime;
    }

    public String getExportPath() {
        return exportPath;
    }

    public void setExportPath(String exportPath) {
        this.exportPath = exportPath != null && !exportPath.trim().isEmpty() ? exportPath.trim() : "reports";
    }

    public String getNotificationReceiver() {
        return notificationReceiver;
    }

    public void setNotificationReceiver(String notificationReceiver) {
        this.notificationReceiver = notificationReceiver;
    }
}