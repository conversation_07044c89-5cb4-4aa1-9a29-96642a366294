<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('风险分析报表')}">
    <meta charset="UTF-8">
    <title>风险分析报表</title>
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">风险分析报表</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToPDF()">导出PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">导出Excel</button>
                        </div>
                        <a th:href="@{/reports}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">筛选条件</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/reports/risk-analysis}" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 图表展示区域 -->
                <div class="row">
                    <!-- 风险等级分布 -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">风险等级分布</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="riskDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 风险趋势 -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">风险趋势（按周）</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="riskTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高风险任务列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">高风险任务列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success" th:if="${#lists.isEmpty(highRiskTasks)}">
                            <i class="bi bi-info-circle me-2"></i>当前没有高风险任务
                        </div>
                        <div class="table-responsive" th:if="${not #lists.isEmpty(highRiskTasks)}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>任务名称</th>
                                        <th>负责人</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>完成率</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${highRiskTasks}">
                                        <td th:text="${task.taskName}">任务名称</td>
                                        <td th:text="${task.responsible}">负责人</td>
                                        <td>
                                            <span th:class="${'badge ' +
                                                (task.status == '未开始' ? 'bg-secondary' :
                                                (task.status == '进行中' ? 'bg-primary' :
                                                (task.status == '已完成' ? 'bg-success' : 'bg-danger')))}"
                                                  th:text="${task.status}">状态</span>
                                        </td>
                                        <td th:text="${#temporals.format(task.createdDateTime, 'yyyy-MM-dd')}">创建时间</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                     th:style="'width: ' + ${task.ratio != null ? task.ratio * 100 : 0} + '%'"
                                                     th:text="${#numbers.formatPercent(task.ratio != null ? task.ratio : 0, 0, 0)}">
                                                    0%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a th:href="@{/tasks/edit/{id}(id=${task.taskId})}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 风险管理建议 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">风险管理建议</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-lightbulb me-2"></i>风险管理建议</h6>
                            <ul>
                                <li>定期检查高风险任务的进展情况，确保及时发现和解决问题</li>
                                <li>对于进度延迟的高风险任务，考虑增加资源或调整计划</li>
                                <li>建立风险应对机制，明确责任人和应对措施</li>
                                <li>加强团队沟通，确保风险信息及时共享</li>
                            </ul>
                        </div>
                    </div>
                </div>
    </div>
    <script th:inline="javascript">
        // 从后端获取数据
        const riskDistributionData = /*[[${riskDistributionData}]]*/ {};
        const riskTrendData = /*[[${riskTrendData}]]*/ {};

        // 风险等级分布图表
        const riskDistributionCtx = document.getElementById('riskDistributionChart').getContext('2d');
        new Chart(riskDistributionCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(riskDistributionData),
                datasets: [{
                    data: Object.values(riskDistributionData),
                    backgroundColor: [
                        '#198754', // 正常 - 绿色
                        '#0dcaf0', // 低 - 浅蓝
                        '#ffc107', // 中 - 黄色
                        '#dc3545'  // 高 - 红色
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: '风险等级分布'
                    }
                }
            }
        });

        // 风险趋势图表
        const riskTrendCtx = document.getElementById('riskTrendChart').getContext('2d');

        // 处理风险趋势数据
        const weeks = Object.keys(riskTrendData);
        const riskLevels = ['正常', '低', '中', '高'];
        const datasets = riskLevels.map((level, index) => {
            const colors = ['#198754', '#0dcaf0', '#ffc107', '#dc3545'];
            return {
                label: level,
                data: weeks.map(week => riskTrendData[week][level] || 0),
                backgroundColor: colors[index],
                borderColor: colors[index],
                borderWidth: 1
            };
        });

        new Chart(riskTrendCtx, {
            type: 'line',
            data: {
                labels: weeks,
                datasets: datasets
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: '风险趋势（按周）'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 导出PDF函数
        function exportToPDF() {
            alert('导出PDF功能将在后续实现');
        }

        // 导出Excel函数
        function exportToExcel() {
            alert('导出Excel功能将在后续实现');
        }
    </script>
</body>
</html>