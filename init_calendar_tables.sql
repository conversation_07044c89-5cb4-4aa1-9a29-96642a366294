-- Create calendar tables for mylog-web
-- SQLite compatible version

-- Create calendars table
CREATE TABLE IF NOT EXISTS calendars (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    color VARCHAR(7) DEFAULT '#007bff',
    user_id INTEGER NOT NULL,
    is_default BOOLEAN DEFAULT 0,
    is_shared BOOLEAN DEFAULT 0,
    created_time DATETIME NOT NULL,
    updated_time DATETIME,
    UNIQUE(user_id, name)
);

-- Create indexes for calendars
CREATE INDEX IF NOT EXISTS idx_calendars_user_id ON calendars(user_id);
CREATE INDEX IF NOT EXISTS idx_calendars_is_default ON calendars(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_calendars_is_shared ON calendars(is_shared);

-- Create calendar_events table
CREATE TABLE IF NOT EXISTS calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    description VARCHAR(1000),
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    is_all_day BOOLEAN DEFAULT 0,
    location VARCHAR(200),
    event_type VARCHAR(20) DEFAULT 'MEETING',
    priority VARCHAR(10) DEFAULT 'NORMAL',
    is_recurring BOOLEAN DEFAULT 0,
    recurrence_type VARCHAR(20),
    recurrence_interval INTEGER,
    recurrence_end_date DATETIME,
    calendar_id INTEGER NOT NULL,
    creator_id INTEGER NOT NULL,
    created_time DATETIME NOT NULL,
    updated_time DATETIME,
    FOREIGN KEY (calendar_id) REFERENCES calendars(id) ON DELETE CASCADE
);

-- Create indexes for calendar_events
CREATE INDEX IF NOT EXISTS idx_events_calendar_id ON calendar_events(calendar_id);
CREATE INDEX IF NOT EXISTS idx_events_creator_id ON calendar_events(creator_id);
CREATE INDEX IF NOT EXISTS idx_events_start_time ON calendar_events(start_time);
CREATE INDEX IF NOT EXISTS idx_events_time_range ON calendar_events(start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_events_event_type ON calendar_events(event_type);
CREATE INDEX IF NOT EXISTS idx_events_priority ON calendar_events(priority);
CREATE INDEX IF NOT EXISTS idx_events_is_recurring ON calendar_events(is_recurring);
CREATE INDEX IF NOT EXISTS idx_events_title ON calendar_events(title);

-- Create event_reminders table
CREATE TABLE IF NOT EXISTS event_reminders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_id INTEGER NOT NULL,
    reminder_time DATETIME NOT NULL,
    reminder_type VARCHAR(20) DEFAULT 'NOTIFICATION',
    is_sent BOOLEAN DEFAULT 0,
    message VARCHAR(500),
    created_time DATETIME NOT NULL,
    FOREIGN KEY (event_id) REFERENCES calendar_events(id) ON DELETE CASCADE
);

-- Create indexes for event_reminders
CREATE INDEX IF NOT EXISTS idx_reminders_event_id ON event_reminders(event_id);
CREATE INDEX IF NOT EXISTS idx_reminders_time ON event_reminders(reminder_time);
CREATE INDEX IF NOT EXISTS idx_reminders_is_sent ON event_reminders(is_sent);
CREATE INDEX IF NOT EXISTS idx_reminders_pending ON event_reminders(is_sent, reminder_time);

-- Insert sample data (only if tables are empty)
INSERT OR IGNORE INTO calendars (name, description, color, user_id, is_default, is_shared, created_time, updated_time) VALUES
('My Calendar', 'Default personal calendar', '#007bff', 1, 1, 0, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('Work Calendar', 'Work related events', '#28a745', 1, 0, 0, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('Shared Calendar', 'Team shared calendar', '#17a2b8', 1, 0, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00');

INSERT OR IGNORE INTO calendar_events (title, description, start_time, end_time, is_all_day, location, event_type, priority, calendar_id, creator_id, created_time, updated_time) VALUES
('Team Meeting', 'Weekly team meeting to discuss project progress', '2025-05-30 09:00:00', '2025-05-30 10:00:00', 0, 'Conference Room A', 'MEETING', 'NORMAL', 1, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('Project Deadline', 'Final project submission deadline', '2025-06-01 23:59:59', NULL, 1, NULL, 'TASK', 'HIGH', 2, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('Client Visit', 'Visit important client for partnership discussion', '2025-05-31 14:00:00', '2025-05-31 16:00:00', 0, 'Client Office', 'APPOINTMENT', 'HIGH', 2, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00');

INSERT OR IGNORE INTO event_reminders (event_id, reminder_time, reminder_type, message, created_time) VALUES
(1, '2025-05-30 08:45:00', 'NOTIFICATION', 'Team meeting in 15 minutes', '2025-01-01 00:00:00'),
(1, '2025-05-30 08:30:00', 'EMAIL', 'Team meeting in 30 minutes, please prepare materials', '2025-01-01 00:00:00'),
(2, '2025-06-01 09:00:00', 'NOTIFICATION', 'Today is project deadline, please submit on time', '2025-01-01 00:00:00'),
(3, '2025-05-31 13:45:00', 'NOTIFICATION', 'Client visit in 15 minutes, time to leave', '2025-01-01 00:00:00');
