package com.mylog.controller.api;

import com.mylog.service.UserSessionTempFileTracker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.AccessDeniedException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 文件上传API控制器
 * 用于处理文件上传进度监控和临时文件管理
 */
@RestController
@RequestMapping(value = "/api/files", produces = MediaType.APPLICATION_JSON_VALUE)
public class FileUploadApiController {
    
    private static final Logger logger = LoggerFactory.getLogger(FileUploadApiController.class);
    
    // 时间戳格式化器
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    @Value("${mylog.data.path:data}")
    private String dataPath;
    
    // 注入会话跟踪器
    private final UserSessionTempFileTracker sessionTracker;
    
    public FileUploadApiController(UserSessionTempFileTracker sessionTracker) {
        this.sessionTracker = sessionTracker;
    }

    /**
     * 临时文件上传端点
     * 用于表单提交前的文件上传
     *
     * @param file 上传的文件
     * @param request HTTP请求（用于获取会话ID）
     * @return 上传结果
     */
    @PostMapping("/upload-temp")
    public ResponseEntity<Map<String, Object>> uploadTemp(@RequestParam("file") MultipartFile file,
                                                         HttpServletRequest request) {
        // 获取当前用户和会话
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        String sessionId = request.getSession().getId();

        logger.info("接收到临时文件上传请求: 用户={}, 会话={}, 文件名={}, 文件大小={}KB",
                currentUsername, sessionId, file.getOriginalFilename(), file.getSize() / 1024);

        Map<String, Object> response = new HashMap<>();

        try {
            if (file.isEmpty()) {
                response.put("success", Boolean.FALSE);
                response.put("message", "上传的文件为空");
                return ResponseEntity.ok(response);
            }

            // 获取文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            // 生成临时文件名，包含用户名以便于清理
            String timestamp = LocalDateTime.now().format(FORMATTER);
            String randomId = UUID.randomUUID().toString().substring(0, 8);
            String tempFilename = "temp_" + timestamp + "_" + currentUsername + "_" + randomId + fileExtension;

            // 确保临时目录存在
            Path tempDir = Paths.get(dataPath, "temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }

            // 保存临时文件
            Path tempFilePath = tempDir.resolve(tempFilename);
            Files.copy(file.getInputStream(), tempFilePath);
            
            // 跟踪会话临时文件
            sessionTracker.trackTempFile(sessionId, currentUsername, tempFilePath.toString());

            // 对小文件添加短暂延迟，确保进度条显示正常
            if (file.getSize() < 1024 * 1024) { // 如果文件小于1MB
                Thread.sleep(300);
            }

            // 设置响应
            response.put("success", Boolean.TRUE);
            response.put("message", "文件上传成功");
            response.put("tempFilePath", tempFilePath.toString());
            response.put("fileName", originalFilename);
            response.put("fileSize", file.getSize());

            logger.info("临时文件上传成功: {}, 响应: {}", tempFilePath, response);

            return ResponseEntity.ok(response);

        } catch (IOException e) {
            logger.error("保存临时文件时出错", e);
            response.put("success", Boolean.FALSE);
            response.put("message", "保存临时文件失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.ok(response);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("文件上传过程被中断", e);
            response.put("success", Boolean.FALSE);
            response.put("message", "文件上传被中断");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("临时文件上传时出现未预期的错误", e);
            response.put("success", Boolean.FALSE);
            response.put("message", "文件上传失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 删除临时文件
     *
     * @param filePath 临时文件路径
     * @param request HTTP请求（用于获取会话ID）
     * @return 删除结果
     */
    @DeleteMapping("/delete-temp")
    public ResponseEntity<Map<String, Object>> deleteTemp(@RequestParam("filePath") String filePath,
                                                         HttpServletRequest request) {
        // 获取当前用户和会话
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        String sessionId = request.getSession().getId();

        logger.info("接收到删除临时文件请求: 用户={}, 会话={}, 文件路径={}", 
                   currentUsername, sessionId, filePath);

        Map<String, Object> response = new HashMap<>();

        try {
            // 检查文件路径是否为空
            if (filePath == null || filePath.isEmpty()) {
                response.put("success", Boolean.FALSE);
                response.put("message", "文件路径为空");
                return ResponseEntity.ok(response);
            }

            // 检查文件路径是否在临时目录中
            Path tempDir = Paths.get(dataPath, "temp");
            Path tempFile = Paths.get(filePath);

            if (!tempFile.normalize().startsWith(tempDir.normalize())) {
                logger.warn("尝试删除非临时目录中的文件: {}", filePath);
                response.put("success", Boolean.FALSE);
                response.put("message", "只能删除临时目录中的文件");
                return ResponseEntity.ok(response);
            }

            // 删除文件 - 处理并发删除情况
            if (Files.exists(tempFile)) {
                try {
                    Files.delete(tempFile);
                    logger.info("临时文件删除成功: {}", tempFile);
                    
                    // 从会话跟踪器中移除文件记录
                    sessionTracker.removeTempFile(sessionId, filePath);

                    response.put("success", Boolean.TRUE);
                    response.put("message", "临时文件删除成功");
                } catch (AccessDeniedException e) {
                    // 并发删除的情况 - 文件可能已被其他线程删除
                    if (!Files.exists(tempFile)) {
                        logger.info("临时文件已被其他线程删除: {}", tempFile);
                        // 仍然从会话跟踪器中移除文件记录
                        sessionTracker.removeTempFile(sessionId, filePath);
                        response.put("success", Boolean.TRUE);
                        response.put("message", "临时文件删除成功");
                    } else {
                        logger.warn("删除临时文件时访问被拒绝: {}", tempFile, e);
                        response.put("success", Boolean.FALSE);
                        response.put("message", "删除临时文件失败: 访问被拒绝");
                    }
                } catch (IOException e) {
                    logger.error("删除临时文件时IO错误: {}", tempFile, e);
                    response.put("success", Boolean.FALSE);
                    response.put("message", "删除临时文件失败: " + e.getMessage());
                }
            } else {
                logger.info("临时文件不存在，可能已被删除: {}", tempFile);
                // 文件不存在时仍然从会话跟踪器中移除记录，确保数据一致性
                sessionTracker.removeTempFile(sessionId, filePath);
                response.put("success", Boolean.TRUE);
                response.put("message", "临时文件删除成功");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("删除临时文件时出错", e);
            response.put("success", Boolean.FALSE);
            response.put("message", "删除临时文件失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 删除临时文件 (POST方法，用于支持sendBeacon API)
     *
     * @param filePath 临时文件路径
     * @param request HTTP请求（用于获取会话ID）
     * @return 删除结果
     */
    @PostMapping("/delete-temp")
    public ResponseEntity<Map<String, Object>> deleteTempPost(@RequestParam("filePath") String filePath,
                                                             HttpServletRequest request) {
        // 复用DELETE方法的逻辑
        return deleteTemp(filePath, request);
    }
}