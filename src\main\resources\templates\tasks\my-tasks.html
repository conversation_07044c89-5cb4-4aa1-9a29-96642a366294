<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('我的订单任务')}">
    <meta charset="UTF-8">
    <title>我的订单任务</title>
    <script th:inline="javascript">
        // 将视觉类型列表保存为全局变量
        const visionTypeList = /*[[${visionTypes}]]*/ [];
        console.log('视觉类型列表:', visionTypeList);

        // 将人员列表保存为全局变量
        const personnelList = /*[[${personnel}]]*/ [];
        console.log('人员列表:', personnelList);

        // 更新值字段
        function updateValueField(selectField) {
            const valueContainer = selectField.closest('.search-condition').querySelector('.value-container');
            const selectedField = selectField.value;

            if (!selectedField) {
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            // 获取当前值（如果存在）
            const currentValue = valueContainer.querySelector('input, select')?.value || '';

            // 创建隐藏的字段名称输入
            let hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            hiddenField.value = selectedField;

            // 根据字段类型创建不同的输入控件
            let inputField;

            switch (selectedField) {
                case 'status':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    const statusOptions = [
                        {value: '未开始', text: '未开始'},
                        {value: '进行中', text: '进行中'},
                        {value: '已完成', text: '已完成'},
                        {value: '已暂停', text: '已暂停'}
                    ];

                    // 添加选项
                    statusOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = currentValue;
                    break;
                case 'risk':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    const riskOptions = [
                        {value: '正常', text: '正常'},
                        {value: '中', text: '中'},
                        {value: '高', text: '高'}
                    ];

                    // 添加选项
                    riskOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = currentValue;
                    break;
                case 'visionType':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    // 添加空选项
                    const emptyVisionOption = document.createElement('option');
                    emptyVisionOption.value = '';
                    emptyVisionOption.textContent = '请选择视觉类型';
                    inputField.appendChild(emptyVisionOption);

                    console.log('更新视觉类型字段，视觉类型列表:', visionTypeList);

                    // 使用全局变量 visionTypeList
                    if (visionTypeList && visionTypeList.length > 0) {
                        visionTypeList.forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            inputField.appendChild(typeOption);
                        });
                    } else {
                        console.warn('视觉类型列表为空或不存在');
                        // 使用备选值
                        ['2D', '3D', 'OCR', '其他'].forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            inputField.appendChild(typeOption);
                        });
                    }
                    break;
                default:
                    inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'form-control';
                    inputField.name = 'field_' + selectedField;
                    inputField.placeholder = '请输入';
                    inputField.value = currentValue;
            }

            // 清空并添加新的输入字段
            valueContainer.innerHTML = '';
            valueContainer.appendChild(hiddenField);
            valueContainer.appendChild(inputField);
        }
    </script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script#customScript})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">我的订单任务 <small class="fs-6"><span class="badge bg-primary rounded-pill" th:text="'进行中 ' + ${inProgressTaskCount}">进行中 23</span>/<span th:text="${taskPage.totalElements}">52</span></small></h1>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">任务搜索</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <!-- 高级搜索 -->
                            <form th:action="@{/tasks/advanced-search}" method="get" class="row g-3" id="advancedSearchForm">
                                <!-- 返回位置参数 -->
                                <input type="hidden" name="returnTo" value="tasks">
                                <!-- 动态搜索条件 -->
                                <div id="searchConditions">
                                    <div class="search-condition row mb-3">
                                        <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                                            <select class="form-select search-field" onchange="updateValueField(this)">
                                                <option value="">选择字段</option>
                                                <option value="taskName">任务名称</option>
                                                <option value="status">状态</option>
                                                <option value="risk">风险等级</option>
                                                <option value="projectName">所属项目</option>
                                                <option value="customerName">客户名称</option>
                                                <option value="visionType">视觉类型</option>
                                                <option value="taskStage">任务阶段</option>
                                            </select>
                                        </div>
                                        <div class="col-10 col-sm-5 col-md-7 value-container">
                                            <!-- 值输入框将根据选择的字段动态生成 -->
                                            <input type="text" class="form-control search-value" disabled placeholder="请先选择字段">
                                        </div>
                                        <div class="col-2 col-sm-1 col-md-2">
                                            <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按钮组 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-outline-primary" onclick="addSearchCondition()">
                                                <i class="bi bi-plus"></i> 添加条件
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" onclick="addTimeCondition()">
                                                <i class="bi bi-calendar"></i> 添加创建时间条件
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-search"></i> 应用筛选
                                            </button>
                                            <a th:href="@{/tasks/order-tasks}" class="btn btn-outline-secondary">
                                                <i class="bi bi-arrow-counterclockwise"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">任务列表</h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#taskListCollapse" aria-expanded="true" aria-controls="taskListCollapse">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="taskListCollapse">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20%; white-space: nowrap;">任务名称</th>
                                    <th style="width: 20%; white-space: nowrap;">所属项目</th>
                                    <th style="width: 6%; white-space: nowrap;">风险</th>
                                    <th style="width: 12%; white-space: nowrap;">进度</th>
                                    <th style="width: 8%; white-space: nowrap;">额定工期</th>
                                    <th style="width: 8%; white-space: nowrap;">累计工期</th>
                                    <th style="width: 8%; white-space: nowrap;">剩余工期</th>
                                    <th style="width: 6%; white-space: nowrap;">状态</th>
                                    <th style="width: 5%; white-space: nowrap;">评论</th>
                                    <th style="width: 7%; white-space: nowrap;">创建时间</th>
                                </tr>
                            </thead>
                            <tbody>                                <!-- 无数据提示 -->
                                <tr th:if="${taskPage.content.empty}">
                                    <td colspan="10" class="text-center">暂无任务数据</td>
                                </tr>

                                <!-- 数据行 -->
                                <tr th:each="task : ${taskPage.content}">
                                    <td>
                                        <a th:href="@{/tasks/{id}(id=${task.taskId})}" th:text="${task.taskName}">任务名称</a>
                                    </td>
                                    <td>
                                        <a th:if="${task.project != null}"
                                           th:href="@{/projects/{id}(id=${task.projectId})}"
                                           th:text="${task.project.projectName}">
                                            项目名称
                                        </a>
                                        <span th:if="${task.project == null && task.projectId != null}"
                                              class="text-danger">项目不存在</span>
                                        <span th:if="${task.projectId == null}"
                                              class="text-muted">-</span>
                                    </td>
                                    <td>
                                        <span th:class="${'badge ' +
                                            (task.risk == '高' ? 'bg-danger' :
                                            (task.risk == '中' ? 'bg-warning' : 'bg-success'))}"
                                            th:text="${task.risk}">风险</span>
                                    </td>                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar"
                                                 th:style="'width: ' + (${task.progress} ?: 0) + '%'"
                                                 th:class="${'progress-bar ' +
                                                    ((task.progress ?: 0) <= 30 ? 'bg-danger' :
                                                    ((task.progress ?: 0) <= 70 ? 'bg-warning' : 'bg-success'))}"
                                                 th:text="(${task.progress} ?: 0) + '%'">0%</div>
                                        </div>
                                    </td>
                                    <td th:text="${task.ratedDurationDays != null ? #numbers.formatDecimal(task.ratedDurationDays, 1, 2) + ' 天' : '-'}" style="white-space: nowrap;">-</td>
                                    <td th:text="${task.cumulativeDurationDays != null ? #numbers.formatDecimal(task.cumulativeDurationDays, 1, 2) + ' 天' : '-'}" style="white-space: nowrap;">-</td>
                                    <td th:text="${task.remainingDurationDays != null ? #numbers.formatDecimal(task.remainingDurationDays, 1, 2) + ' 天' : '-'}"
                                        th:style="${task.remainingDurationDays != null && task.remainingDurationDays < 0 ? 'white-space: nowrap; color: red; font-weight: bold;' : 'white-space: nowrap;'}"
                                        style="white-space: nowrap;">-</td>
                                    <td>
                                        <span th:class="${'badge ' +
                                            (task.status == '进行中' ? 'bg-primary' :
                                            (task.status == '已完成' ? 'bg-success' :
                                            (task.status == '未开始' ? 'bg-secondary' :
                                            (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                            th:text="${task.status}">状态</span>
                                    </td>
                                    <td>
                                        <span th:if="${task.commentDays != null}"
                                              th:class="${'badge ' +
                                                (task.commentDays >= 7 ? 'bg-danger' :
                                                (task.commentDays >= 4 ? 'bg-warning' :
                                                (task.commentDays >= 0 ? 'bg-success' : 'bg-dark')))}"
                                              th:text="${task.commentDays}">0.0</span>
                                        <span th:unless="${task.commentDays != null}">-</span>
                                    </td>
                                    <td th:text="${task.createdDateTime != null ? #temporals.format(task.createdDateTime, 'yyyy-MM-dd') : '-'}" style="white-space: nowrap;">2025-01-01</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- 分页控件 -->
                <div class="card-footer" th:if="${taskPage.totalPages > 0}">
                    <div th:replace="~{fragments/pagination :: pagination(${taskPage}, @{/tasks/order-tasks})}"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/history-tracker.js}"></script>    <!-- 添加备用的分页处理脚本 -->
    <script id="customScript" th:inline="javascript">
        // 更新值字段
        function updateValueField(selectField) {
            console.log('执行updateValueField函数，选择的字段:', selectField.value);
            const valueContainer = selectField.closest('.search-condition').querySelector('.value-container');
            const selectedField = selectField.value;

            if (!selectedField) {
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            // 获取当前值（如果存在）
            const currentValue = valueContainer.querySelector('input, select')?.value || '';
            console.log('当前值:', currentValue);

            // 创建隐藏的字段名称输入
            let hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            hiddenField.value = selectedField;

            // 根据字段类型创建不同的输入控件
            let inputField;

            switch (selectedField) {
                case 'status':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    const statusOptions = [
                        {value: '未开始', text: '未开始'},
                        {value: '进行中', text: '进行中'},
                        {value: '已完成', text: '已完成'},
                        {value: '已暂停', text: '已暂停'}
                    ];

                    // 添加选项
                    statusOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = currentValue;
                    break;
                case 'risk':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    const riskOptions = [
                        {value: '正常', text: '正常'},
                        {value: '中', text: '中'},
                        {value: '高', text: '高'}
                    ];

                    // 添加选项
                    riskOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = currentValue;
                    break;
                case 'visionType':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + selectedField;

                    // 添加空选项
                    const emptyVisionOption = document.createElement('option');
                    emptyVisionOption.value = '';
                    emptyVisionOption.textContent = '请选择视觉类型';
                    inputField.appendChild(emptyVisionOption);

                    console.log('更新视觉类型字段，视觉类型列表:', visionTypeList);
                    console.log('视觉类型列表长度:', visionTypeList.length);
                    console.log('视觉类型列表内容详细:', JSON.stringify(visionTypeList));

                    // 使用全局变量 visionTypeList
                    if (visionTypeList && visionTypeList.length > 0) {
                        visionTypeList.forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            inputField.appendChild(typeOption);
                        });
                    } else {
                        console.warn('视觉类型列表为空或不存在');
                        // 使用备选值
                        ['2D', '3D', 'OCR', '其他'].forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            inputField.appendChild(typeOption);
                        });
                    }
                    break;
                case 'taskStage':
                    // 创建任务阶段范围输入
                    const rangeContainer = document.createElement('div');
                    rangeContainer.className = 'row';

                    // 最小值输入框
                    const minContainer = document.createElement('div');
                    minContainer.className = 'col-6';
                    const minGroup = document.createElement('div');
                    minGroup.className = 'input-group';

                    const minLabel = document.createElement('span');
                    minLabel.className = 'input-group-text';
                    minLabel.textContent = '最小值';

                    const minInput = document.createElement('input');
                    minInput.type = 'number';
                    minInput.className = 'form-control';
                    minInput.name = 'field_taskStage_min';
                    minInput.min = '0';
                    minInput.placeholder = '0';

                    minGroup.appendChild(minLabel);
                    minGroup.appendChild(minInput);
                    minContainer.appendChild(minGroup);

                    // 最大值输入框
                    const maxContainer = document.createElement('div');
                    maxContainer.className = 'col-6';
                    const maxGroup = document.createElement('div');
                    maxGroup.className = 'input-group';

                    const maxLabel = document.createElement('span');
                    maxLabel.className = 'input-group-text';
                    maxLabel.textContent = '最大值';

                    const maxInput = document.createElement('input');
                    maxInput.type = 'number';
                    maxInput.className = 'form-control';
                    maxInput.name = 'field_taskStage_max';
                    maxInput.min = '0';
                    maxInput.placeholder = '不限';

                    maxGroup.appendChild(maxLabel);
                    maxGroup.appendChild(maxInput);
                    maxContainer.appendChild(maxGroup);

                    // 组装范围容器
                    rangeContainer.appendChild(minContainer);
                    rangeContainer.appendChild(maxContainer);

                    // 创建隐藏的字段名称输入
                    const hiddenTaskStageField = document.createElement('input');
                    hiddenTaskStageField.type = 'hidden';
                    hiddenTaskStageField.name = 'fieldNames';
                    hiddenTaskStageField.value = 'taskStage';

                    // 清空并添加新的输入字段
                    valueContainer.innerHTML = '';
                    valueContainer.appendChild(hiddenTaskStageField);
                    valueContainer.appendChild(rangeContainer);

                    return; // 直接返回，不执行后面的代码
                default:
                    inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'form-control';
                    inputField.name = 'field_' + selectedField;
                    inputField.placeholder = '请输入' + selectField.options[selectField.selectedIndex].text;
                    inputField.value = '';
            }

            // 清空并添加新的输入字段
            valueContainer.innerHTML = '';
            valueContainer.appendChild(hiddenField);
            valueContainer.appendChild(inputField);
            console.log('已更新值字段容器');
        }

        // 添加搜索条件
        function addSearchCondition() {
            const template = document.querySelector('.search-condition').cloneNode(true);
            template.querySelector('.search-field').value = '';
            let valueContainer = template.querySelector('.value-container');
            valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';

            document.getElementById('searchConditions').appendChild(template);
        }

        // 添加创建时间条件
        function addTimeCondition() {
            const conditions = document.getElementById('searchConditions');
            const timeCondition = document.createElement('div');
            timeCondition.className = 'search-condition row mb-3';
            timeCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select" name="fieldNames" disabled>
                        <option value="createdDate" selected>创建日期</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="createdDate">
                </div>
                <div class="col-10 col-sm-5 col-md-7">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">从</span>
                                <input type="date" class="form-control" name="field_createdDate_start">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" name="field_createdDate_end">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(timeCondition);
        }

        // 移除条件
        function removeCondition(button) {
            const condition = button.closest('.search-condition');

            // 如果只有一个搜索条件，不删除，只重置
            if (document.querySelectorAll('.search-condition').length <= 1) {
                condition.querySelector('.search-field').value = '';
                let valueContainer = condition.querySelector('.value-container');
                valueContainer.innerHTML = '<input type="text" class="form-control search-value" disabled placeholder="请先选择字段">';
                return;
            }

            condition.remove();
        }

        // 分页处理和表单验证
        document.addEventListener('DOMContentLoaded', function() {
            // 为表单添加提交验证
            document.getElementById('advancedSearchForm').addEventListener('submit', function(e) {
                // 检查是否有有效的搜索条件
                const hasValidCondition = Array.from(document.querySelectorAll('.search-field'))
                    .some(select => select.value !== '');

                // 检查是否有特殊条件（日期条件）
                const hasSpecialCondition =
                    document.querySelectorAll('input[name="fieldNames"][value="createdDate"]').length > 0;

                if (!hasValidCondition && !hasSpecialCondition) {
                    e.preventDefault();
                    alert('请至少选择一个搜索字段');
                    return false;
                }

                // 确保表单提交到正确的URL
                this.action = window.location.origin + '/tasks/advanced-search';

                // 添加返回位置参数
                const returnToField = document.querySelector('input[name="returnTo"]');
                if (!returnToField) {
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'returnTo';
                    hiddenField.value = 'tasks';
                    this.appendChild(hiddenField);
                }

                return true;
            });

            // 恢复搜索条件（如果存在）
            restoreSearchConditions();
        });

        // 恢复搜索条件
        function restoreSearchConditions() {
            // 检查URL是否包含搜索参数
            const url = new URL(window.location.href);
            const params = url.searchParams;
            const searchParams = {};

            // 收集所有搜索参数
            for (const [key, value] of params.entries()) {
                if (key.startsWith('field_') || key === 'fieldNames') {
                    searchParams[key] = value;
                }
            }

            // 如果没有搜索参数则不处理
            if (Object.keys(searchParams).length === 0) {
                return;
            }

            console.log('恢复搜索条件:', searchParams);

            // 获取所有字段名
            const fieldNames = params.getAll('fieldNames');
            if (!fieldNames || fieldNames.length === 0) {
                return;
            }

            // 移除默认的空搜索条件
            const searchConditions = document.getElementById('searchConditions');
            while (searchConditions.firstChild) {
                searchConditions.removeChild(searchConditions.firstChild);
            }

            // 重建搜索条件
            fieldNames.forEach(fieldName => {
                // 处理时间范围条件
                if (fieldName === 'createdDate') {
                    addTimeConditionWithValues(
                        params.get('field_createdDate_start'),
                        params.get('field_createdDate_end')
                    );
                    return;
                }

                // 处理任务阶段条件
                if (fieldName === 'taskStage') {
                    const minValue = params.get('field_taskStage_min');
                    const maxValue = params.get('field_taskStage_max');
                    console.log('恢复任务阶段条件:', { min: minValue, max: maxValue });
                    addTaskStageConditionWithValues(
                        minValue,
                        maxValue
                    );
                    return;
                }

                // 处理其他条件
                const fieldValue = params.get('field_' + fieldName);
                if (fieldValue) {
                    addSearchConditionWithValues(fieldName, fieldValue);
                }
            });
        }

        // 使用指定的值添加搜索条件
        function addSearchConditionWithValues(fieldName, fieldValue) {
            const searchConditions = document.getElementById('searchConditions');
            const condition = document.createElement('div');
            condition.className = 'search-condition row mb-3';

            // 创建字段选择下拉框
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';

            const select = document.createElement('select');
            select.className = 'form-select search-field';
            select.setAttribute('onchange', 'updateValueField(this)');
            select.innerHTML = `
                <option value="">选择字段</option>
                <option value="taskName">任务名称</option>
                <option value="status">状态</option>
                <option value="risk">风险等级</option>
                <option value="projectName">所属项目</option>
                <option value="customerName">客户名称</option>
                <option value="visionType">视觉类型</option>
                <option value="taskStage">任务阶段</option>
            `;
            select.value = fieldName;
            fieldCol.appendChild(select);

            // 创建值容器
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7 value-container';

            // 创建隐藏字段名称输入
            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            hiddenField.value = fieldName;
            valueCol.appendChild(hiddenField);

            // 根据字段类型创建不同的输入控件
            let inputField;

            switch (fieldName) {
                case 'status':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + fieldName;

                    const statusOptions = [
                        {value: '未开始', text: '未开始'},
                        {value: '进行中', text: '进行中'},
                        {value: '已完成', text: '已完成'},
                        {value: '已暂停', text: '已暂停'}
                    ];

                    // 添加选项
                    statusOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = fieldValue;
                    break;
                case 'risk':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + fieldName;

                    const riskOptions = [
                        {value: '正常', text: '正常'},
                        {value: '中', text: '中'},
                        {value: '高', text: '高'}
                    ];

                    // 添加选项
                    riskOptions.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option.value;
                        optionEl.textContent = option.text;
                        inputField.appendChild(optionEl);
                    });
                    inputField.value = fieldValue;
                    break;
                case 'visionType':
                    inputField = document.createElement('select');
                    inputField.className = 'form-select';
                    inputField.name = 'field_' + fieldName;

                    // 添加空选项
                    const emptyVisionOption = document.createElement('option');
                    emptyVisionOption.value = '';
                    emptyVisionOption.textContent = '请选择视觉类型';
                    inputField.appendChild(emptyVisionOption);

                    console.log('恢复视觉类型条件，视觉类型列表:', visionTypeList);

                    // 使用全局变量 visionTypeList
                    if (visionTypeList && visionTypeList.length > 0) {
                        visionTypeList.forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            typeOption.selected = (type === fieldValue);
                            inputField.appendChild(typeOption);
                        });
                    } else {
                        console.warn('视觉类型列表为空或不存在');
                        // 使用备选值
                        ['2D', '3D', 'OCR', '其他'].forEach(type => {
                            const typeOption = document.createElement('option');
                            typeOption.value = type;
                            typeOption.textContent = type;
                            typeOption.selected = (type === fieldValue);
                            inputField.appendChild(typeOption);
                        });
                    }
                    break;
                default:
                    inputField = document.createElement('input');
                    inputField.type = 'text';
                    inputField.className = 'form-control';
                    inputField.name = 'field_' + fieldName;
                    inputField.placeholder = '请输入';
                    inputField.value = fieldValue;
            }

            valueCol.appendChild(inputField);

            // 创建删除按钮
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';

            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-outline-danger w-100';
            deleteBtn.setAttribute('onclick', 'removeCondition(this)');
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            btnCol.appendChild(deleteBtn);

            // 组装条件行
            condition.appendChild(fieldCol);
            condition.appendChild(valueCol);
            condition.appendChild(btnCol);

            // 添加到搜索条件容器
            searchConditions.appendChild(condition);
        }

        // 带有指定值添加时间条件
        function addTimeConditionWithValues(startDate, endDate) {
            const conditions = document.getElementById('searchConditions');
            const timeCondition = document.createElement('div');
            timeCondition.className = 'search-condition row mb-3';
            timeCondition.innerHTML = `
                <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
                    <select class="form-select" name="fieldNames" disabled>
                        <option value="createdDate" selected>创建日期</option>
                    </select>
                    <input type="hidden" name="fieldNames" value="createdDate">
                </div>
                <div class="col-10 col-sm-5 col-md-7">
                    <div class="row">
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">从</span>
                                <input type="date" class="form-control" name="field_createdDate_start" value="${startDate || ''}">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="input-group">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" name="field_createdDate_end" value="${endDate || ''}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-2 col-sm-1 col-md-2">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCondition(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            conditions.appendChild(timeCondition);
        }

        // 带有指定值添加任务阶段条件
        function addTaskStageConditionWithValues(minStage, maxStage) {
            const conditions = document.getElementById('searchConditions');
            const stageCondition = document.createElement('div');
            stageCondition.className = 'search-condition row mb-3';

            // 创建字段选择列
            const fieldCol = document.createElement('div');
            fieldCol.className = 'col-12 col-sm-6 col-md-3 mb-2 mb-sm-0';

            // 创建可操作的下拉框
            const select = document.createElement('select');
            select.className = 'form-select search-field';
            select.setAttribute('onchange', 'updateValueField(this)');
            select.innerHTML = `
                <option value="">选择字段</option>
                <option value="taskName">任务名称</option>
                <option value="status">状态</option>
                <option value="risk">风险等级</option>
                <option value="projectName">所属项目</option>
                <option value="customerName">客户名称</option>
                <option value="visionType">视觉类型</option>
                <option value="taskStage" selected>任务阶段</option>
            `;
            fieldCol.appendChild(select);

            // 创建值容器
            const valueCol = document.createElement('div');
            valueCol.className = 'col-10 col-sm-5 col-md-7 value-container';

            // 创建隐藏字段名称输入
            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'fieldNames';
            hiddenField.value = 'taskStage';
            valueCol.appendChild(hiddenField);

            // 创建任务阶段范围输入
            const rangeContainer = document.createElement('div');
            rangeContainer.className = 'row';

            // 最小值输入框
            const minContainer = document.createElement('div');
            minContainer.className = 'col-6';
            const minGroup = document.createElement('div');
            minGroup.className = 'input-group';

            const minLabel = document.createElement('span');
            minLabel.className = 'input-group-text';
            minLabel.textContent = '最小值';

            const minInput = document.createElement('input');
            minInput.type = 'number';
            minInput.className = 'form-control';
            minInput.name = 'field_taskStage_min';
            minInput.min = '0';
            minInput.placeholder = '0';
            minInput.value = minStage || '';

            minGroup.appendChild(minLabel);
            minGroup.appendChild(minInput);
            minContainer.appendChild(minGroup);

            // 最大值输入框
            const maxContainer = document.createElement('div');
            maxContainer.className = 'col-6';
            const maxGroup = document.createElement('div');
            maxGroup.className = 'input-group';

            const maxLabel = document.createElement('span');
            maxLabel.className = 'input-group-text';
            maxLabel.textContent = '最大值';

            const maxInput = document.createElement('input');
            maxInput.type = 'number';
            maxInput.className = 'form-control';
            maxInput.name = 'field_taskStage_max';
            maxInput.min = '0';
            maxInput.placeholder = '不限';
            maxInput.value = maxStage || '';

            maxGroup.appendChild(maxLabel);
            maxGroup.appendChild(maxInput);
            maxContainer.appendChild(maxGroup);

            // 组装范围容器
            rangeContainer.appendChild(minContainer);
            rangeContainer.appendChild(maxContainer);
            valueCol.appendChild(rangeContainer);

            // 创建删除按钮
            const btnCol = document.createElement('div');
            btnCol.className = 'col-2 col-sm-1 col-md-2';

            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-outline-danger';
            deleteBtn.setAttribute('onclick', 'removeCondition(this)');
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            btnCol.appendChild(deleteBtn);

            // 组装条件行
            stageCondition.appendChild(fieldCol);
            stageCondition.appendChild(valueCol);
            stageCondition.appendChild(btnCol);

            conditions.appendChild(stageCondition);

            // 添加事件监听器
            select.addEventListener('change', function() {
                updateValueField(this);
            });
        }

        // 确保分页链接正常工作
        window.addEventListener('load', function() {
            console.log('order-tasks页面完全加载');

            // 直接修改链接的href属性，无需依赖click事件
            $('.pagination-link').each(function() {
                const $link = $(this);
                const page = $link.attr('data-page');

                if (page && $link.attr('href') !== '#') {
                    // 创建备用链接
                    const currentUrl = new URL(window.location.href);

                    // 保留路径，特别是对于高级搜索来说
                    const newUrl = new URL($link.attr('href'));

                    // 从当前URL复制所有参数到新URL
                    currentUrl.searchParams.forEach((value, key) => {
                        if (key !== 'page') { // 避免覆盖页码参数
                            newUrl.searchParams.set(key, value);
                        }
                    });

                    // 设置新的href属性
                    $link.attr('href', newUrl.toString());
                    console.log('更新备用链接:', newUrl.toString());
                }
            });

            // 仍然保留点击事件以防通过href导航失败
            $(document).on('click', '.pagination-link', function(e) {
                if ($(this).attr('href') === '#') {
                    e.preventDefault();
                    return;
                }

                // 如果href属性已经设置并有效，不阻止默认行为，使用正常链接导航
                // 但如果是另一个域的链接，我们还是应该处理
                const linkUrl = new URL($(this).attr('href'), window.location.origin);
                if (linkUrl.origin !== window.location.origin) {
                    e.preventDefault();

                    // 获取目标页码
                    const page = $(this).attr('data-page');
                    if (!page) return;

                    // 构建新URL
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('page', page);

                    // 跳转
                    window.location.href = currentUrl.toString();
                }
            });
        });
    </script>
</body>
</html>