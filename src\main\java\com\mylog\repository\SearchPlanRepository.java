package com.mylog.repository;

import com.mylog.model.SearchPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface SearchPlanRepository extends JpaRepository<SearchPlan, Long> {
    
    /**
     * 根据用户名查找搜索方案
     * @param username 用户名
     * @return 该用户的所有搜索方案
     */
    List<SearchPlan> findByUsernameOrderByCreatedDateDesc(String username);
    
    /**
     * 根据用户名和方案名称查找搜索方案
     * @param username 用户名
     * @param name 方案名称
     * @return 匹配的搜索方案
     */
    List<SearchPlan> findByUsernameAndName(String username, String name);
} 