package com.mylog.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mylog.service.OptionsService;

/**
 * 系统选项初始化器，用于在应用启动时预加载所有选项
 * 注意：这是唯一负责预加载选项的组件，其他组件中的预加载操作已移除
 */
@Component
@Order(3) // 在其他初始化器之后运行
public class OptionsInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(OptionsInitializer.class);

    @Autowired
    private OptionsService optionsService;

    @Override
    public void run(String... args) throws Exception {
        logger.info("开始初始化系统选项，预加载所有选项到缓存中");

        try {
            // 唯一的预加载操作点
            optionsService.preloadAllOptions();
            logger.info("系统选项预加载完成");
        } catch (Exception e) {
            logger.error("初始化系统选项时发生错误: {}", e.getMessage(), e);
        }
    }
}