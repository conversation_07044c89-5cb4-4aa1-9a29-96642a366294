package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Entity
@Table(name = "Messages")
@Data
public class Message {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final ZoneId SYSTEM_ZONE = ZoneId.systemDefault();
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long messageId;
    
    @Column(nullable = false)
    private String receiver;
    
    @Column(nullable = false)
    private String messageTitle;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String messageContent;
    
    @Column(nullable = false)
    private String relatedType; // Project, Task, SubTask
    
    @Column(nullable = false)
    private Long relatedId;
    
    @Column(nullable = false)
    private boolean isRead = false;
    
    @Column(name = "CreatedDate", nullable = false, columnDefinition = "TEXT")
    private String createdDateStr;
    
    @Transient
    private LocalDateTime createdDate;
    
    /**
     * 姒涙顓婚弸鍕偓鐘插毐閺佸府绱濋崚婵嗩潗閸栨牕鍨卞鐑樻）閺?
     */
    public Message() {
        // 鐠佸墽鐤嗘妯款吇閻ㄥ嫬鍨卞鐑樻）閺?
        setCreatedDate(LocalDateTime.now());
    }
    
    /**
     * 鐟欙絾鐎介弮銉︽埂鐎涙顑佹稉韫礋 LocalDateTime 鐎电钖?
     * @param dateStr 閺冦儲婀＄€涙顑佹稉?
     * @return 鐟欙絾鐎介崥搴ｆ畱 LocalDateTime 鐎电钖勯敍灞筋洤閺嬫粏袙閺嬫劕銇戠拹銉ュ灟鏉╂柨娲?null
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null) {
            return null;
        }
        try {
            // 妫ｆ牕鍘涚亸婵婄槸鐟欙絾鐎介弽鍥у櫙閺嶇厧绱?
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                // 婵″倹鐏夌憴锝嗙€芥径杈Е閿涘苯鐨剧拠鏇炵殺閸忔湹缍旀稉鐑樻闂傚瓨鍩戞径鍕倞
                long timestamp = Long.parseLong(dateStr);
                return LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(timestamp),
                    SYSTEM_ZONE
                );
            } catch (NumberFormatException ex) {
                return null;
            }
        }
    }
    
    /**
     * 閺嶇厧绱￠崠?LocalDateTime 鐎电钖勬稉鍝勭摟缁楋缚瑕?
     * @param dateTime LocalDateTime 鐎电钖?
     * @return 閺嶇厧绱￠崠鏍ф倵閻ㄥ嫬鐡х粭锔胯閿涘苯顩ч弸?dateTime 娑?null 閸掓瑨绻戦崶?null
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }
    
    /**
     * 閼惧嘲褰囬崚娑樼紦閺冦儲婀￠惃?LocalDateTime 鐎电钖?
     * @return 閸掓稑缂撻弮銉︽埂閻?LocalDateTime 鐎电钖?
     */
    public LocalDateTime getCreatedDate() {
        if (createdDate == null && createdDateStr != null) {
            createdDate = parseDateTime(createdDateStr);
            if (createdDate == null) {
                // 娑撳秷顩﹂崷銊ㄧ箹闁插矁顔曠純顔肩秼閸撳秵妞傞梻杈剧礉鏉╂瑤绱扮€佃壈鍤ч幍鈧張澶嬬Х閹垱妯夌粈铏规祲閸氬瞼娈戦弮鍫曟？
                // 婵″倹鐏夐弮鐘崇《鐟欙絾鐎介弮銉︽埂鐎涙顑佹稉璇х礉鏉╂柨娲杗ull閹存牠绮拋銈嗘）閺?
                createdDate = LocalDateTime.of(2000, 1, 1, 0, 0, 0); // 娴ｈ法鏁ゆ稉鈧稉顏呮閺勫墽娈戞妯款吇閺冦儲婀?
                // 娑撳秷顩︽穱顔芥暭閸樼喎顫愰惃鍒eatedDateStr
            }
        }
        return createdDate;
    }
    
    /**
     * 鐠佸墽鐤嗛崚娑樼紦閺冦儲婀￠惃?LocalDateTime 鐎电钖?
     * @param dateTime 閸掓稑缂撻弮銉︽埂閻?LocalDateTime 鐎电钖?
     */
    public void setCreatedDate(LocalDateTime dateTime) {
        this.createdDate = dateTime;
        this.createdDateStr = formatDateTime(dateTime);
    }
    
    /**
     * 閼惧嘲褰囬崚娑樼紦閺冦儲婀￠惃鍕摟缁楋缚瑕嗙悰銊с仛
     * @return 閸掓稑缂撻弮銉︽埂閻ㄥ嫬鐡х粭锔胯鐞涖劎銇?
     */
    public String getCreatedDateStr() {
        return this.createdDateStr;
    }
    
    /**
     * 鐠佸墽鐤嗛崚娑樼紦閺冦儲婀￠惃鍕摟缁楋缚瑕嗙悰銊с仛
     * @param dateStr 閸掓稑缂撻弮銉︽埂閻ㄥ嫬鐡х粭锔胯鐞涖劎銇?
     */
    public void setCreatedDateStr(String dateStr) {
        this.createdDateStr = dateStr;
        this.createdDate = null; // 濞撳懐鈹栫紓鎾崇摠閻?LocalDateTime 鐎电钖勯敍灞间簰娓氬じ绗呭▎陇骞忛崣鏍ㄦ闁插秵鏌婄憴锝嗙€?
    }
} 
