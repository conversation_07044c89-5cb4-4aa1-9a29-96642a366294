# MyLog Web应用程序部署指南

## 部署前准备

1. 确保目标系统已安装Java 21
   - 可从 [Oracle官网](https://www.oracle.com/java/technologies/downloads/) 或 [OpenJDK](https://jdk.java.net/21/) 下载

## 部署步骤

1. 复制数据库文件到data目录
   - 将 `LogManagement.db` 复制到 `data/` 目录
   - 将 `UserManagement.db` 复制到 `data/` 目录

2. 启动应用程序
   - Windows系统：双击 `startup.bat`
   - Linux系统：执行 `chmod +x startup.sh` 后运行 `./startup.sh`

3. 访问应用程序
   - 打开浏览器访问：`http://localhost:8080`
   - 默认管理员账号：admin/admin123
   - 默认普通用户账号：user/123

## 注意事项

1. 如需修改端口号，请编辑 `application.properties` 文件中的 `server.port` 属性
2. 如需增加Java内存分配，请编辑启动脚本，添加 `-Xms512m -Xmx1024m` 等参数
3. 定期备份 `data` 目录下的数据库文件
4. 如果启动失败，请检查日志文件 `logs/mylog-web.log`

## 自定义配置

如需进一步自定义应用配置，请参考 `application.properties` 文件中的注释说明。 