package com.mylog.config;

import com.mylog.service.ViolationRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 违规规则初始化配置
 */
@Configuration
public class ViolationRuleInitializer {
    
    private static final Logger logger = LoggerFactory.getLogger(ViolationRuleInitializer.class);
    
    /**
     * 应用启动时初始化违规规则
     */
    @Bean
    public static CommandLineRunner initViolationRules(ViolationRuleService violationRuleService) {
        return args -> {
            logger.info("检查并初始化违规规则...");
            violationRuleService.initDefaultRules();
        };
    }
}
