package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;
import java.util.Optional;

@Entity
@Table(name = "Tasks")
@Data
public class ProjectTask {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTask.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long taskId;

    @Column(nullable = false)
    private Long projectId;

    @Column(nullable = false, length = 255)
    private String taskName;

    @Column(nullable = false, length = 50)
    private String responsible;

    @Column(nullable = false, length = 20)
    private String status;

    @Column(columnDefinition = "varchar(10) default '正常'")
    private String risk = "正常";

    @Column(columnDefinition = "TEXT")
    private String remarks;

    @Column
    private BigDecimal durationDays;

    @Column
    private BigDecimal ratedDurationDays;
    
    /**
     * 累计工期（天）
     */
    @Column(name = "cumulative_duration_days")
    private BigDecimal cumulativeDurationDays;
    
    /**
     * 剩余工期（天）= 额定工期 - 累计工期
     */
    @Column(name = "remaining_duration_days")
    private BigDecimal remainingDurationDays;

    @Column(length = 50)
    private String type;

    @Column
    private Double ratio = 0.0;

    @Column(nullable = false)
    private Integer progress = 0;    @Column
    private BigDecimal bonus;

    @Column(columnDefinition = "TEXT")
    private String actualStartDate;

    @Column(columnDefinition = "TEXT")
    private String actualEndDate;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String createdDate;

    @Column(length = 50)
    private String createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "projectId", referencedColumnName = "projectId", insertable = false, updatable = false)
    private Project project;

    @Column
    private Double commentDays;

    @Column(name = "last_comment_date", columnDefinition = "TEXT")
    private String lastCommentDate;

    /**
     * 是否需要审批
     */
    @Column(name = "need_approval", columnDefinition = "INTEGER DEFAULT 0")
    private Integer needApproval = 0;

    /**
     * 关联的审批流程实例ID
     */
    @Column(name = "approval_instance_id")
    private Long approvalInstanceId;

    /**
     * 审批状态
     * 0: 无需审批
     * 1: 审批中
     * 2: 审批通过
     * 3: 审批拒绝
     */
    @Column(name = "approval_status", columnDefinition = "INTEGER DEFAULT 0")
    private Integer approvalStatus = 0;

    public ProjectTask() {
        setCreatedDateTime(LocalDateTime.now());
        if (progress == null) {
            progress = 0;
        }
        if (ratio == null) {
            ratio = 0.0;
        }
    }

    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                if (dateStr.contains("T")) {
                    return LocalDateTime.parse(dateStr);
                }
            } catch (DateTimeParseException ex) {
                logger.error("日期格式错误: {}", ex.getMessage());
            }
            return null;
        }
    }

    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }

    public String getActualStartDate() {
        return this.actualStartDate;
    }

    public void setActualStartDate(String date) {
        if (date != null && date.contains("T")) {
            try {
                LocalDateTime dateTime = LocalDateTime.parse(date);
                this.actualStartDate = formatDateTime(dateTime);
            } catch (Exception e) {
                this.actualStartDate = date;
                logger.error("日期格式错误: {}", e.getMessage());
            }
        } else {
            this.actualStartDate = date;
        }
    }

    public String getActualEndDate() {
        return this.actualEndDate;
    }

    public void setActualEndDate(String date) {
        if (date != null && date.contains("T")) {
            try {
                LocalDateTime dateTime = LocalDateTime.parse(date);
                this.actualEndDate = formatDateTime(dateTime);
            } catch (Exception e) {
                this.actualEndDate = date;
                logger.error("日期格式错误: {}", e.getMessage());
            }
        } else {
            this.actualEndDate = date;
        }
    }

    public String getCreatedDate() {
        return this.createdDate;
    }

    public void setCreatedDate(String date) {
        this.createdDate = date;
    }

    public LocalDateTime getActualStartDateTime() {
        return parseDateTime(this.actualStartDate);
    }

    public void setActualStartDateTime(LocalDateTime dateTime) {
        this.actualStartDate = formatDateTime(dateTime);
    }

    public LocalDateTime getActualEndDateTime() {
        return parseDateTime(this.actualEndDate);
    }

    public void setActualEndDateTime(LocalDateTime dateTime) {
        this.actualEndDate = formatDateTime(dateTime);
    }

    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.createdDate);
    }

    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.createdDate = formatDateTime(dateTime);
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        if ("已完成".equals(this.status) && !"已完成".equals(status)) {
            this.actualEndDate = null;
            logger.debug("状态从已完成变更为{}，清空实际完成时间", status);
        }

        if ("进行中".equals(status) && (this.actualStartDate == null || this.actualStartDate.isEmpty())) {
            logger.debug("自动设置任务开始时间");
            setActualStartDateTime(LocalDateTime.now());
        }

        this.status = status;
    }

    public Integer getProgressPercentage() {
        if (progress != null) {
            return progress;
        } else if (ratio != null) {
            return (int) (ratio * 100);
        }
        return 0;
    }

    public Double getCommentDays() {
        return commentDays;
    }

    public void setCommentDays(Double commentDays) {
        this.commentDays = commentDays;
    }

    public String getLastCommentDate() {
        return lastCommentDate;
    }

    public void setLastCommentDate(String lastCommentDate) {
        this.lastCommentDate = lastCommentDate;
    }

    public LocalDateTime getLastCommentDateTime() {
        return parseDateTime(this.lastCommentDate);
    }

    public void setLastCommentDateTime(LocalDateTime dateTime) {
        this.lastCommentDate = formatDateTime(dateTime);
    }
}