<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{fragments/layout}">
<head th:replace="~{fragments/layout :: head('违规规则管理')}">
    <meta charset="UTF-8">
    <title>违规规则管理</title>
    <style>
        .rule-card {
            margin-bottom: 15px;
        }
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .rule-actions {
            display: flex;
            gap: 5px;
        }
        .disabled-rule {
            opacity: 0.6;
        }
    </style>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">违规规则管理</h3>
                            <div class="card-tools">
                                <a th:href="@{/admin/violation-rules/create(currentUsername=${currentUsername})}" class="btn btn-primary btn-sm">
                                    <i class="bi bi-plus-lg"></i> 新建规则
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 成功/错误消息 -->
                            <div th:if="${successMessage}" class="alert alert-success alert-dismissible">
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                <h5><i class="bi bi-check-circle"></i> 成功!</h5>
                                <span th:text="${successMessage}"></span>
                            </div>
                            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible">
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                <h5><i class="bi bi-exclamation-triangle"></i> 错误!</h5>
                                <span th:text="${errorMessage}"></span>
                            </div>

                            <!-- 规则列表 -->
                            <div th:if="${#lists.isEmpty(rules)}" class="alert alert-info">
                                <i class="bi bi-info-circle"></i> 暂无违规规则，请点击"新建规则"按钮创建。
                            </div>

                            <div th:each="rule : ${rules}" class="card rule-card" th:classappend="${!rule.enabled ? 'disabled-rule' : ''}">
                                <div class="card-header rule-header">
                                    <h5 class="card-title">
                                        <span th:text="${rule.ruleName}"></span>
                                        <span th:if="${!rule.enabled}" class="badge bg-secondary">已禁用</span>
                                    </h5>
                                    <div class="rule-actions">
                                        <a th:href="@{/admin/violation-rules/edit/{id}(id=${rule.ruleId},currentUsername=${currentUsername})}" class="btn btn-info btn-sm">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </a>
                                        <form th:action="@{/admin/violation-rules/toggle/{id}(id=${rule.ruleId})}" method="post" style="display: inline;">
                                            <input type="hidden" name="currentUsername" th:value="${currentUsername}">
                                            <button type="submit" class="btn btn-warning btn-sm">
                                                <i class="bi bi-power"></i>
                                                <span th:text="${rule.enabled ? '禁用' : '启用'}"></span>
                                            </button>
                                        </form>
                                        <form th:action="@{/admin/violation-rules/delete/{id}(id=${rule.ruleId})}" method="post" style="display: inline;"
                                              onsubmit="return confirm('确定要删除此规则吗？此操作不可撤销。');">
                                            <input type="hidden" name="currentUsername" th:value="${currentUsername}">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </form>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p th:if="${rule.description}" th:text="${rule.description}"></p>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <dl class="row">
                                                <dt class="col-sm-4">任务状态:</dt>
                                                <dd class="col-sm-8" th:text="${rule.taskStatus ?: '所有状态'}"></dd>

                                                <dt class="col-sm-4">排除任务名称:</dt>
                                                <dd class="col-sm-8" th:text="${rule.taskNameExcludes ?: '无'}"></dd>

                                                <dt class="col-sm-4">包含任务名称:</dt>
                                                <dd class="col-sm-8" th:text="${rule.taskNameIncludes ?: '所有'}"></dd>
                                            </dl>
                                        </div>
                                        <div class="col-md-6">
                                            <dl class="row">
                                                <dt class="col-sm-4">评论天数阈值:</dt>
                                                <dd class="col-sm-8" th:text="${rule.commentDaysThreshold ?: '无'}"></dd>

                                                <dt class="col-sm-4">特殊任务标识:</dt>
                                                <dd class="col-sm-8" th:text="${rule.specialTaskIdentifier ?: '无'}"></dd>

                                                <dt class="col-sm-4">特殊任务评论天数阈值:</dt>
                                                <dd class="col-sm-8" th:text="${rule.specialTaskCommentDaysThreshold ?: '无'}"></dd>

                                                <dt class="col-sm-4">违规扣分:</dt>
                                                <dd class="col-sm-8" th:text="${rule.score ?: 0} + '分'"></dd>
                                            </dl>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <small class="text-muted">
                                                创建: <span th:text="${#temporals.format(rule.createdDateTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                                <span th:if="${rule.createdBy}" th:text="${'由 ' + rule.createdBy}"></span>
                                                <span th:if="${rule.lastModifiedDateTime}">
                                                    | 最后修改: <span th:text="${#temporals.format(rule.lastModifiedDateTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                                    <span th:if="${rule.lastModifiedBy}" th:text="${'由 ' + rule.lastModifiedBy}"></span>
                                                </span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 违规任务报表自动导出配置 -->
            <div class="row justify-content-center mt-4">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gear-fill me-2"></i>违规任务报表自动导出配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 成功提示消息 -->
                            <div th:if="${exportConfigSuccessMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle me-2"></i>
                                <span th:text="${exportConfigSuccessMessage}">配置已保存</span>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>

                            <div class="row justify-content-center">
                                <div class="col-12 col-md-8">
                                    <form th:action="@{/reports/report-export-config/save}" method="post" th:object="${config}">
                                        <input type="hidden" th:field="*{id}">

                                        <!-- 启用开关 -->
                                        <div class="form-check form-switch mb-4">
                                            <input class="form-check-input" type="checkbox" th:field="*{enabled}" id="enabled">
                                            <label class="form-check-label fs-5" for="enabled">启用自动导出</label>
                                        </div>

                                        <!-- 导出时间 -->
                                        <div class="mb-4">
                                            <label for="exportTime" class="form-label fs-5">导出时间</label>
                                            <select class="form-select form-select-lg" id="exportTime" name="exportTime" required>
                                                <option value="" disabled>选择小时</option>
                                                <option th:each="hour : ${#numbers.sequence(0, 23)}"
                                                        th:value="${hour + ':00'}"
                                                        th:text="${hour + ' 点'}"
                                                        th:selected="${config.exportTime != null && config.exportTime.hour == hour}">
                                                    0 点
                                                </option>
                                            </select>
                                            <div class="form-text">只需要选择小时，系统将在指定小时整点执行导出</div>
                                        </div>

                                        <!-- 导出路径 -->
                                        <div class="mb-4">
                                            <label for="exportPath" class="form-label fs-5">导出路径</label>
                                            <input type="text" class="form-control form-control-lg" id="exportPath" th:field="*{exportPath}"
                                                   placeholder="默认为 reports" title="请输入相对于应用程序根目录的路径">
                                            <div class="form-text">如果路径不存在，系统会自动创建。请使用相对路径，例如：reports</div>
                                        </div>

                                        <!-- 星期选择 -->
                                        <div class="mb-4">
                                            <label class="form-label fs-5">导出日期</label>
                                            <div class="row g-3">
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{monday}" id="monday">
                                                        <label class="form-check-label" for="monday">星期一</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{tuesday}" id="tuesday">
                                                        <label class="form-check-label" for="tuesday">星期二</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{wednesday}" id="wednesday">
                                                        <label class="form-check-label" for="wednesday">星期三</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{thursday}" id="thursday">
                                                        <label class="form-check-label" for="thursday">星期四</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{friday}" id="friday">
                                                        <label class="form-check-label" for="friday">星期五</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{saturday}" id="saturday">
                                                        <label class="form-check-label" for="saturday">星期六</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" th:field="*{sunday}" id="sunday">
                                                        <label class="form-check-label" for="sunday">星期日</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 最后导出时间 -->
                                        <div class="mb-4" th:if="${config.lastExportTime != null}">
                                            <label class="form-label fs-5">最后导出时间</label>
                                            <p class="form-control-plaintext fs-5" th:text="${#temporals.format(config.lastExportTime, 'yyyy-MM-dd HH:mm:ss')}">2023-01-01 12:00:00</p>
                                        </div>
                                        <!-- 保存配置按钮 -->
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-info btn-lg">
                                                <i class="bi bi-save me-2"></i>保存配置
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


    </div>
</body>
</html>
