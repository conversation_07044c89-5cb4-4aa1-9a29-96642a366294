-- 创建工时登记表
CREATE TABLE IF NOT EXISTS work_hours_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    business_type TEXT NOT NULL,           -- 业务类型（字符串）
    business_id INTEGER NOT NULL,          -- 业务ID（整数）
    hours_inventory REAL NOT NULL,         -- 工时存量（double）
    hours_change REAL NOT NULL,            -- 工时变化（double）
    reason TEXT NOT NULL,                  -- 原因（字符串）
    created_time TEXT NOT NULL             -- 创建时间（字符串格式为2025-03-03 08:07:05）
);

-- 为business_type字段创建索引，加速按业务类型查询
CREATE INDEX IF NOT EXISTS idx_work_hours_log_business_type ON work_hours_log (business_type);

-- 为business_id字段创建索引，加速按业务ID查询
CREATE INDEX IF NOT EXISTS idx_work_hours_log_business_id ON work_hours_log (business_id);

-- 为created_time字段创建索引，方便按时间排序和查询
CREATE INDEX IF NOT EXISTS idx_work_hours_log_created_time ON work_hours_log (created_time);

-- 为business_type和business_id创建复合索引，加速联合查询
CREATE INDEX IF NOT EXISTS idx_work_hours_log_business ON work_hours_log (business_type, business_id);
