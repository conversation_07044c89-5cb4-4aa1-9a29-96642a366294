<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head(${step.stepId == null ? '添加流程步骤' : '编辑流程步骤'})}">
    <meta charset="UTF-8">
    <title>流程步骤表单</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${step.stepId == null ? '添加流程步骤' : '编辑流程步骤'}">流程步骤表单</h1>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('stepForm').submit()">
                    <i class="bi bi-save"></i> 保存
                </button>
            </div>
        </div>

        <!-- 表单卡片 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">步骤信息</h5>
                    </div>
                    <div class="card-body">
                        <form id="stepForm" th:action="@{/workflow/steps/save}" method="post" th:object="${step}" class="row g-3">
                            <!-- 隐藏字段 -->
                            <input type="hidden" th:field="*{stepId}">
                            <input type="hidden" name="templateId" th:value="${template.templateId}">
                            
                            <!-- 步骤名称 -->
                            <div class="col-md-6">
                                <label for="stepName" class="form-label">步骤名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="stepName" th:field="*{stepName}" required>
                            </div>
                            
                            <!-- 步骤顺序 -->
                            <div class="col-md-6">
                                <label for="stepOrder" class="form-label">步骤顺序 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="stepOrder" th:field="*{stepOrder}" min="1" required>
                            </div>
                            
                            <!-- 审批人类型 -->
                            <div class="col-md-6">
                                <label for="approverType" class="form-label">审批人类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="approverType" th:field="*{approverType}" required onchange="updateApproverConfigHelp()">
                                    <option th:each="type : ${approverTypes}" th:value="${type}" th:text="${type.name() == 'FIXED_USER' ? '固定用户' : (type.name() == 'ROLE' ? '角色' : (type.name() == 'DEPARTMENT_HEAD' ? '部门主管' : '动态指定'))}">审批人类型</option>
                                </select>
                            </div>
                            
                            <!-- 审批人配置 -->
                            <div class="col-md-6">
                                <label for="approverConfig" class="form-label">审批人配置</label>
                                <input type="text" class="form-control" id="approverConfig" th:field="*{approverConfig}">
                                <small id="approverConfigHelp" class="form-text text-muted">
                                    <!-- 根据审批人类型显示不同的帮助文本 -->
                                </small>
                            </div>
                            
                            <!-- 允许退回 -->
                            <div class="col-md-4">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="allowReject" th:field="*{allowReject}">
                                    <label class="form-check-label" for="allowReject">允许退回</label>
                                </div>
                            </div>
                            
                            <!-- 允许转交 -->
                            <div class="col-md-4">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="allowTransfer" th:field="*{allowTransfer}">
                                    <label class="form-check-label" for="allowTransfer">允许转交</label>
                                </div>
                            </div>
                            
                            <!-- 条件分支 -->
                            <div class="col-md-4">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="isConditionBranch" th:field="*{isConditionBranch}" onchange="toggleConditionExpression()">
                                    <label class="form-check-label" for="isConditionBranch">条件分支</label>
                                </div>
                            </div>
                            
                            <!-- 条件表达式 -->
                            <div class="col-md-12" id="conditionExpressionDiv" style="display: none;">
                                <label for="conditionExpression" class="form-label">条件表达式</label>
                                <textarea class="form-control" id="conditionExpression" th:field="*{conditionExpression}" rows="3"></textarea>
                                <small class="form-text text-muted">
                                    请输入条件表达式，例如：bonus > 10000 && risk == '高风险'
                                </small>
                            </div>
                            
                            <!-- 步骤说明 -->
                            <div class="col-md-12">
                                <label for="description" class="form-label">步骤说明</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-end">
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('stepForm').submit()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表单验证
            const form = document.getElementById('stepForm');
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
            
            // 初始化
            updateApproverConfigHelp();
            toggleConditionExpression();
        });
        
        function updateApproverConfigHelp() {
            const approverType = document.getElementById('approverType').value;
            const helpText = document.getElementById('approverConfigHelp');
            
            switch (approverType) {
                case 'FIXED_USER':
                    helpText.textContent = '请输入用户名，多个用户用逗号分隔，例如：user1,user2';
                    break;
                case 'ROLE':
                    helpText.textContent = '请输入角色名称，例如：ADMIN,MANAGER';
                    break;
                case 'DEPARTMENT_HEAD':
                    helpText.textContent = '请输入部门名称，例如：技术部,销售部';
                    break;
                case 'DYNAMIC':
                    helpText.textContent = '请输入动态指定的表达式，例如：${initiator}';
                    break;
                default:
                    helpText.textContent = '';
            }
        }
        
        function toggleConditionExpression() {
            const isConditionBranch = document.getElementById('isConditionBranch').checked;
            const conditionExpressionDiv = document.getElementById('conditionExpressionDiv');
            
            if (isConditionBranch) {
                conditionExpressionDiv.style.display = 'block';
            } else {
                conditionExpressionDiv.style.display = 'none';
            }
        }
    </script>
</body>
</html>
