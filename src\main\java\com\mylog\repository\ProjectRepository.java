package com.mylog.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.mylog.model.Project;

@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    // 根据项目编号查找项目
    Project findByProjectCode(String projectCode);
    
    // 根据项目名称查找项目（模糊查询）
    List<Project> findByProjectNameContaining(String projectName);
    
    // 根据客户名称查找项目
    List<Project> findByCustomerName(String customerName);
    
    // 根据负责人查找项目
    @Query("SELECT p FROM Project p WHERE p.responsible = :responsible ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    List<Project> findByResponsible(@Param("responsible") String responsible);
    
    // 根据状态查找项目
    List<Project> findByStatus(String status);
    
    /**
     * 统计特定负责人和状态的项目数量
     * @param responsible 负责人
     * @param status 状态
     * @return 项目数量
     */
    @Query("SELECT COUNT(p) FROM Project p WHERE p.responsible = :responsible AND p.status = :status")
    Long countByResponsibleAndStatus(@Param("responsible") String responsible, @Param("status") String status);
    
    // 查询所有项目并按创建时间倒序排序
    @Query("SELECT p FROM Project p ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    List<Project> findAllOrderByCreatedDateDesc();
    
    // 查询所有项目并按创建时间倒序排序（分页版本）
    @Query("SELECT p FROM Project p ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    Page<Project> findAllOrderByCreatedDateDescPageable(Pageable pageable);
    
    // 综合搜索
    @Query("SELECT p FROM Project p WHERE " +
           "(:keyword IS NULL OR p.projectCode LIKE %:keyword% OR " +
           "p.projectName LIKE %:keyword% OR " +
           "p.customerName LIKE %:keyword% OR " +
           "p.responsible LIKE %:keyword%) ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    List<Project> searchProjects(@Param("keyword") String keyword);
    
    // 综合搜索（分页版本）
    @Query("SELECT p FROM Project p WHERE " +
           "(:keyword IS NULL OR p.projectCode LIKE %:keyword% OR " +
           "p.projectName LIKE %:keyword% OR " +
           "p.customerName LIKE %:keyword% OR " +
           "p.responsible LIKE %:keyword%) ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    Page<Project> searchProjectsPageable(@Param("keyword") String keyword, Pageable pageable);
    
    // 多字段组合搜索
    @Query("SELECT p FROM Project p WHERE " +
           "(:projectName IS NULL OR :projectName = '' OR p.projectName LIKE %:projectName%) AND " +
           "(:customerName IS NULL OR :customerName = '' OR p.customerName LIKE %:customerName%) AND " +
           "(:status IS NULL OR :status = '' OR p.status = :status) ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    List<Project> advancedSearchProjects(
            @Param("projectName") String projectName,
            @Param("customerName") String customerName,
            @Param("status") String status);
    
    // 多字段组合搜索（分页版本）
    @Query("SELECT p FROM Project p WHERE " +
           "(:projectName IS NULL OR :projectName = '' OR p.projectName LIKE %:projectName%) AND " +
           "(:customerName IS NULL OR :customerName = '' OR p.customerName LIKE %:customerName%) AND " +
           "(:status IS NULL OR :status = '' OR p.status = :status) ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    Page<Project> advancedSearchProjectsPageable(
            @Param("projectName") String projectName,
            @Param("customerName") String customerName,
            @Param("status") String status,
            Pageable pageable);
    
    // 查询当天最大的项目编码序号
    @Query("SELECT MAX(CAST(SUBSTRING(p.projectCode, 11) AS int)) " +
           "FROM Project p " +
           "WHERE p.projectCode LIKE CONCAT('PJ', :dateStr, '%')")
    Integer findMaxSequenceByDate(@Param("dateStr") String dateStr);
    
    /**
     * 查询已归档项目
     * @param archive 归档状态值
     * @param pageable 分页参数
     * @return 已归档项目列表
     */
    @Query("SELECT p FROM Project p WHERE p.archive = :archive ORDER BY p.createdDate DESC")
    Page<Project> findByArchive(@Param("archive") Integer archive, Pageable pageable);
    
    /**
     * 查询未归档项目
     * @param archive 归档状态值
     * @param pageable 分页参数
     * @return 未归档项目列表
     */
    @Query("SELECT p FROM Project p WHERE p.archive IS NULL OR p.archive != :archive ORDER BY CASE WHEN p.status = '进行中' THEN 0 ELSE 1 END, p.createdDate DESC")
    Page<Project> findByArchiveIsNullOrArchiveNot(@Param("archive") Integer archive, Pageable pageable);
    
    /**
     * 查询未归档且处于特定状态的项目
     * @param archive 归档状态值
     * @param status 项目状态
     * @param pageable 分页参数
     * @return 符合条件的项目列表
     */
    @Query("SELECT p FROM Project p WHERE (p.archive IS NULL OR p.archive != :archive) AND p.status = :status ORDER BY p.createdDate DESC")
    Page<Project> findByArchiveIsNullOrArchiveNotAndStatus(
            @Param("archive") Integer archive, 
            @Param("status") String status, 
            Pageable pageable);
            
    @Query(value = "DELETE FROM SQLITE_SEQUENCE WHERE name='Projects'", nativeQuery = true)
    @Modifying
    void resetAutoIncrement();

    @Query(value = "UPDATE SQLITE_SEQUENCE SET seq = 0 WHERE name='Projects'", nativeQuery = true)
    @Modifying
    void resetSequenceToZero();
} 