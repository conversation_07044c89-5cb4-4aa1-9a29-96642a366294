package com.mylog.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户会话临时文件跟踪器
 * 用于管理用户上传的临时文件，跟踪文件与会话的关联关系
 */
@Service
public class UserSessionTempFileTracker {
    
    private static final Logger logger = LoggerFactory.getLogger(UserSessionTempFileTracker.class);
    
    // 会话ID -> 用户名的映射
    private final Map<String, String> sessionUserMap = new ConcurrentHashMap<>();
    
    // 会话ID -> 临时文件路径集合的映射
    private final Map<String, Set<String>> sessionFilesMap = new ConcurrentHashMap<>();
    
    // 文件路径 -> 会话ID的映射，用于快速查找
    private final Map<String, String> fileSessionMap = new ConcurrentHashMap<>();

    /**
     * 跟踪临时文件
     * 
     * @param sessionId 会话ID
     * @param username 用户名
     * @param filePath 临时文件路径
     */
    public void trackTempFile(String sessionId, String username, String filePath) {
        if (sessionId == null || filePath == null) {
            logger.warn("无效的参数：sessionId={}, filePath={}", sessionId, filePath);
            return;
        }
        
        logger.info("跟踪临时文件：会话={}, 用户={}, 文件={}", sessionId, username, filePath);
        
        // 记录会话和用户的关联
        if (username != null) {
            sessionUserMap.put(sessionId, username);
        }
        
        // 记录会话和文件的关联
        sessionFilesMap.computeIfAbsent(sessionId, k -> new HashSet<>()).add(filePath);
        
        // 记录文件和会话的关联
        fileSessionMap.put(filePath, sessionId);
    }

    /**
     * 移除临时文件跟踪
     * 
     * @param sessionId 会话ID
     * @param filePath 临时文件路径
     */
    public void removeTempFile(String sessionId, String filePath) {
        if (sessionId == null || filePath == null) {
            logger.warn("无效的参数：sessionId={}, filePath={}", sessionId, filePath);
            return;
        }
        
        logger.info("移除临时文件跟踪：会话={}, 文件={}", sessionId, filePath);
        
        // 从会话文件映射中移除
        Set<String> files = sessionFilesMap.get(sessionId);
        if (files != null) {
            files.remove(filePath);
            if (files.isEmpty()) {
                sessionFilesMap.remove(sessionId);
                sessionUserMap.remove(sessionId);
            }
        }
        
        // 从文件会话映射中移除
        fileSessionMap.remove(filePath);
    }

    /**
     * 获取会话关联的所有临时文件
     * 
     * @param sessionId 会话ID
     * @return 临时文件路径集合
     */
    public Set<String> getSessionTempFiles(String sessionId) {
        if (sessionId == null) {
            return new HashSet<>();
        }
        
        Set<String> files = sessionFilesMap.get(sessionId);
        return files != null ? new HashSet<>(files) : new HashSet<>();
    }

    /**
     * 获取文件对应的会话ID
     * 
     * @param filePath 文件路径
     * @return 会话ID，如果未找到则返回null
     */
    public String getFileSession(String filePath) {
        return fileSessionMap.get(filePath);
    }

    /**
     * 获取会话对应的用户名
     * 
     * @param sessionId 会话ID
     * @return 用户名，如果未找到则返回null
     */
    public String getSessionUser(String sessionId) {
        return sessionUserMap.get(sessionId);
    }

    /**
     * 清理会话的所有临时文件跟踪
     * 
     * @param sessionId 会话ID
     */
    public void clearSession(String sessionId) {
        if (sessionId == null) {
            return;
        }
        
        logger.info("清理会话临时文件跟踪：会话={}", sessionId);
        
        Set<String> files = sessionFilesMap.remove(sessionId);
        sessionUserMap.remove(sessionId);
        
        if (files != null) {
            for (String filePath : files) {
                fileSessionMap.remove(filePath);
            }
        }
    }

    /**
     * 获取所有被跟踪的会话数量
     * 
     * @return 会话数量
     */
    public int getSessionCount() {
        return sessionFilesMap.size();
    }

    /**
     * 获取所有被跟踪的文件数量
     * 
     * @return 文件数量
     */
    public int getFileCount() {
        return fileSessionMap.size();
    }

    /**
     * 获取跟踪信息的统计摘要
     * 
     * @return 包含统计信息的Map
     */
    public Map<String, Object> getTrackingStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("sessionCount", getSessionCount());
        stats.put("fileCount", getFileCount());
        stats.put("sessionUsers", new HashMap<>(sessionUserMap));
        
        Map<String, Integer> sessionFileCounts = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : sessionFilesMap.entrySet()) {
            sessionFileCounts.put(entry.getKey(), entry.getValue().size());
        }
        stats.put("sessionFileCounts", sessionFileCounts);
        
        return stats;
    }
}
