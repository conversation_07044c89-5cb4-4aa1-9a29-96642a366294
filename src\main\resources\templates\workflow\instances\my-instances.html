<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('我的流程')}">
    <meta charset="UTF-8">
    <title>我的流程</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">我的流程</h1>
            <div>
                <a th:href="@{/workflow/my}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                <a th:href="@{/workflow/instances/create}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> 发起流程
                </a>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">搜索条件</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <form th:action="@{/workflow/instances/my}" method="get" class="row g-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">全部</option>
                                        <option th:each="statusOption : ${statuses}" 
                                                th:value="${statusOption}" 
                                                th:text="${statusOption.name() == 'DRAFT' ? '草稿' : 
                                                         (statusOption.name() == 'PROCESSING' ? '处理中' : 
                                                         (statusOption.name() == 'APPROVED' ? '已批准' : 
                                                         (statusOption.name() == 'REJECTED' ? '已拒绝' : 
                                                         (statusOption.name() == 'CANCELED' ? '已取消' : '已终止'))))}"
                                                th:selected="${status == statusOption}">
                                            状态
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                    <a th:href="@{/workflow/instances/my}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i> 清除
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的流程列表 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">我的流程列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 成功/错误消息 -->
                        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <span th:text="${message}">操作成功</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                        <!-- 流程列表表格 - 替换为my-workflow.html中的对应代码 -->
                        <div class="table-responsive" th:if="${!instancePage.empty}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>流程标题</th>
                                        <th>流程模板</th>
                                        <th>业务ID</th>
                                        <th>状态</th>
                                        <th>流程进度</th>
                                        <th>创建时间</th>
                                        <th>待审批人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="instance : ${instancePage.content}">
                                        <td th:text="${instance.title}">流程标题</td>
                                        <td th:text="${instance.template != null ? instance.template.templateName : '未知模板'}">流程模板</td>
                                        <td>
                                            <!-- 如果业务类型为任务，则显示为任务链接 -->
                                            <a th:if="${instance.businessId != null && instance.businessType == '任务'}" 
                                               th:href="@{/tasks/{id}(id=${instance.businessId})}"
                                               th:text="${instance.businessId}">123</a>
                                            <!-- 如果业务类型不是任务或为空，则显示普通文本 -->
                                            <span th:if="${instance.businessId != null && instance.businessType != '任务'}" 
                                                  th:text="${instance.businessId}">123</span>
                                            <span th:unless="${instance.businessId != null}">-</span>
                                        </td>
                                        <td>
                                            <span th:if="${instance.status.name() == 'DRAFT'}" class="badge bg-secondary">草稿</span>
                                            <span th:if="${instance.status.name() == 'PROCESSING'}" class="badge bg-primary">处理中</span>
                                            <span th:if="${instance.status.name() == 'APPROVED'}" class="badge bg-success">已批准</span>
                                            <span th:if="${instance.status.name() == 'REJECTED'}" class="badge bg-danger">已拒绝</span>
                                            <span th:if="${instance.status.name() == 'CANCELED'}" class="badge bg-warning">已取消</span>
                                            <span th:if="${instance.status.name() == 'TERMINATED'}" class="badge bg-dark">已终止</span>
                                        </td>
                                        <td>
                                            <!-- 添加流程进度显示，增加null值检查 -->
                                            <span th:if="${instance.status.name() == 'PROCESSING'}" class="badge bg-info ms-1" 
                                                  th:text="${(instanceSteps != null && instanceSteps[instance.instanceId] != null ? instanceSteps[instance.instanceId] : 0) + 
                                                  '/' + (instance.stepCount != null ? instance.stepCount : 0) + ' 步'}">0/0 步</span>
                                        </td>
                                        <td th:text="${instance.createdDateTime != null ? #temporals.format(instance.createdDateTime, 'yyyy-MM-dd HH:mm') : '-'}">创建时间</td>
                                        <td>
                                            <span th:if="${instance.status.name() != 'PROCESSING'}">-</span>
                                            <span th:if="${instance.status.name() == 'PROCESSING' && instance.currentApprover != null}">
                                                <!-- 如果是用户名，直接显示 -->
                                                <span th:if="${!instance.currentApprover.contains(';') && !instance.currentApprover.contains(',')}" 
                                                      th:text="${instance.currentApprover}"></span>
                                                <!-- 如果是多个用户（以分号或逗号分隔），显示"多人审批" -->
                                                <span th:if="${instance.currentApprover.contains(';') || instance.currentApprover.contains(',')}" 
                                                      title="多人审批" th:attr="data-approvers=${instance.currentApprover}">多人审批</span>
                                            </span>
                                            <span th:if="${instance.status.name() == 'PROCESSING' && instance.currentApprover == null}">-</span>
                                        </td>                                        <td>
                                            <a th:href="@{/workflow/instances/{id}(id=${instance.instanceId})}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> 查看
                                            </a>
                                            <a th:if="${instance.status.name() == 'DRAFT'}" th:href="@{/workflow/approval/{id}(id=${instance.instanceId})}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-send"></i> 提交
                                            </a>
                                            <a th:if="${instance.status.name() == 'PROCESSING' && instanceSteps != null && instanceSteps[instance.instanceId] != null && instanceSteps[instance.instanceId] == 1}" th:href="@{/workflow/approval/{id}(id=${instance.instanceId})}" class="btn btn-sm btn-warning">
                                                <i class="bi bi-arrow-return-left"></i> 撤回
                                            </a>                                            <!-- 草稿状态允许删除 -->
                                            <button th:if="${instance.status.name() == 'DRAFT'}" 
                                                    type="button" 
                                                    class="btn btn-sm btn-outline-danger delete-draft-btn"
                                                    th:attr="data-instance-id=${instance.instanceId},data-title=${instance.title}">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${instancePage.empty}">
                            <p class="text-muted mb-0">暂无发起的流程</p>
                        </div>
                    </div>
                    <!-- 分页控件 -->
                    <div class="card-footer" th:if="${instancePage.totalPages > 0}">
                        <div th:replace="~{fragments/pagination :: pagination(${instancePage}, @{/workflow/instances/my})}"></div>
                    </div>
                </div>
            </div>
        </div>    </div>

    <script>
    /*<![CDATA[*/
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化撤回功能');
            
            // 直接获取所有撤回按钮并添加点击事件
            var withdrawBtns = document.querySelectorAll('.withdraw-btn');
            console.log('找到撤回按钮数量:', withdrawBtns.length);
            
            withdrawBtns.forEach(function(btn) {
                // 直接添加点击事件，不使用事件委托
                btn.onclick = function(event) {
                    event.preventDefault();
                    console.log('撤回按钮被点击');
                    
                    // 获取数据
                    var instanceId = this.getAttribute('data-instance-id');
                    var title = this.getAttribute('data-title');
                    console.log('获取数据:', instanceId, title);
                    
                    // 填充模态框数据
                    document.getElementById('withdrawInstanceId').value = instanceId;
                    document.getElementById('withdrawTitle').textContent = title || '未知流程';
                    
                    // 显示模态框 - 直接使用DOM操作
                    var modal = document.getElementById('withdrawModal');
                    modal.classList.add('show');
                    modal.style.display = 'block';
                    document.body.classList.add('modal-open');
                    
                    // 添加背景遮罩
                    var backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    document.body.appendChild(backdrop);
                    
                    return false;
                };
            });
            
            // 处理关闭按钮
            var closeModalBtns = document.querySelectorAll('#withdrawModal [data-bs-dismiss="modal"]');
            closeModalBtns.forEach(function(btn) {
                btn.onclick = function() {
                    closeWithdrawModal();
                    return false;
                };
            });
            
            // 处理表单提交
            var withdrawForm = document.getElementById('withdrawForm');
            if (withdrawForm) {
                withdrawForm.onsubmit = function(event) {
                    console.log('提交撤回表单');
                    
                    var comment = document.getElementById('comment').value;
                    var instanceId = document.getElementById('withdrawInstanceId').value;
                    
                    if (!comment || comment.trim() === '') {
                        alert('请输入撤回原因');
                        event.preventDefault();
                        return false;
                    }
                    
                    console.log('撤回实例:', instanceId, '原因:', comment);
                    // 表单会正常提交
                    return true;
                };
            }              
            // 初始化删除草稿功能
            console.log('初始化删除草稿功能');
            console.log('页面DOM状态:', document.readyState);
            console.log('页面完整HTML:', document.documentElement.outerHTML.substring(0, 500) + '...');
            
            // 先检查是否有草稿状态的行
            var draftRows = document.querySelectorAll('tr');
            console.log('找到总行数:', draftRows.length);
            var draftCount = 0;
            draftRows.forEach(function(row) {
                if (row.innerHTML.includes('草稿')) {
                    draftCount++;
                    console.log('找到草稿行:', row.innerHTML.substring(0, 200) + '...');
                }
            });
            console.log('草稿状态行数:', draftCount);
            
            var deleteDraftBtns = document.querySelectorAll('.delete-draft-btn');
            console.log('找到删除草稿按钮数量:', deleteDraftBtns.length);
            
            // 检查是否有任何带delete-draft-btn类的元素
            var allElements = document.querySelectorAll('*');
            var elementsWithClass = 0;
            allElements.forEach(function(el) {
                if (el.className && el.className.includes('delete-draft-btn')) {
                    elementsWithClass++;
                    console.log('找到带delete-draft-btn类的元素:', el.outerHTML);
                }
            });
            console.log('总共找到带delete-draft-btn类的元素数量:', elementsWithClass);
            
            // 由于模态框可能不在当前模板中，我们动态创建删除模态框
            if (deleteDraftBtns.length > 0) {
                console.log('发现删除按钮，动态创建删除模态框');
                
                // 创建删除草稿确认模态框
                var modalHTML = `
                    <!-- 删除草稿确认模态框 -->
                    <div class="modal fade" id="deleteDraftModal" tabindex="-1" aria-labelledby="deleteDraftModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteDraftModalLabel">确认删除草稿</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form id="deleteDraftForm" action="/workflow/instances/delete-draft" method="post">
                                    <div class="modal-body">
                                        <input type="hidden" id="deleteInstanceId" name="instanceId">
                                        <p>您确定要删除草稿流程 "<span id="deleteDraftTitle"></span>" 吗？</p>
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                            <strong>注意：</strong>删除后无法恢复，如果此流程关联了任务，任务的审批状态将被重置。
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                        <button type="submit" class="btn btn-danger">确认删除</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                `;
                
                // 将模态框添加到body末尾
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                console.log('删除模态框已动态创建');
                
                // 重新获取模态框元素
                var deleteInstanceIdInput = document.getElementById('deleteInstanceId');
                var deleteDraftTitleSpan = document.getElementById('deleteDraftTitle');
                var deleteDraftModal = document.getElementById('deleteDraftModal');
                
                console.log('重新检查模态框元素:');
                console.log('deleteInstanceIdInput:', deleteInstanceIdInput);
                console.log('deleteDraftTitleSpan:', deleteDraftTitleSpan);
                console.log('deleteDraftModal:', deleteDraftModal);
                
                // 绑定删除按钮事件
                if (deleteInstanceIdInput && deleteDraftTitleSpan && deleteDraftModal) {
                    console.log('开始绑定删除按钮事件...');
                    deleteDraftBtns.forEach(function(btn, index) {
                        console.log('绑定第', index + 1, '个删除按钮');
                        btn.addEventListener('click', function(event) {
                            event.preventDefault();
                            console.log('删除草稿按钮被点击');
                            
                            var instanceId = this.getAttribute('data-instance-id');
                            var title = this.getAttribute('data-title');
                            
                            console.log('获取到数据 - instanceId:', instanceId, 'title:', title);
                            
                            if (!instanceId) {
                                alert('流程实例ID不能为空');
                                return;
                            }
                            
                            // 填充模态框数据
                            deleteInstanceIdInput.value = instanceId;
                            deleteDraftTitleSpan.textContent = title || '未知流程';
                            
                            // 显示模态框
                            var modal = new bootstrap.Modal(deleteDraftModal);
                            modal.show();
                        });
                    });
                    console.log('删除按钮事件绑定完成');
                } else {
                    console.error('动态创建模态框后仍然无法找到必要元素');
                }
            } else {
                console.log('没有找到删除按钮，跳过删除功能初始化');
            }
        });        
        
        // 关闭模态框的辅助函数
        function closeWithdrawModal() {
            var modal = document.getElementById('withdrawModal');
            modal.classList.remove('show');
            modal.style.display = 'none';
            document.body.classList.remove('modal-open');
            
            // 移除背景遮罩
            var backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.parentNode.removeChild(backdrop);
            }
        }
    /*]]>*/
    </script>
    
    <!-- 撤回确认模态框 -->
    <div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="withdrawModalLabel">确认撤回流程</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="withdrawForm" th:action="@{/workflow/approval/withdraw}" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="withdrawInstanceId" name="instanceId">
                        <p>您确定要撤回流程 "<span id="withdrawTitle"></span>" 吗？</p>
                        <div class="mb-3">
                            <label for="comment" class="form-label">撤回原因</label>
                            <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-warning">确认撤回</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除草稿确认模态框 -->
    <div class="modal fade" id="deleteDraftModal" tabindex="-1" aria-labelledby="deleteDraftModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteDraftModalLabel">确认删除草稿</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="deleteDraftForm" th:action="@{/workflow/instances/delete-draft}" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="deleteInstanceId" name="instanceId">
                        <p>您确定要删除草稿流程 "<span id="deleteDraftTitle"></span>" 吗？</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>注意：</strong>删除后无法恢复，如果此流程关联了任务，任务的审批状态将被重置。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
