/**
 * 审批表单初始化脚本
 */
$(document).ready(function() {
    console.log('审批表单JS加载完成，开始初始化...');
    // 初始化所有下拉菜单组件
    initializeSelectComponents();
});

/**
 * 初始化所有下拉菜单组件
 */
function initializeSelectComponents() {
    try {
        console.log('初始化审批表单下拉菜单组件...');
        
        // 初始化转交人员选择框
        const transferSelect = $('#toUsername');
        if (transferSelect.length) {
            console.log('找到转交人员选择框，开始初始化...');
            transferSelect.select2({
                placeholder: '请选择转交人员',
                width: '100%',
                allowClear: true
            });
            console.log('转交人员选择框初始化完成');
        } else {
            console.warn('未找到转交人员选择框 (#toUsername)');
        }
        
        // 初始化下一步审批人选择框
        const nextApproverSelect = $('#nextApprover');
        if (nextApproverSelect.length) {
            console.log('找到下一步审批人选择框，开始初始化...');
            nextApproverSelect.select2({
                placeholder: '请选择审批人',
                width: '100%',
                allowClear: true
            });
            console.log('下一步审批人选择框初始化完成');
        }

        // 打印页面中所有select元素的ID，用于调试
        console.log('页面中的所有select元素:');
        $('select').each(function() {
            console.log('- select元素: #' + $(this).attr('id') + ', options数量: ' + $(this).find('option').length);
        });
    } catch (error) {
        console.error('初始化下拉菜单组件时出错:', error);
    }
}