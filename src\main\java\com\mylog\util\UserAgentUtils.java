package com.mylog.util;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 用户代理工具类
 * 用于解析User-Agent获取访问设备类型
 */
public class UserAgentUtils {

    /**
     * 检测访问设备的操作系统类型
     * 
     * @param request HTTP请求
     * @return 格式化的访问终端类型，如 "移动端（iOS）" 或 "桌面端（Windows）"
     */
    public static String getAccessType(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return "未知设备";
        }
        
        userAgent = userAgent.toLowerCase();
        
        // 移动设备检测
        if (userAgent.contains("iphone") || userAgent.contains("ipad") || userAgent.contains("ipod")) {
            return "移动端（iOS）";
        } else if (userAgent.contains("android")) {
            return "移动端（Android）";
        } else if (userAgent.contains("windows phone")) {
            return "移动端（Windows Phone）";
        }
        
        // 桌面设备检测
        if (userAgent.contains("windows")) {
            return "桌面端（Windows）";
        } else if (userAgent.contains("macintosh") || userAgent.contains("mac os x")) {
            return "桌面端（macOS）";
        } else if (userAgent.contains("linux")) {
            return "桌面端（Linux）";
        } else if (userAgent.contains("unix")) {
            return "桌面端（Unix）";
        }
        
        // 无法识别的设备
        return "未知设备";
    }
} 