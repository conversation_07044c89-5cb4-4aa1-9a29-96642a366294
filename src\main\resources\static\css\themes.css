/* 主题样式 */

/* 默认主题 */
body.theme-default {
    --primary-color: #0d6efd;
    --primary-hover-color: #0b5ed7;
    --primary-border-color: #0a58ca;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #f8f9fa;
    --body-color: #212529;
    --card-bg: #ffffff;
    --card-border-color: rgba(0, 0, 0, 0.125);
    --card-header-bg: #f8f9fa;
    --sidebar-bg: #343a40;
    --sidebar-color: rgba(255, 255, 255, 0.8);
    --sidebar-hover-color: #ffffff;
    --sidebar-active-bg: rgba(255, 255, 255, 0.2);
}

/* 深色主题 */
body.theme-dark {
    --primary-color: #0d6efd;
    --primary-hover-color: #0b5ed7;
    --primary-border-color: #0a58ca;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #212529;
    --body-color: #f8f9fa;
    --card-bg: #343a40;
    --card-border-color: rgba(255, 255, 255, 0.125);
    --card-header-bg: #212529;
    --sidebar-bg: #212529;
    --sidebar-color: rgba(255, 255, 255, 0.8);
    --sidebar-hover-color: #ffffff;
    --sidebar-active-bg: rgba(255, 255, 255, 0.2);
}

/* 浅色主题 */
body.theme-light {
    --primary-color: #0d6efd;
    --primary-hover-color: #0b5ed7;
    --primary-border-color: #0a58ca;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #ffffff;
    --body-color: #212529;
    --card-bg: #f8f9fa;
    --card-border-color: rgba(0, 0, 0, 0.125);
    --card-header-bg: #e9ecef;
    --sidebar-bg: #f8f9fa;
    --sidebar-color: #212529;
    --sidebar-hover-color: #0d6efd;
    --sidebar-active-bg: rgba(13, 110, 253, 0.2);
}

/* 蓝色主题 */
body.theme-blue {
    --primary-color: #0a58ca;
    --primary-hover-color: #084298;
    --primary-border-color: #084298;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #f0f7ff;
    --body-color: #0a58ca;
    --card-bg: #ffffff;
    --card-border-color: rgba(10, 88, 202, 0.2);
    --card-header-bg: #cfe2ff;
    --sidebar-bg: #0a58ca;
    --sidebar-color: rgba(255, 255, 255, 0.8);
    --sidebar-hover-color: #ffffff;
    --sidebar-active-bg: rgba(255, 255, 255, 0.2);
}

/* 应用主题变量 */
body {
    background-color: var(--body-bg);
    color: var(--body-color);
}

.sidebar {
    background-color: var(--sidebar-bg);
}

.sidebar .nav-link {
    color: var(--sidebar-color);
}

.sidebar .nav-link:hover {
    color: var(--sidebar-hover-color);
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-active-bg);
}

.card {
    background-color: var(--card-bg);
    border-color: var(--card-border-color);
}

.card-header {
    background-color: var(--card-header-bg);
    border-color: var(--card-border-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-border-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover-color);
    border-color: var(--primary-border-color);
}

.table th {
    background-color: var(--card-header-bg);
}

/* 表格行交替颜色 */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

body.theme-dark .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
} 