<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程管理')}">
    <meta charset="UTF-8">
    <title>流程管理</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">流程管理</h1>
            <div>
                <a th:href="@{/workflow/templates}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-list-ul"></i> 流程模板列表
                </a>
                <a th:href="@{/workflow/instances}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-list-check"></i> 流程实例列表
                </a>
                <a th:href="@{/workflow/templates/create}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> 创建流程模板
                </a>
            </div>
        </div>


        <!-- 最近流程模板和实例 -->
        <div class="row">
            <!-- 最近流程模板 -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-diagram-2"></i> 最近流程模板
                        </h5>
                        <a th:href="@{/workflow/templates}" class="btn btn-sm btn-link">查看全部</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" th:if="${recentTemplates != null && !recentTemplates.isEmpty()}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>模板名称</th>
                                        <th>适用范围</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="template : ${recentTemplates}">
                                        <td>
                                            <a th:href="@{/workflow/steps/template/{id}(id=${template.templateId})}" 
                                               th:text="${template.templateName != null ? template.templateName : '未命名模板'}">模板名称</a>
                                        </td>
                                        <td th:text="${template.applicableScope != null ? template.applicableScope : '-'}">适用范围</td>
                                        <td>
                                            <span th:if="${template.enabled == true}" class="badge bg-success">启用</span>
                                            <span th:unless="${template.enabled == true}" class="badge bg-secondary">禁用</span>
                                        </td>
                                        <td th:text="${template.createdDateTime != null ? #temporals.format(template.createdDateTime, 'yyyy-MM-dd') : 
                                               (template.createdDate != null ? #dates.format(template.createdDate, 'yyyy-MM-dd') : '-')}">创建时间</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${recentTemplates == null || recentTemplates.isEmpty()}">
                            <p class="text-muted mb-0">暂无流程模板</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近流程实例 -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-diagram-3"></i> 最近流程实例
                            <span th:if="${processingInstanceCount != null && processingInstanceCount > 0}" 
                                  class="badge bg-primary ms-2" 
                                  th:text="'处理中 ' + ${processingInstanceCount}">处理中 0</span>
                        </h5>
                        <a th:href="@{/workflow/instances}" class="btn btn-sm btn-link">查看全部</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" th:if="${recentInstances != null && !recentInstances.isEmpty()}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>流程标题</th>
                                        <th>状态</th>
                                        <th>流程进度</th>
                                        <th>发起人</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="instance : ${recentInstances}">
                                        <td>
                                            <a th:href="@{/workflow/instances/{id}(id=${instance.instanceId})}" 
                                               th:text="${instance.title != null ? instance.title : '未命名流程'}">流程标题</a>
                                        </td>
                                        <td>
                                            <span th:if="${instance.status != null && instance.status.name() == 'DRAFT'}" class="badge bg-secondary">草稿</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'PROCESSING'}" class="badge bg-primary">处理中</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'APPROVED'}" class="badge bg-success">已批准</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'REJECTED'}" class="badge bg-danger">已拒绝</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'CANCELED'}" class="badge bg-warning">已取消</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'TERMINATED'}" class="badge bg-dark">已终止</span>
                                            <span th:if="${instance.status == null}" class="badge bg-secondary">未知</span>
                                        </td>
                                        <td>
                                            <!-- 流程进度显示 -->
                                            <span class="badge bg-info" th:text="${(instanceSteps != null && instanceSteps[instance.instanceId] != null ? instanceSteps[instance.instanceId] : 0) + 
                                            '/' + (instance.stepCount != null ? instance.stepCount : 0) + ' 步'}">0/0 步</span>
                                        </td>
                                        <td th:text="${instance.initiator != null ? instance.initiator : '-'}">发起人</td>
                                        <td th:text="${instance.createdDateTime != null ? #temporals.format(instance.createdDateTime, 'yyyy-MM-dd') : 
                                               (instance.createdDate != null ? #dates.format(instance.createdDate, 'yyyy-MM-dd') : '-')}">创建时间</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${recentInstances == null || recentInstances.isEmpty()}">
                            <p class="text-muted mb-0">暂无流程实例</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
