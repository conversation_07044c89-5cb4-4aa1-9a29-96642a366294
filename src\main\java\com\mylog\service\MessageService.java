package com.mylog.service;

import com.mylog.model.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface MessageService {
    
    List<Message> findAllMessages();
    
    Optional<Message> findMessageById(Long id);
    
    List<Message> findMessagesByReceiver(String receiver);
    
    Page<Message> findMessagesByReceiver(String receiver, Pageable pageable);
    
    List<Message> findUnreadMessagesByReceiver(String receiver);
    
    Long countUnreadMessagesByReceiver(String receiver);
    
    Message saveMessage(Message message);
    
    void deleteMessage(Long id);
    
    void markAsRead(Long id);
    
    void markAllAsRead(String receiver);
    
    List<Message> findMessagesByRelatedTypeAndId(String relatedType, Long relatedId);
    
    /**
     * 统计消息总数
     * @return 消息总数
     */
    Long countTotalMessages();
} 