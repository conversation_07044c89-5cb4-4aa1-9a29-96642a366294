package com.mylog.service.impl;

import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.repository.workflow.WorkflowTemplateRepository; // 修正导入路径
import com.mylog.service.WorkflowTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 流程模板服务实现类
 */
@Service
public class WorkflowTemplateServiceImpl implements WorkflowTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowTemplateServiceImpl.class);

    @Autowired
    private WorkflowTemplateRepository templateRepository;
    
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowTemplate> findAllTemplates() {
        return templateRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkflowTemplate> findAllTemplates(Pageable pageable) {
        logger.info("查询所有流程模板，分页参数: 页码={}, 每页记录数={}",
            pageable.getPageNumber(), pageable.getPageSize());

        // 先查询总数，确认数据库中确实有记录
        long totalCount = templateRepository.count();
        logger.info("数据库中流程模板总数: {}", totalCount);

        Page<WorkflowTemplate> result = templateRepository.findAll(pageable);
        logger.info("查询结果: 总记录数={}, 当前页记录数={}",
            result.getTotalElements(), result.getContent().size());

        // 过滤掉content中的null元素（如果有的话）
        List<WorkflowTemplate> filteredContent = new java.util.ArrayList<>();
        for (WorkflowTemplate template : result.getContent()) {
            if (template != null) {
                filteredContent.add(template);
            } else {
                logger.warn("发现空的模板对象，已被过滤掉");
            }
        }
        
        // 如果有空元素被过滤掉，创建一个新的Page对象
        if (filteredContent.size() < result.getContent().size()) {
            logger.info("过滤前模板数量: {}, 过滤后模板数量: {}", 
                result.getContent().size(), filteredContent.size());
            
            // 创建一个新的Page实现，替换content为过滤后的内容
            result = new org.springframework.data.domain.PageImpl<>(
                filteredContent, 
                pageable, 
                result.getTotalElements() - (result.getContent().size() - filteredContent.size())
            );
        }

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowTemplate> findTemplateById(Long id) {
        Optional<WorkflowTemplate> templateOpt = templateRepository.findById(id);

        // 如果找到模板，确保步骤列表被初始化
        if (templateOpt.isPresent()) {
            WorkflowTemplate template = templateOpt.get();
            // 强制初始化步骤列表
            if (template.getSteps() != null) {
                logger.info("模板 ID: {}, 名称: {}, 步骤数量: {}",
                    template.getTemplateId(), template.getTemplateName(), template.getSteps().size());
            }
        }

        return templateOpt;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowTemplate> findTemplateByCode(String templateCode) {
        // 为保持向后兼容，调用新方法
        return findTemplateByTitle(templateCode);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<WorkflowTemplate> findTemplateByTitle(String templateTitle) {
        return templateRepository.findByTemplateTitle(templateTitle);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowTemplate> findTemplatesByScope(String applicableScope) {
        return templateRepository.findByApplicableScope(applicableScope);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowTemplate> findEnabledTemplatesByScope(String applicableScope) {
        return templateRepository.findByApplicableScopeAndEnabled(applicableScope, true);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<WorkflowTemplate> findByEnabled(Boolean enabled) {
        return templateRepository.findByEnabled(enabled);
    }

    @Override
    @Transactional
    public WorkflowTemplate saveTemplate(WorkflowTemplate template) {
        // 记录保存前的状态
        logger.debug("保存前的模板信息: ID={}, 名称={}", template.getTemplateId(), template.getTemplateName());
        
        try {
            // 使用LocalDateTime并格式化为yyyy-MM-dd HH:mm:ss
            LocalDateTime now = LocalDateTime.now();
            boolean isNew = template.getTemplateId() == null;
            
            // 如果是更新操作而非新建，需要处理现有步骤和保留创建信息
            if (!isNew) {
                // 先获取数据库中的原始模板，包括其所有步骤
                Optional<WorkflowTemplate> existingTemplateOpt = templateRepository.findById(template.getTemplateId());
                if (existingTemplateOpt.isPresent()) {
                    WorkflowTemplate existingTemplate = existingTemplateOpt.get();
                    
                    // 保留原有的创建人信息
                    template.setCreatedBy(existingTemplate.getCreatedBy());
                    
                    // 保留原有的创建时间信息
                    template.setCreatedDate(existingTemplate.getCreatedDate());
                    logger.info("更新模板时保留原有创建信息: 创建人={}, 创建时间={}", 
                        existingTemplate.getCreatedBy(), existingTemplate.getCreatedDate());
                    
                    // 强制初始化步骤集合
                    if (existingTemplate.getSteps() != null) {
                        org.hibernate.Hibernate.initialize(existingTemplate.getSteps());
                        logger.info("保存模板前，已获取现有步骤数据，步骤数量: {}", existingTemplate.getSteps().size());
                        // 保存已有的步骤到新模板（防止被删除）
                        template.setSteps(existingTemplate.getSteps());
                    }
                }
            }
            
            if (isNew) {
                // 使用我们添加的格式化方法设置日期
                template.setCreatedDateTime(now);
                // 生成新的ID
                template.setTemplateId(generateNewTemplateId());
            }
            // 使用我们添加的格式化方法设置日期
            template.setLastModifiedDateTime(now);
            
            // 如果是新记录，先检查启用状态，默认为启用
            if (isNew && template.getEnabled() == null) {
                template.setEnabled(true);
            }
            
            // 使用JPA保存模板
            WorkflowTemplate savedTemplate = templateRepository.save(template);
            
            // 确认保存后的ID
            logger.info("JPA保存后的模板信息: ID={}, 名称={}", savedTemplate.getTemplateId(), savedTemplate.getTemplateName());
            
            // 刷新以确保数据已写入数据库
            templateRepository.flush();
            
            return savedTemplate;
        } catch (Exception e) {
            logger.error("保存模板时出错: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 生成新的模板ID
     * 通过查询当前最大ID然后加1的方式实现
     */
    private Long generateNewTemplateId() {
        // 查询当前最大ID
        Long maxId = templateRepository.findMaxTemplateId();
        logger.info("当前最大模板ID: {}", maxId);
        
        // 如果没有现有记录或查询结果为null，从1开始
        return maxId != null ? maxId + 1 : 1L;
    }

    @Override
    @Transactional
    public void deleteTemplate(Long id) {
        templateRepository.deleteById(id);
    }

    @Override
    @Transactional
    public WorkflowTemplate toggleTemplateStatus(Long id, boolean enabled) {
        try {
            // 使用JPA方法更新状态
            Optional<WorkflowTemplate> templateOpt = templateRepository.findById(id);
            if (templateOpt.isPresent()) {
                WorkflowTemplate template = templateOpt.get();
                template.setEnabled(enabled);
                // 使用LocalDateTime并通过格式化方法设置日期
                template.setLastModifiedDateTime(LocalDateTime.now());
                return templateRepository.save(template);
            } else {
                logger.warn("切换模板状态失败，未找到ID: {}", id);
                return null;
            }
        } catch (Exception e) {
            logger.error("切换模板状态时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkflowTemplate> searchTemplates(String keyword, String applicableScope, Boolean enabled, Pageable pageable) {
        logger.info("搜索流程模板，关键词: {}, 适用范围: {}, 启用状态: {}, 分页参数: 页码={}, 每页记录数={}",
            keyword != null ? keyword : "无",
            applicableScope != null ? applicableScope : "无",
            enabled != null ? enabled : "无",
            pageable.getPageNumber(), pageable.getPageSize());

        Page<WorkflowTemplate> result = templateRepository.searchTemplates(keyword, applicableScope, enabled, pageable);
        logger.info("搜索结果: 总记录数={}, 当前页记录数={}",
            result.getTotalElements(), result.getContent().size());

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkflowTemplate> findAllTemplatesByNativeQuery() {
        logger.info("使用原生SQL查询所有流程模板");

        // 使用EntityManager直接执行原生SQL查询
        List<WorkflowTemplate> templates = templateRepository.findAllByNativeQuery();

        logger.info("原生SQL查询结果: 记录数={}", templates.size());

        // 过滤掉null元素并创建新列表
        List<WorkflowTemplate> filteredTemplates = new java.util.ArrayList<>();
        for (WorkflowTemplate template : templates) {
            if (template != null) {
                filteredTemplates.add(template);
            } else {
                logger.warn("发现空的模板对象，已被过滤掉");
            }
        }
        
        // 使用过滤后的列表替换原列表
        templates = filteredTemplates;

        // 打印每个模板的详细信息，用于调试
        for (WorkflowTemplate template : templates) {
            logger.info("模板ID: {}, 名称: {}, 标题: {}, 启用状态: {}",
                template.getTemplateId(),
                template.getTemplateName(),
                template.getTemplateTitle(),
                template.getEnabled());
        }

        return templates;
    }
}
