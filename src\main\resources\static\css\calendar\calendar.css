/* 日历页面布局对齐样式 */
.calendar-layout {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
}

.calendar-layout .col-md-2,
.calendar-layout .col-md-10 {
    padding-left: 15px;
    padding-right: 15px;
    margin: 0;
    display: flex;
    flex-direction: column;
}

.calendar-layout .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
    max-width: 16.66666667%;
}

.calendar-layout .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
    max-width: 83.33333333%;
    margin-left: 0;
    min-height: 600px;
}

/* 确保日历布局在所有屏幕尺寸下正确对齐 */
@media (min-width: 768px) {
    .calendar-layout .col-md-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
        max-width: 16.66666667%;
    }
    
    .calendar-layout .col-md-10 {
        flex: 0 0 auto;
        width: 83.33333333%;
        max-width: 83.33333333%;
        margin-left: 0;
    }
}

/* 日历组件样式 */
.calendar-container {
    min-height: 600px;
}

/* 月视图样式 */
.month-view {
    width: 100%;
    height: 600px;
}

.month-view table {
    width: 100%;
    height: 100%;
    border-collapse: collapse;
}

.month-view th,
.month-view td {
    border: 1px solid #dee2e6;
    vertical-align: top;
    position: relative;
}

.month-view th {
    height: 40px;
    background-color: #f8f9fa;
    text-align: center;
    font-weight: 600;
    color: #495057;
}

.month-view td {
    height: 100px;
    padding: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.month-view td:hover {
    background-color: #f8f9fa;
}

.month-view .day-number {
    font-weight: 500;
    margin-bottom: 2px;
    font-size: 14px;
}

.month-view .other-month {
    color: #6c757d;
    background-color: #f8f9fa;
}

.month-view .today {
    background-color: #e3f2fd;
}

.month-view .today .day-number {
    color: #1976d2;
    font-weight: 700;
}

.month-view .selected {
    background-color: #bbdefb;
}

/* 事件样式 */
.event-item {
    /* 移除固定的背景色和文字颜色，由日历动态设置 */
    font-size: 11px;
    padding: 1px 4px;
    margin: 1px 0;
    border-radius: 3px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.2s;
    border-width: 1px;
    border-style: solid;
}

.event-item:hover {
    transform: scale(1.02);
    opacity: 0.8;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 优先级标识 - 使用边框来区分优先级，保持日历颜色为主色 */
.event-item.priority-low {
    border-width: 1px;
    border-style: solid;
}

.event-item.priority-normal {
    border-width: 2px;
    border-style: solid;
}

.event-item.priority-high {
    border-width: 2px;
    border-style: solid;
    font-weight: bold;
}

.event-item.priority-urgent {
    border-width: 3px;
    border-style: solid;
    font-weight: bold;
    animation: pulse 2s infinite;
}

/* 紧急事件的脉冲动画 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 事件类型标识 - 在日历颜色基础上添加类型特征 */
.event-item.type-meeting::before {
    content: "👥 ";
    font-size: 10px;
}

.event-item.type-task::before {
    content: "📋 ";
    font-size: 10px;
}

.event-item.type-appointment::before {
    content: "📅 ";
    font-size: 10px;
}

.event-item.type-reminder::before {
    content: "🔔 ";
    font-size: 10px;
}

.event-item.type-other::before {
    content: "📝 ";
    font-size: 10px;
}

.event-item.all-day {
    border-radius: 0;
    margin: 0 -4px 1px -4px;
    font-weight: 500;
}

/* 周视图样式 */
.week-view {
    display: none;
}

.week-view table {
    width: 100%;
    border-collapse: collapse;
}

.week-view .time-column {
    width: 60px;
    border-right: 2px solid #dee2e6;
}

.week-view .day-column {
    width: calc((100% - 60px) / 7);
    border-right: 1px solid #dee2e6;
    position: relative;
}

.week-view .hour-row {
    height: 60px;
    border-bottom: 1px solid #f0f0f0;
}

.week-view .hour-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    padding: 4px;
}

/* 日视图样式 */
.day-view {
    display: none;
}

/* 小日历样式 */
.mini-calendar {
    font-size: 12px;
}

.mini-calendar table {
    width: 100%;
    border-collapse: collapse;
}

.mini-calendar th,
.mini-calendar td {
    text-align: center;
    padding: 4px 2px;
    border: none;
}

.mini-calendar th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.mini-calendar td {
    cursor: pointer;
    transition: background-color 0.2s;
}

.mini-calendar td:hover {
    background-color: #e9ecef;
}

.mini-calendar .today {
    background-color: #007bff;
    color: white;
    border-radius: 50%;
}

.mini-calendar .other-month {
    color: #6c757d;
}

.mini-calendar .selected {
    background-color: #0056b3;
    color: white;
    border-radius: 50%;
}

/* 日历列表样式 */
.calendar-list-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.calendar-list-item:hover {
    background-color: #f8f9fa;
}

.calendar-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px #dee2e6;
}

.calendar-name {
    flex: 1;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.calendar-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.calendar-list-item:hover .calendar-actions {
    opacity: 1;
}

.calendar-checkbox {
    margin-right: 4px;
}

/* 提醒样式 */
.reminder-item {
    margin-bottom: 12px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.reminder-columns {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 1fr;
    gap: 12px;
    align-items: start;
    margin-bottom: 8px;
}

.reminder-col-1 {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.reminder-field {
    margin-bottom: 4px;
}

.reminder-field:last-child {
    margin-bottom: 0;
}

.reminder-col-2,
.reminder-col-3,
.reminder-col-4 {
    display: flex;
    align-items: flex-start;
    min-height: 84px; /* 大约3个字段的高度 */
}

.reminder-col-2 select {
    width: 100%;
    height: 80px; /* 适应多选框的高度 */
}

.reminder-col-3 .form-check {
    margin-top: 8px;
}

.reminder-col-4 .check-in-window {
    width: 100%;
    margin-top: 8px;
}

.reminder-actions {
    text-align: right;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
}

.reminder-item .btn-remove {
    width: auto;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .reminder-columns {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .reminder-col-2,
    .reminder-col-3,
    .reminder-col-4 {
        min-height: auto;
    }
    
    .reminder-col-2 select {
        height: auto;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .month-view td {
        height: 80px;
        padding: 2px;
    }
    
    .month-view .day-number {
        font-size: 12px;
    }
    
    .event-item {
        font-size: 10px;
        padding: 1px 2px;
    }
    
    .btn-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-group {
        margin-bottom: 8px;
    }
}

/* 响应式设计优化 */
@media (max-width: 767.98px) {
    .calendar-layout .col-md-2,
    .calendar-layout .col-md-10 {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 1rem;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .calendar-layout .col-md-2 {
        width: 25% !important;
        max-width: 25% !important;
    }
    
    .calendar-layout .col-md-10 {
        width: 75% !important;
        max-width: 75% !important;
    }
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #6c757d;
}

.loading .spinner-border {
    margin-right: 8px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

/* 事件详情样式 */
.event-detail-item {
    margin-bottom: 12px;
}

.event-detail-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.event-detail-value {
    color: #6c757d;
}

.event-detail-priority {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.event-detail-priority.low {
    background-color: #e9ecef;
    color: #6c757d;
}

.event-detail-priority.normal {
    background-color: #cce5ff;
    color: #0056b3;
}

.event-detail-priority.high {
    background-color: #fff3cd;
    color: #856404;
}

.event-detail-priority.urgent {
    background-color: #f8d7da;
    color: #721c24;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 按钮组活跃状态 */
.btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* 主题适配 */
.theme-dark .month-view th {
    background-color: #343a40;
    color: #f8f9fa;
    border-color: #495057;
}

.theme-dark .month-view td {
    border-color: #495057;
    background-color: #212529;
    color: #f8f9fa;
}

.theme-dark .month-view td:hover {
    background-color: #343a40;
}

.theme-dark .month-view .other-month {
    background-color: #343a40;
    color: #6c757d;
}

.theme-dark .calendar-list-item:hover {
    background-color: #343a40;
}

/* 日历卡片对齐优化 */
.calendar-layout .card {
    margin-bottom: 1rem;
}

.calendar-layout .col-md-2 .card,
.calendar-layout .col-md-10 .card {
    height: auto;
    display: flex;
    flex-direction: column;
}

.calendar-layout .col-md-10 .card {
    min-height: 600px;
}

.calendar-layout .col-md-2 .card .card-body,
.calendar-layout .col-md-10 .card .card-body {
    flex: 1;
}

/* 确保卡片在不同屏幕尺寸下的正确显示 */
.calendar-layout .card {
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.calendar-layout .card-header {
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* 签到相关样式 */
.check-in-section {
    margin-top: 8px;
}

.check-in-btn {
    transition: all 0.2s ease;
}

.check-in-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.check-in-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.check-in-status.checked-in {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.check-in-status.not-checked-in {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.check-in-status.window-closed {
    background-color: #e2e3e5;
    color: #6c757d;
    border: 1px solid #d6d8db;
}

.reminder-item {
    padding: 4px 0;
    border-bottom: 1px solid #e9ecef;
}

.reminder-item:last-child {
    border-bottom: none;
}

.reminder-item .badge {
    font-size: 10px;
    margin-left: 4px;
}

.check-in-history {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
}

.check-in-history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    font-size: 13px;
}

.check-in-history-time {
    color: #6c757d;
    font-size: 12px;
}

.check-in-location {
    font-size: 11px;
    color: #adb5bd;
    margin-top: 2px;
}

/* 签到统计样式 */
.check-in-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin: 16px 0;
}

.check-in-stat-item {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.check-in-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.check-in-stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

/* 签到列表样式 */
.check-in-list {
    max-height: 400px;
    overflow-y: auto;
}

.check-in-list-item {
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 8px;
    background: #fff;
    transition: box-shadow 0.2s ease;
}

.check-in-list-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.check-in-list-item .event-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.check-in-list-item .check-in-info {
    font-size: 13px;
    color: #6c757d;
}

.check-in-list-item .check-in-time {
    font-size: 12px;
    color: #adb5bd;
    margin-top: 4px;
}

/* 签到窗口提示样式 */
.check-in-window {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 13px;
    color: #856404;
}

.check-in-window.active {
    background: linear-gradient(45deg, #d4edda, #a8e6cf);
    border-color: #a8e6cf;
    color: #155724;
}

.check-in-window.expired {
    background: linear-gradient(45deg, #f8d7da, #ffb3ba);
    border-color: #ffb3ba;
    color: #721c24;
}

/* 事件表单2列布局样式 */
.event-form-two-column .row {
    margin-bottom: 1rem;
}

.event-form-two-column .form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.event-form-two-column .form-check {
    margin-bottom: 0.5rem;
}

.event-form-two-column .form-check:last-child {
    margin-bottom: 0;
}

/* 重复事件配置3列布局 */
.recurring-options-three-column {
    display: flex;
    gap: 1rem;
}

.recurring-options-three-column .col-md-4 {
    flex: 1;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
    .event-form-two-column .col-md-6 {
        margin-bottom: 1rem;
    }
    
    .recurring-options-three-column {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .recurring-options-three-column .col-md-4 {
        width: 100%;
        margin-bottom: 0.75rem;
    }
}
