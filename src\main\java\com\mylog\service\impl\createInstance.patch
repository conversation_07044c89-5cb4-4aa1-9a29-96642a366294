    @Override
    @Transactional
    public WorkflowInstance createInstance(WorkflowInstance instance) {
        // 设置创建时间和状态
        instance.setCreatedDateTime(LocalDateTime.now());
        instance.setStatus(WorkflowStatus.DRAFT);

        // 设置步骤数量并获取第一步审批人
        if (instance.getTemplate() != null) {
            List<WorkflowStep> steps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
            instance.setStepCount(steps.size());
            
            // 从模板中读取第一步的审批人信息，并设置到流程实例中
            if (!steps.isEmpty()) {
                // 按照步骤顺序排序
                steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));
                
                // 获取第一步
                WorkflowStep firstStep = steps.get(0);
                if (firstStep != null && firstStep.getApproverConfig() != null && !firstStep.getApproverConfig().isEmpty()) {
                    // 将第一步的审批人配置设置为当前审批人
                    instance.setCurrentApprover(firstStep.getApproverConfig());
                    logger.info("已从模板中读取第一步审批人配置: {}", firstStep.getApproverConfig());
                }
            }
        }

        return instanceRepository.save(instance);
    }
