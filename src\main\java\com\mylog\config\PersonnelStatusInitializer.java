package com.mylog.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mylog.service.PersonnelStatusService;

/**
 * 人员状态初始化器，在应用启动时初始化人员状态表
 */
@Component
@Order(4) // 在OptionsInitializer之后运行
public class PersonnelStatusInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(PersonnelStatusInitializer.class);

    @Autowired
    private PersonnelStatusService personnelStatusService;

    @Override
    public void run(String... args) throws Exception {
        logger.info("开始初始化人员状态表...");

        try {
            // 初始化人员状态表
            personnelStatusService.initializePersonnelStatus();
            logger.info("人员状态表初始化完成");
        } catch (Exception e) {
            logger.error("初始化人员状态表时发生错误: {}", e.getMessage(), e);
        }
    }
}
