package com.mylog.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.mylog.service.ProjectService;
import com.mylog.service.TaskService;
import com.mylog.service.MessageService;
import com.mylog.service.UserService;
import com.mylog.service.PersonnelStatusService;
import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.model.PersonnelStatus;

import java.util.List;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
public class HomeController {
    
    private static final Logger logger = LoggerFactory.getLogger(HomeController.class);
    
    @Autowired
    private ProjectService projectService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private MessageService messageService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PersonnelStatusService personnelStatusService;
    
    @GetMapping("/")
    public String home() {
        return "redirect:/dashboard";
    }
    
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        try {
            // 设置活动菜单
            model.addAttribute("activeMenu", "dashboard");
            
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            logger.info("当前用户: {}", currentUsername);
            
            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            });
            
            // 获取未归档项目总数
            long nonArchivedProjectCount = projectService.countNonArchivedProjects();
            model.addAttribute("nonArchivedProjectCount", nonArchivedProjectCount);
            
            // 获取未归档且进行中的项目数
            long nonArchivedInProgressProjectCount = projectService.countNonArchivedProjectsByStatus("进行中");
            model.addAttribute("nonArchivedInProgressProjectCount", nonArchivedInProgressProjectCount);
            
            // 获取未归档项目中的任务总数
            long nonArchivedTaskCount = taskService.findTasksFromNonArchivedProjects(PageRequest.of(0, 1)).getTotalElements();
            model.addAttribute("nonArchivedTaskCount", nonArchivedTaskCount);
            
            // 获取未归档项目中进行中的任务数
            long nonArchivedInProgressTaskCount = taskService.countNonArchivedInProgressTasks();
            model.addAttribute("nonArchivedInProgressTaskCount", nonArchivedInProgressTaskCount);
            
            // 获取消息总数
            long totalMessageCount = messageService.countTotalMessages();
            model.addAttribute("totalMessageCount", totalMessageCount);
            
            // 获取视觉程序项目数
            long visionProgramProjectCount = projectService.countProjectsByCustomerName("视觉程序发布");
            model.addAttribute("visionProgramProjectCount", visionProgramProjectCount);
            
            // 获取技术资料项目数
            long technicalDocumentProjectCount = projectService.countProjectsByCustomerName("技术资料发布");
            model.addAttribute("technicalDocumentProjectCount", technicalDocumentProjectCount);
            
// 参考上面的程序，增加获取视觉程序任务数，和技术资料任务数
            long visionProgramTaskCount = taskService.countTasksByProjectCustomerName("视觉程序发布");
            model.addAttribute("visionProgramTaskCount", visionProgramTaskCount);
            long technicalDocumentTaskCount = taskService.countTasksByProjectCustomerName("技术资料发布");
            model.addAttribute("technicalDocumentTaskCount", technicalDocumentTaskCount);

            // 获取最近10个未归档的项目
            List<Project> recentProjects = projectService.findRecentNonArchivedProjects(10);
            model.addAttribute("recentProjects", recentProjects);
            
            // 获取最近10个未归档项目的任务
            List<ProjectTask> recentTasks = taskService.findRecentTasksFromNonArchivedProjects(10);
            model.addAttribute("recentTasks", recentTasks);
            
            // 获取有效的人员状态记录
            List<PersonnelStatus> validPersonnelStatus = personnelStatusService.getValidPersonnelStatusRecords(30);
            model.addAttribute("validPersonnelStatus", validPersonnelStatus);
            
        } catch (Exception e) {
            logger.error("仪表盘加载出错: {}", e.getMessage(), e);
            // 添加默认数据，确保页面能够正常显示
            model.addAttribute("activeMenu", "dashboard");
            model.addAttribute("userTheme", "theme-light");
            model.addAttribute("nonArchivedProjectCount", 0);
            model.addAttribute("nonArchivedInProgressProjectCount", 0);
            model.addAttribute("nonArchivedTaskCount", 0);
            model.addAttribute("nonArchivedInProgressTaskCount", 0);
            model.addAttribute("totalMessageCount", 0);
            model.addAttribute("visionProgramProjectCount", 0);
            model.addAttribute("technicalDocumentProjectCount", 0);
            model.addAttribute("recentProjects", new ArrayList<>());
            model.addAttribute("recentTasks", new ArrayList<>());
            model.addAttribute("validPersonnelStatus", new ArrayList<>());
        }
        
        return "dashboard";
    }
    
    @GetMapping("/login")
    public String login() {
        return "login";
    }
} 