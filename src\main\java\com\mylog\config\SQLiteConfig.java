package com.mylog.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = "com.mylog.repository",
    excludeFilters = @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.mylog\\.repository\\.user\\..*"),
    entityManagerFactoryRef = "entityManagerFactory",
    transactionManagerRef = "logTransactionManager"
)
public class SQLiteConfig {
    
    @Value("${spring.datasource.url}")
    private String logDbUrl;
    
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    
    @Value("${spring.user-datasource.url}")
    private String userDbUrl;
    
    @Bean(name = "logDataSource")
    @Primary
    public DataSource logDataSource() {
        // 使用HikariCP连接池
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(driverClassName);
        // 添加SQLite的PRAGMA设置到URL
        String url = logDbUrl;
        if (!url.contains("?")) {
            url += "?journal_mode=DELETE&synchronous=NORMAL&foreign_keys=ON&busy_timeout=30000";
        }
        config.setJdbcUrl(url);
        
        // 连接池配置
        config.setMaximumPoolSize(5); // SQLite不需要太多连接
        config.setMinimumIdle(1);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setLeakDetectionThreshold(60000);
        config.setAutoCommit(false); // 明确禁用自动提交
        
        return new HikariDataSource(config);
    }

    @Bean(name = "userDataSource")
    public DataSource userDataSource() {
        // 使用HikariCP连接池
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(driverClassName);
        // 添加SQLite的PRAGMA设置到URL
        String url = userDbUrl;
        if (!url.contains("?")) {
            url += "?journal_mode=DELETE&synchronous=NORMAL&foreign_keys=ON&busy_timeout=30000";
        }
        config.setJdbcUrl(url);
        
        // 连接池配置
        config.setMaximumPoolSize(3); // 用户数据库连接更少
        config.setMinimumIdle(1);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setAutoCommit(false); // 明确禁用自动提交
        
        return new HikariDataSource(config);
    }
    
    @Primary
    @Bean(name = "entityManagerFactory")
    public LocalContainerEntityManagerFactoryBean logEntityManagerFactory() {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(logDataSource());
        em.setPackagesToScan("com.mylog.model");
        
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabasePlatform("org.hibernate.community.dialect.SQLiteDialect");
        vendorAdapter.setShowSql(true);
        vendorAdapter.setGenerateDdl(true);
        em.setJpaVendorAdapter(vendorAdapter);
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.community.dialect.SQLiteDialect");
        properties.put("hibernate.hbm2ddl.auto", "update");
        properties.put("hibernate.show_sql", "true");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.connection.charSet", "UTF-8");
        properties.put("hibernate.enable_lazy_load_no_trans", "true");
        
        // SQLite特定配置 - 移除隔离级别设置，SQLite不支持
        // properties.put("hibernate.connection.isolation", "8"); // SERIALIZABLE
        properties.put("hibernate.connection.autocommit", "false");
        properties.put("hibernate.connection.release_mode", "after_transaction");
        
        // SQLite PRAGMA设置
        properties.put("hibernate.connection.provider_disables_autocommit", "true");
        properties.put("hibernate.connection.characterEncoding", "utf8");
        properties.put("hibernate.connection.CharSet", "utf8");
        properties.put("hibernate.connection.useUnicode", "true");
        
        // 移除连接池配置，因为现在使用HikariCP
        // properties.put("hibernate.connection.pool_size", "10");
        // 移除 thread session context，让Spring管理事务
        // properties.put("hibernate.current_session_context_class", "thread");
        
        // 设置命名策略
        properties.put("hibernate.physical_naming_strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        properties.put("hibernate.implicit_naming_strategy", "org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl");
        
        // 设置批处理大小
        properties.put("hibernate.jdbc.batch_size", "50");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.jdbc.batch_versioned_data", "true");
        
        em.setJpaPropertyMap(properties);
        
        return em;
    }
    
    @Primary
    @Bean(name = "logTransactionManager")
    public PlatformTransactionManager logTransactionManager() {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(logEntityManagerFactory().getObject());
        return transactionManager;
    }
}