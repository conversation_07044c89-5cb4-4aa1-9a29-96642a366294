package com.mylog.config;

import com.mylog.service.WorkflowInstanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 流程相关数据拦截器
 * 用于在所有页面加载时添加处理中的流程实例数量
 */
@Component
public class WorkflowInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowInterceptor.class);

    @Autowired
    private WorkflowInstanceService instanceService;

    @Override
    public void postHandle(@NonNull HttpServletRequest request,
                          @NonNull HttpServletResponse response,
                          @NonNull Object handler,
                          @Nullable ModelAndView modelAndView) throws Exception {
        if (modelAndView != null) {
            try {
                // 获取处理中的流程实例数量
                long processingCount = instanceService.countProcessingInstances();
                // 将数量添加到模型中，供所有视图使用
                modelAndView.addObject("processingInstanceCount", processingCount);
            } catch (Exception e) {
                logger.error("获取处理中流程实例数量失败: {}", e.getMessage(), e);
                // 发生错误时设置为0
                modelAndView.addObject("processingInstanceCount", 0);
            }
        }
    }
}
