@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
echo MyLog Web应用程序打包部署脚本
echo ==================================

set DEPLOY_DIR=mylog-web
set JAR_FILE=mylog-web-0.0.1-SNAPSHOT.jar
set TARGET_DB_DIR=%DEPLOY_DIR%\data

echo 执行Maven构建...
call mvn clean package -DskipTests
if %ERRORLEVEL% NEQ 0 (
    echo 错误：Maven构建失败
    pause
    exit /b 1
)

echo 检查并创建目标目录...
if not exist %DEPLOY_DIR% mkdir %DEPLOY_DIR%
if not exist %TARGET_DB_DIR% mkdir %TARGET_DB_DIR%

echo 检查JAR文件是否存在...
if not exist target\%JAR_FILE% (
    echo 错误：JAR文件不存在：target\%JAR_FILE%
    echo 请先运行 mvn clean package 构建项目
    pause
    exit /b 1
)

echo 复制JAR文件...
copy /Y target\%JAR_FILE% %DEPLOY_DIR%\%JAR_FILE%

echo 复制配置文件...
copy /Y target\classes\application.properties %DEPLOY_DIR%\
copy /Y target\classes\logback-spring.xml %DEPLOY_DIR%\

echo 检查版本信息是否正确替换...
findstr "app.build.version" %DEPLOY_DIR%\application.properties | findstr "@build.timestamp.version@"
if %ERRORLEVEL% EQU 0 (
    echo 警告：版本信息未正确替换，可能需要检查Maven配置
) else (
    echo 版本信息已正确替换
)

echo 复制数据库文件...
copy /Y data555\*.* %TARGET_DB_DIR%\

echo 复制所有内容到Y盘...
if not exist Y:\ (
    echo 错误：Y盘不存在，请确保Y盘已正确映射
    exit /b 1
)

echo 检查Y盘目标位置是否已存在JAR文件...
if exist "Y:\%DEPLOY_DIR%\%JAR_FILE%" (
    echo 发现已存在的JAR文件，正在备份...
    
    REM 获取当前时间戳 - 使用更可靠的方法
    for /f "tokens=1-4 delims=/ " %%a in ('date /t') do (
        set "curr_date=%%d%%b%%c"
    )
    for /f "tokens=1-2 delims=: " %%a in ('time /t') do (
        set "curr_time=%%a%%b"
    )
    
    REM 使用PowerShell获取更精确的时间戳
    for /f %%i in ('powershell -command "Get-Date -Format 'yyyyMMdd_HHmmss'"') do set "timestamp=%%i"
    
    REM 备份现有JAR文件
    set "backup_name=mylog-web-0.0.1-SNAPSHOT_!timestamp!.jar"
    echo 备份文件名: !backup_name!
    echo 执行备份命令: move "Y:\%DEPLOY_DIR%\%JAR_FILE%" "Y:\%DEPLOY_DIR%\!backup_name!"
    
    REM 执行move命令并立即保存错误级别
    move "Y:\%DEPLOY_DIR%\%JAR_FILE%" "Y:\%DEPLOY_DIR%\!backup_name!"
    set MOVE_ERROR=!ERRORLEVEL!
    
    echo 备份命令执行后的错误级别: !MOVE_ERROR!
    
    REM 检查备份是否成功
    if !MOVE_ERROR! EQU 0 (
        echo 备份成功: !backup_name!
    ) else (
        echo 错误：备份失败，错误级别: !MOVE_ERROR!
        echo 可能的原因：
        echo - Y盘空间不足，或文件正在被使用
        echo - 权限不足
        pause
        exit /b 1
    )
    
    REM 验证备份文件是否存在
    if exist "Y:\%DEPLOY_DIR%\!backup_name!" (
        echo 验证成功：备份文件已创建
    ) else (
        echo 错误：备份文件未找到，尽管move命令返回成功
        pause
        exit /b 1
    )
    
) else (
    echo Y盘目标位置不存在JAR文件，可以直接复制
)

xcopy /E /I /Y %DEPLOY_DIR% Y:\%DEPLOY_DIR%

echo 执行目标数据库备份...

if exist "Y:\%DEPLOY_DIR%\backup-databases.bat" (
    echo 找到备份脚本，开始执行数据库备份...
    pushd "Y:\%DEPLOY_DIR%"
    call backup-databases.bat
    popd
    if %ERRORLEVEL% EQU 0 (
        echo 数据库备份完成
    ) else (
        echo 警告：数据库备份失败，错误代码: %ERRORLEVEL%
    )
) else (
    echo 警告：未找到备份脚本 Y:\%DEPLOY_DIR%\backup-databases.bat
)

echo 部署完成！
echo *
echo *
echo *
echo 部署文件夹: %DEPLOY_DIR%
echo 然后运行startup.bat启动应用
pause
