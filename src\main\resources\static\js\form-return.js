/**
 * 表单返回处理脚本
 * 用于处理表单提交后返回上一页的逻辑
 */

// 页面加载时执行
document.addEventListener('DOMContentLoaded', function() {
    // 标记当前页面为表单页面
    sessionStorage.setItem('isFormPage', 'true');
    
    // 检查URL参数中是否有success=true
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('success') === 'true') {
        // 如果有success=true参数，说明是表单提交成功后的重定向
        // 从localStorage中获取返回URL
        const returnUrl = localStorage.getItem('returnUrl');
        if (returnUrl) {
            // 清除localStorage中的returnUrl
            localStorage.removeItem('returnUrl');
            // 重定向到返回URL
            window.location.href = returnUrl;
        }
    }
    
    // 为所有表单添加提交事件监听器
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        // 排除搜索表单、删除表单和模态框中的表单
        if (!form.id.includes('search') && !form.action.includes('/delete') && !form.closest('.modal')) {
            form.addEventListener('submit', function() {
                // 获取当前页面的历史记录
                let historyStack = [];
                try {
                    const storedHistory = sessionStorage.getItem('pageHistory');
                    if (storedHistory) {
                        historyStack = JSON.parse(storedHistory);
                    }
                } catch (e) {
                    console.error('Error retrieving history from sessionStorage:', e);
                }
                
                // 如果历史记录中有上一页，则使用上一页作为返回URL
                if (historyStack.length > 1) {
                    // 使用倒数第二个URL作为返回URL（倒数第一个是当前页面）
                    localStorage.setItem('returnUrl', historyStack[historyStack.length - 2]);
                } else {
                    // 如果没有历史记录，则使用referrer作为返回URL
                    const referer = document.querySelector('input[name="referer"]')?.value;
                    localStorage.setItem('returnUrl', referer || document.referrer || '/dashboard');
                }
            });
        }
    });
    
    // 为所有取消按钮添加点击事件监听器
    const cancelButtons = document.querySelectorAll('button[type="button"].btn-secondary, a.btn-secondary');
    cancelButtons.forEach(function(button) {
        if (button.textContent.trim() === '取消' && !button.hasAttribute('onclick')) {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                handleFormCancel();
            });
        }
    });
});

/**
 * 处理表单取消操作
 */
function handleFormCancel() {
    // 获取当前页面的历史记录
    let historyStack = [];
    try {
        const storedHistory = sessionStorage.getItem('pageHistory');
        if (storedHistory) {
            historyStack = JSON.parse(storedHistory);
        }
    } catch (e) {
        console.error('Error retrieving history from sessionStorage:', e);
    }
    
    // 首先尝试获取隐藏的referer字段
    const refererInput = document.querySelector('input[name="referer"]');
    if (refererInput && refererInput.value) {
        window.location.href = refererInput.value;
        return;
    }
    
    // 如果历史记录中有上一页，使用智能返回
    if (historyStack.length > 1) {
        if (typeof handleSmartBack === 'function') {
            handleSmartBack();
        } else {
            // 找到最近的非表单页面
            for (let i = historyStack.length - 2; i >= 0; i--) {
                const url = historyStack[i];
                if (!isFormPageUrl(url)) {
                    window.location.href = url;
                    return;
                }
            }
            // 如果没有找到合适的页面，返回首页
            window.location.href = '/dashboard';
        }
    } else {
        // 如果没有历史记录，尝试使用referrer
        if (document.referrer && !isFormPageUrl(document.referrer)) {
            window.location.href = document.referrer;
        } else {
            // 如果referrer也不可用，返回首页
            window.location.href = '/dashboard';
        }
    }
}

/**
 * 检查URL是否是表单页面
 * @param {string} url - 要检查的URL
 * @returns {boolean} - 如果是表单页面则返回true，否则返回false
 */
function isFormPageUrl(url) {
    const formPatterns = [
        '/new',
        '/edit',
        '/form'
    ];
    
    return formPatterns.some(pattern => url.includes(pattern));
} 