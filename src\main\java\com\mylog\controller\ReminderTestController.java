package com.mylog.controller;

import com.mylog.dto.CalendarEventDTO;
import com.mylog.dto.EventReminderDTO;
import com.mylog.model.CalendarEvent;
import com.mylog.model.EventReminder;
import com.mylog.service.CalendarEventService;
import com.mylog.service.CalendarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提醒功能测试控制器
 */
@RestController
@RequestMapping("/api/test")
@Slf4j
public class ReminderTestController {
    
    @Autowired
    private CalendarEventService eventService;
    
    @Autowired
    private CalendarService calendarService;
    
    /**
     * 创建测试事件（带有接收人的提醒）
     */
    @PostMapping("/create-reminder-event")
    public ResponseEntity<Map<String, Object>> createReminderTestEvent() {
        try {
            log.info("开始创建带有接收人的测试事件");
              // 获取第一个日历
            Long currentUserId = 1L; // 使用默认用户ID，实际应用中应从SecurityContext获取
            var calendars = calendarService.getCalendarsByUserId(currentUserId);
            if (calendars.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "没有可用的日历"
                ));
            }
            
            var calendar = calendars.get(0);
            
            // 创建测试事件
            CalendarEventDTO eventDTO = new CalendarEventDTO();
            eventDTO.setTitle("提醒接收人测试事件");
            eventDTO.setDescription("这是一个测试事件，用于验证提醒接收人功能");
            eventDTO.setStartTime(LocalDateTime.now().plusHours(2)); // 2小时后开始
            eventDTO.setEndTime(LocalDateTime.now().plusHours(3));   // 3小时后结束
            eventDTO.setEventType(CalendarEvent.EventType.MEETING);
            eventDTO.setPriority(CalendarEvent.Priority.NORMAL);
            eventDTO.setCalendarId(calendar.getId());
            eventDTO.setCreatorId(1L); // 使用默认用户ID
            
            // 创建提醒
            List<EventReminderDTO> reminders = new ArrayList<>();
            
            // 提醒1：30分钟前，发送给特定人员
            EventReminderDTO reminder1 = new EventReminderDTO();
            reminder1.setTime(30); // 30分钟前
            reminder1.setReminderType(EventReminder.ReminderType.NOTIFICATION);
            reminder1.setMessage("30分钟后有会议，请准备");
            reminder1.setRecipients("吴侃,刘超飞,陈平柠"); // 指定接收人
            reminder1.setRequiresCheckIn(true);
            reminder1.setCheckInWindowMinutes(30);
            reminders.add(reminder1);
            
            // 提醒2：15分钟前，发送给不同人员
            EventReminderDTO reminder2 = new EventReminderDTO();
            reminder2.setTime(15); // 15分钟前
            reminder2.setReminderType(EventReminder.ReminderType.EMAIL);
            reminder2.setMessage("15分钟后有会议，请及时参加");
            reminder2.setRecipients("刘红阳,奉双宏"); // 指定不同接收人
            reminder2.setRequiresCheckIn(false);
            reminders.add(reminder2);
            
            eventDTO.setReminders(reminders);
            
            // 创建事件
            CalendarEventDTO createdEvent = eventService.createEvent(eventDTO);
            
            log.info("测试事件创建成功: ID={}, 提醒数量={}", createdEvent.getId(), createdEvent.getReminders().size());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "测试事件创建成功");
            response.put("eventId", createdEvent.getId());
            response.put("eventTitle", createdEvent.getTitle());
            response.put("reminderCount", createdEvent.getReminders().size());
            
            // 详细信息
            List<Map<String, Object>> reminderDetails = new ArrayList<>();
            for (EventReminderDTO reminder : createdEvent.getReminders()) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("id", reminder.getId());
                detail.put("time", reminder.getTime());
                detail.put("type", reminder.getReminderType().toString());
                detail.put("message", reminder.getMessage());
                detail.put("recipients", reminder.getRecipients());
                detail.put("requiresCheckIn", reminder.getRequiresCheckIn());
                reminderDetails.add(detail);
            }
            response.put("reminders", reminderDetails);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("创建测试事件失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "创建测试事件失败: " + e.getMessage()
            ));
        }
    }
}
