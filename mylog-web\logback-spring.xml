<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出器 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <filter class="com.mylog.config.LoggingFilter" />
        
        <!-- 过滤掉所有包含"Hibernate:"的日志信息，不区分大小写 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
                <expression>
                    return message != null &amp;&amp; 
                    (message.toLowerCase().contains("hibernate:") || 
                     message.toLowerCase().contains("select") &amp;&amp; message.toLowerCase().contains("from") || 
                     message.toLowerCase().contains("insert") || 
                     message.toLowerCase().contains("update") || 
                     message.toLowerCase().contains("delete"));
                </expression>
            </evaluator>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出器 - 记录所有日志，包括调试信息 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志文件名称 -->
        <file>logs/mylog-web.log</file>
        
        <!-- 添加过滤器以阻止DEBUG级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        
        <!-- 过滤NoResourceFoundException错误 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
                <expression>
                    return throwable != null &amp;&amp; 
                           throwable instanceof org.springframework.web.servlet.resource.NoResourceFoundException;
                </expression>
            </evaluator>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 滚动后的日志文件名称格式，包含日期和序号 -->
            <fileNamePattern>logs/mylog-web.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 每个文件最大 100MB -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保留 30 天的日志文件 -->
            <maxHistory>30</maxHistory>
            <!-- 所有日志文件总大小不超过 3GB -->
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 日志级别设置 -->
    <logger name="org.springframework" level="WARN" />
    <logger name="org.springframework.beans.factory" level="DEBUG" />
    <logger name="org.springframework.context" level="DEBUG" />
    
    <!-- 禁止所有Hibernate相关的日志输出 - 适用于Hibernate 6.x -->
    <logger name="org.hibernate" level="ERROR" additivity="false" />
    <logger name="org.hibernate.SQL" level="OFF" additivity="false" />
    <logger name="org.hibernate.type" level="OFF" additivity="false" />
    <logger name="org.hibernate.type.descriptor.sql" level="OFF" additivity="false" />
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="OFF" additivity="false" />
    <logger name="org.hibernate.orm.jdbc.bind" level="OFF" additivity="false" />
    <logger name="org.hibernate.orm.jdbc" level="OFF" additivity="false" />
    <logger name="org.hibernate.engine.jdbc" level="OFF" additivity="false" />
    <logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="ERROR" additivity="false" />
    <logger name="org.hibernate.orm.jdbc.extract" level="OFF" additivity="false" />
    <logger name="org.hibernate.stat" level="OFF" additivity="false" />
    <logger name="org.hibernate.SQL_SLOW" level="OFF" additivity="false" />
    <logger name="org.hibernate.cache" level="OFF" additivity="false" />
    
    <!-- JPA相关日志 -->
    <logger name="org.springframework.orm.jpa" level="OFF" additivity="false" />
    <logger name="org.springframework.orm.jpa.JpaTransactionManager" level="OFF" additivity="false" />
    <logger name="org.springframework.transaction" level="WARN" additivity="false" />
    
    <logger name="com.zaxxer.hikari" level="WARN" />
    <logger name="org.apache" level="WARN" />

    <!-- Spring 调度器日志 -->
    <logger name="org.springframework.scheduling" level="DEBUG" />

    <!-- 应用程序特定日志级别设置 -->
    <logger name="com.mylog" level="DEBUG" />
    <logger name="com.mylog.service.impl" level="DEBUG" />
    <logger name="com.mylog.task" level="DEBUG" />
    <logger name="com.mylog.controller" level="INFO" />
    <logger name="com.mylog.service" level="INFO" />

    <!-- 禁用消息相关的日志 -->
    <logger name="com.mylog.controller.MessageController" level="WARN" />
    <logger name="com.mylog.service.impl.MessageServiceImpl" level="WARN" />

    <!-- 过滤不需要的调试信息 -->
    <logger name="org.springframework.web" level="WARN" />
    <logger name="org.springframework.security" level="WARN" />
    <logger name="org.thymeleaf" level="WARN" />

    <!-- 根日志级别 -->
    <root level="WARN">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>