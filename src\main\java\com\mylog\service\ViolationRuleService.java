package com.mylog.service;

import com.mylog.model.ViolationRule;

import java.util.List;
import java.util.Optional;

/**
 * 违规规则服务接口
 */
public interface ViolationRuleService {
    
    /**
     * 获取所有规则
     * @return 规则列表
     */
    List<ViolationRule> getAllRules();
    
    /**
     * 获取所有启用的规则
     * @return 规则列表
     */
    List<ViolationRule> getAllEnabledRules();
    
    /**
     * 根据ID获取规则
     * @param ruleId 规则ID
     * @return 规则对象
     */
    Optional<ViolationRule> getRuleById(Long ruleId);
    
    /**
     * 保存规则
     * @param rule 规则对象
     * @return 保存后的规则对象
     */
    ViolationRule saveRule(ViolationRule rule);
    
    /**
     * 删除规则
     * @param ruleId 规则ID
     */
    void deleteRule(Long ruleId);
    
    /**
     * 启用规则
     * @param ruleId 规则ID
     * @return 更新后的规则对象
     */
    ViolationRule enableRule(Long ruleId);
    
    /**
     * 禁用规则
     * @param ruleId 规则ID
     * @return 更新后的规则对象
     */
    ViolationRule disableRule(Long ruleId);
    
    /**
     * 初始化默认规则（如果不存在）
     */
    void initDefaultRules();
}
