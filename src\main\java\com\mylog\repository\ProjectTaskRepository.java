package com.mylog.repository;

import com.mylog.model.ProjectTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ProjectTaskRepository extends JpaRepository<ProjectTask, Long> {

    List<ProjectTask> findByProjectId(Long projectId);

    @Query("SELECT t FROM ProjectTask t WHERE t.projectId = :projectId ORDER BY t.createdDate DESC")
    List<ProjectTask> findByProjectIdOrderByCreatedDateDesc(@Param("projectId") Long projectId);

    @Query("SELECT t FROM ProjectTask t WHERE t.projectId = :projectId ORDER BY t.createdDate DESC")
    Page<ProjectTask> findByProjectIdOrderByCreatedDateDescPageable(@Param("projectId") Long projectId, Pageable pageable);

    /**
     * 查找所有非归档项目的任务
     * @param archiveStatus 归档状态值（例如1表示已归档）
     * @return 非归档项目任务列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId WHERE p.archive IS NULL OR p.archive != :archiveStatus ORDER BY CASE WHEN t.status = '进行中' THEN 0 ELSE 1 END, t.createdDate DESC")
    List<ProjectTask> findTasksFromNonArchivedProjects(@Param("archiveStatus") Integer archiveStatus);

    /**
     * 查找所有非归档项目的任务（分页版本）
     * @param archiveStatus 归档状态值（例如1表示已归档）
     * @param pageable 分页参数
     * @return 分页非归档项目任务列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId WHERE p.archive IS NULL OR p.archive != :archiveStatus ORDER BY CASE WHEN t.status = '进行中' THEN 0 ELSE 1 END, t.createdDate DESC")
    Page<ProjectTask> findTasksFromNonArchivedProjects(@Param("archiveStatus") Integer archiveStatus, Pageable pageable);

    /**
     * 统计特定负责人和状态的任务数量
     * @param responsible 负责人
     * @param status 状态
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t WHERE t.responsible = :responsible AND t.status = :status")
    Long countByResponsibleAndStatus(@Param("responsible") String responsible, @Param("status") String status);

    /**
     * 统计特定状态的任务数量
     * @param status 状态
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t WHERE t.status = :status")
    Long countByStatus(@Param("status") String status);

    List<ProjectTask> findByStatus(String status);

    Page<ProjectTask> findByStatus(String status, Pageable pageable);

    List<ProjectTask> findByRisk(String risk);

    Page<ProjectTask> findByRisk(String risk, Pageable pageable);

    @Query("SELECT t FROM ProjectTask t WHERE t.createdDate BETWEEN :startDate AND :endDate")
    List<ProjectTask> findByCreatedDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    @Query("SELECT t FROM ProjectTask t WHERE t.createdDate BETWEEN :startDate AND :endDate")
    Page<ProjectTask> findByCreatedDateBetweenPageable(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    @Query("SELECT t FROM ProjectTask t WHERE t.taskName LIKE %:keyword% OR t.remarks LIKE %:keyword%")
    List<ProjectTask> findByKeyword(@Param("keyword") String keyword);

    @Query("SELECT t FROM ProjectTask t WHERE t.taskName LIKE %:keyword% OR t.remarks LIKE %:keyword%")
    Page<ProjectTask> findByKeywordPageable(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计指定负责人的任务数量
     * @param responsible 负责人
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t WHERE t.responsible = :responsible")
    Long countByResponsible(@Param("responsible") String responsible);

    List<ProjectTask> findByResponsible(String responsible);

    Page<ProjectTask> findByResponsible(String responsible, Pageable pageable);

    /**
     * 查找特定用户负责的专项任务（非归档项目）
     * @param responsible 负责人
     * @param archiveStatus 归档状态值
     * @param pageable 分页参数
     * @return 特殊任务分页列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND t.type = '专项' AND (p.archive IS NULL OR p.archive != :archiveStatus) " +
           "ORDER BY CASE WHEN t.status = '进行中' THEN 0 ELSE 1 END, t.createdDate DESC")
    Page<ProjectTask> findSpecialTasksByResponsible(
            @Param("responsible") String responsible,
            @Param("archiveStatus") Integer archiveStatus,
            Pageable pageable);

    /**
     * 查找特定用户负责的困难任务（非归档项目）
     * @param responsible 负责人
     * @param archiveStatus 归档状态值
     * @param pageable 分页参数
     * @return 困难任务分页列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND t.type = '难点' AND (p.archive IS NULL OR p.archive != :archiveStatus) " +
           "ORDER BY CASE WHEN t.status = '进行中' THEN 0 ELSE 1 END, t.createdDate DESC")
    Page<ProjectTask> findDifficultTasksByResponsible(
            @Param("responsible") String responsible,
            @Param("archiveStatus") Integer archiveStatus,
            Pageable pageable);

    /**
     * 查找特定用户负责的任务（非归档项目）
     * @param responsible 负责人
     * @param archiveStatus 归档状态值
     * @param pageable 分页参数
     * @return 任务分页列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND (p.archive IS NULL OR p.archive != :archiveStatus) " +
           "ORDER BY t.createdDate DESC")
    Page<ProjectTask> findByResponsibleFromNonArchivedProjects(
            @Param("responsible") String responsible,
            @Param("archiveStatus") Integer archiveStatus,
            Pageable pageable);

    /**
     * 查找所有任务（非归档项目）
     * @param archiveStatus 归档状态值
     * @param pageable 分页参数
     * @return 任务分页列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE p.archive IS NULL OR p.archive != :archiveStatus " +
           "ORDER BY t.createdDate DESC")
    Page<ProjectTask> findAllFromNonArchivedProjects(
            @Param("archiveStatus") Integer archiveStatus,
            Pageable pageable);

    /**
     * 查找所有非归档项目中特定状态的任务
     * @param archiveStatus 归档状态值
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 分页任务列表
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE (p.archive IS NULL OR p.archive != :archiveStatus) AND t.status = :status " +
           "ORDER BY t.createdDate DESC")
    Page<ProjectTask> findTasksFromNonArchivedProjectsByStatus(
            @Param("archiveStatus") Integer archiveStatus,
            @Param("status") String status,
            Pageable pageable);

    /**
     * 统计特定用户负责的困难任务中状态为"进行中"的数量（非归档项目）
     * @param responsible 负责人
     * @param status 任务状态
     * @param archiveStatus 归档状态值
     * @return 进行中的困难任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND t.type = '难点' AND t.status = :status AND (p.archive IS NULL OR p.archive != :archiveStatus)")
    Long countDifficultTasksByResponsibleAndStatus(
            @Param("responsible") String responsible,
            @Param("status") String status,
            @Param("archiveStatus") Integer archiveStatus);

    /**
     * 统计特定用户负责的专项任务中状态为"进行中"的数量（非归档项目）
     * @param responsible 负责人
     * @param status 任务状态
     * @param archiveStatus 归档状态值
     * @return 进行中的专项任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND t.type = '专项' AND t.status = :status AND (p.archive IS NULL OR p.archive != :archiveStatus)")
    Long countSpecialTasksByResponsibleAndStatus(
            @Param("responsible") String responsible,
            @Param("status") String status,
            @Param("archiveStatus") Integer archiveStatus);

    /**
     * 统计项目中非指定状态的任务数量
     * @param projectId 项目ID
     * @param status 排除的状态
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t WHERE t.projectId = :projectId AND t.status != :status")
    Long countByProjectIdAndStatusNot(@Param("projectId") Long projectId, @Param("status") String status);

    @Query(value = "DELETE FROM SQLITE_SEQUENCE WHERE name='Tasks'", nativeQuery = true)
    @Modifying
    void resetAutoIncrement();

    @Query(value = "UPDATE SQLITE_SEQUENCE SET seq = 0 WHERE name='Tasks'", nativeQuery = true)
    @Modifying
    void resetSequenceToZero();

    /**
     * 查找用户负责的订单任务（非归档项目）
     */
    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND t.type = :type " +
           "AND (p.archive IS NULL OR p.archive != :archiveStatus) " +
           "ORDER BY CASE WHEN t.status = '进行中' THEN 0 ELSE 1 END, t.createdDate DESC")
    Page<ProjectTask> findByResponsibleAndTypeAndProjectArchiveIsNullOrProjectArchiveNot(
        @Param("responsible") String responsible,
        @Param("type") String type,
        @Param("archiveStatus") Integer archiveStatus,
        Pageable pageable);

    /**
     * 统计用户负责的订单任务数量（按状态，非归档项目）
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND t.type = :type AND t.status = :status " +
           "AND (p.archive IS NULL OR p.archive != :archiveStatus)")
    Long countByResponsibleAndTypeAndStatusAndProjectArchiveIsNullOrProjectArchiveNot(
        @Param("responsible") String responsible,
        @Param("type") String type,
        @Param("status") String status,
        @Param("archiveStatus") Integer archiveStatus);

    @Query("SELECT t FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE t.responsible = :responsible AND (p.archive IS NULL OR p.archive != :archiveStatus) " +
           "ORDER BY CASE WHEN t.status = '进行中' THEN 0 ELSE 1 END, t.createdDate DESC")
    Page<ProjectTask> findByResponsibleFromNonArchivedProjectsOrderByStatusAndDate(
            @Param("responsible") String responsible,
            @Param("archiveStatus") Integer archiveStatus,
            Pageable pageable);

    /**
     * 查找特定项目中特定状态的任务
     * @param projectId 项目ID
     * @param status 任务状态
     * @return 任务列表
     */
    @Query("SELECT t FROM ProjectTask t WHERE t.projectId = :projectId AND t.status = :status ORDER BY t.createdDate DESC")
    List<ProjectTask> findByProjectIdAndStatus(
            @Param("projectId") Long projectId,
            @Param("status") String status);

    /**
     * 获取最大任务ID
     * @return 最大任务ID
     */
    @Query("SELECT MAX(t.taskId) FROM ProjectTask t")
    Long findMaxTaskId();

    /**
     * 根据项目客户名称统计任务数量
     * @param customerName 客户名称
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM ProjectTask t JOIN Project p ON t.projectId = p.projectId " +
           "WHERE p.customerName = :customerName")
    Long countTasksByProjectCustomerName(@Param("customerName") String customerName);
}