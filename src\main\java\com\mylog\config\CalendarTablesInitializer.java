package com.mylog.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 初始化日历表和示例数据
 */
@Component
public class CalendarTablesInitializer implements ApplicationListener<ContextRefreshedEvent> {
    
    private static final Logger log = LoggerFactory.getLogger(CalendarTablesInitializer.class);
    private boolean initialized = false;
    
    @Autowired
    private DataSource logDataSource;    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {
        if (initialized) {
            return;
        }
        
        log.info("检查日历表是否需要初始化...");
        try {
            // 检查是否已经有日历数据，如果有则跳过初始化
            String checkQuery = "SELECT COUNT(*) FROM calendars";
            try (var connection = logDataSource.getConnection();
                 var statement = connection.createStatement();
                 var resultSet = statement.executeQuery(checkQuery)) {
                
                if (resultSet.next() && resultSet.getInt(1) > 0) {
                    log.info("日历表已有数据，跳过初始化");
                    initialized = true;
                    return;
                }
            } catch (Exception e) {
                log.info("日历表不存在或为空，开始初始化...");
            }
            
            // 首先尝试从类路径加载
            Resource resource = new ClassPathResource("init_calendar_tables.sql");
            if (!resource.exists()) {
                // 如果类路径中不存在，尝试从文件系统加载
                String filePath = "c:/mylog-web/init_calendar_tables.sql";
                log.info("尝试从文件系统加载日历表初始化脚本: {}", filePath);
                if (Files.exists(Paths.get(filePath))) {
                    File sqlFile = new File(filePath);
                    resource = new org.springframework.core.io.FileSystemResource(sqlFile);
                    log.info("找到日历表初始化脚本: {}", sqlFile.getAbsolutePath());
                } else {
                    log.warn("日历表初始化脚本不存在: {}", filePath);
                    return;
                }
            }
            
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(resource);
            populator.setContinueOnError(true);
            populator.setIgnoreFailedDrops(true);
            
            log.info("执行日历表初始化脚本...");
            populator.execute(logDataSource);
            log.info("日历表初始化完成");
            
            initialized = true;
        } catch (Exception e) {
            log.error("初始化日历表结构和示例数据时发生错误", e);
        }
    }
}
