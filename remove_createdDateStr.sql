-- 删除Users表中的createdDateStr字段，只保留CreatedDate字段

-- SQLite不支持直接删除列，需要重建表的方式
-- 1. 备份数据到临时表
CREATE TABLE Users_backup AS 
SELECT UserId, Username, weixinID, Password, Role, CreatedDate, 
       dateFormat, department, displayName, email, language, 
       notificationMethod, phone, receiveDeadlineReminders, 
       receiveProjectUpdates, receiveSystemNotifications, 
       receiveTaskAssignments, theme, timezone, theme_style, 
       last_modified_time, webhook_url
FROM Users;

-- 2. 删除原表
DROP TABLE Users;

-- 3. 重新创建表（不包含createdDateStr列）
CREATE TABLE Users (
    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL UNIQUE,
    weixinID TEXT,
    Password TEXT NOT NULL,
    Role INTEGER NOT NULL,
    CreatedDate TEXT NOT NULL,
    dateFormat VARCHAR(255),
    department VARCHAR(255),
    displayName VARCHAR(255),
    email VARCHAR(255),
    language VARCHAR(255),
    notificationMethod VARCHAR(255),
    phone VARCHAR(255),
    receiveDeadlineReminders BOOLEAN DEFAULT 0,
    receiveProjectUpdates BOOLEAN DEFAULT 0,
    receiveSystemNotifications BOOLEAN DEFAULT 0,
    receiveTaskAssignments BOOLEAN DEFAULT 0,
    theme VARCHAR(255),
    timezone VARCHAR(255),
    theme_style VARCHAR(255),
    last_modified_time TEXT,
    webhook_url VARCHAR(255) DEFAULT 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=afd06d9c-9713-47f2-972d-b1b1eb473744'
);

-- 4. 恢复数据
INSERT INTO Users SELECT * FROM Users_backup;

-- 5. 删除备份表
DROP TABLE Users_backup;

-- 6. 验证结果
.schema Users
