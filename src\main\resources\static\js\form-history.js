// 处理表单页面的历史记录
function handleFormHistory() {
    // 标记当前页面为表单页面
    sessionStorage.setItem('isFormPage', 'true');
    
    // 获取来源页面
    const referer = document.querySelector('input[name="referer"]')?.value;
    
    if (referer) {
        // 替换当前历史记录，而不是添加新记录
        history.replaceState({ isForm: true, referer: referer }, document.title, window.location.href);
        
        // 监听 popstate 事件（当用户点击浏览器的后退按钮时触发）
        window.addEventListener('popstate', function(event) {
            // 如果用户点击后退按钮，直接跳转到来源页面
            window.location.replace(referer);
        });
    }

    // 在页面卸载前清除表单页面的历史记录
    window.addEventListener('beforeunload', function() {
        if (referer) {
            // 将当前表单页面从历史记录中移除
            history.replaceState(null, '', referer);
        }
    });
}

// 判断是否是表单/编辑页面
function isFormPage() {
    const path = window.location.pathname;
    return path.includes('/form') || 
           path.includes('/edit') || 
           path.includes('/new') ||
           path.includes('/create') ||
           document.querySelector('form.needs-validation') !== null;
}

// 获取历史记录
function getHistoryStack() {
    try {
        const history = sessionStorage.getItem('pageHistory');
        return history ? JSON.parse(history) : [];
    } catch (e) {
        console.error('Error parsing history:', e);
        return [];
    }
}

// 保存历史记录
function saveHistoryStack(stack) {
    try {
        sessionStorage.setItem('pageHistory', JSON.stringify(stack));
    } catch (e) {
        console.error('Error saving history:', e);
    }
}

// 添加页面到历史记录
function addToHistory(url) {
    if (!url) return;
    
    const stack = getHistoryStack();
    
    // 如果当前URL已经在栈顶，不重复添加
    if (stack[stack.length - 1] === url) {
        return;
    }
    
    // 如果URL已存在于历史记录中，先移除旧的
    const index = stack.indexOf(url);
    if (index !== -1) {
        stack.splice(index, 1);
    }
    
    // 添加新的URL到栈顶
    stack.push(url);
    
    // 限制历史记录长度，保留最近的50条记录
    if (stack.length > 50) {
        stack.shift();
    }
    
    saveHistoryStack(stack);
}

// 处理返回按钮点击
function handleBack(event) {
    event.preventDefault();
    const referer = document.querySelector('input[name="referer"]')?.value;
    const fromPage = new URLSearchParams(window.location.search).get('from');
    const stack = getHistoryStack();
    
    if (fromPage === 'dashboard') {
        window.location.replace('/dashboard');
    } else if (referer) {
        window.location.replace(referer);
    } else if (stack.length > 1) {
        // 获取上一个非当前页面的URL
        let previousUrl = stack[stack.length - 2];
        // 移除当前页面
        stack.pop();
        saveHistoryStack(stack);
        window.location.replace(previousUrl);
    } else {
        history.back();
    }
    return false;
}

// 处理取消按钮点击（用于表单页面）
function handleCancel(event) {
    event.preventDefault();
    const referer = document.querySelector('input[name="referer"]')?.value;
    if (referer) {
        // 使用 replace 而不是 href，这样不会在历史记录中留下表单页面
        window.location.replace(referer);
    } else {
        const stack = getHistoryStack();
        if (stack.length > 1) {
            // 获取上一个非当前页面的URL
            let previousUrl = stack[stack.length - 2];
            // 移除当前页面
            stack.pop();
            saveHistoryStack(stack);
            window.location.replace(previousUrl);
        } else {
            history.back();
        }
    }
    return false;
}

// 在页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const isForm = isFormPage();
    const currentUrl = window.location.href;
    
    // 如果不是表单页面，记录当前页面 URL
    if (!isForm) {
        sessionStorage.setItem('lastNonFormPage', currentUrl);
        // 添加到历史记录栈中
        addToHistory(currentUrl);
    }
    
    // 如果是表单页面，初始化表单历史处理
    if (isForm) {
        handleFormHistory();
    }
    
    // 为所有返回按钮添加点击事件处理
    document.querySelectorAll('button[onclick*="handleBack"], a[onclick*="handleBack"]').forEach(button => {
        button.onclick = handleBack;
    });
}); 