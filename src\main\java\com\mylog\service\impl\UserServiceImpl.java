package com.mylog.service.impl;

import com.mylog.model.user.User;
import com.mylog.repository.user.UserRepository;
import com.mylog.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    
    @Autowired
    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Override
    public List<User> findAllUsers() {
        return userRepository.findAll();
    }
    
    @Override
    public Optional<User> findUserById(Long id) {
        return userRepository.findById(id);
    }
    
    @Override
    public Optional<User> findUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    @Override
    public User saveUser(User user) {
        // 如果是新用户，设置创建时间
        if (user.getUserId() == null) {
            user.setCreatedDate(java.time.LocalDateTime.now());
        }
        
        // 更新最后修改时间
        user.setLastModifiedTime(java.time.LocalDateTime.now());
        
        return userRepository.save(user);
    }
    
    @Override
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
    
    @Override
    public boolean changePassword(Long userId, String currentPassword, String newPassword) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            return false;
        }
        
        User user = userOpt.get();
        
        // 直接比较原始密码
        if (!currentPassword.equals(user.getPassword())) {
            return false;
        }
        
        // 更新密码（不加密）
        user.setPassword(newPassword);
        // 使用saveUser方法来保存，这样会自动更新最后修改时间
        saveUser(user);
        
        return true;
    }
} 