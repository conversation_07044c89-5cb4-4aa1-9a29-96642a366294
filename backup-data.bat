@echo off
echo Starting backup...

REM Get current date and time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set datetime=%%a
set year=%datetime:~0,4%
set month=%datetime:~4,2%
set day=%datetime:~6,2%
set hour=%datetime:~8,2%
set minute=%datetime:~10,2%
set second=%datetime:~12,2%
set timestamp=%year%%month%%day%_%hour%%minute%%second%

REM Set backup directory
set BACKUP_DIR=..\dataBK
set BACKUP_DIR_WITH_DATE=%BACKUP_DIR%\%timestamp%

REM Check if backup directory exists
if not exist "%BACKUP_DIR%" (
    echo Creating backup directory %BACKUP_DIR%
    mkdir "%BACKUP_DIR%"
)

REM Create dated backup subdirectory
echo Creating backup subdirectory %BACKUP_DIR_WITH_DATE%
mkdir "%BACKUP_DIR_WITH_DATE%"

REM Perform backup
echo Backing up data directory to %BACKUP_DIR_WITH_DATE%...
xcopy "data" "%BACKUP_DIR_WITH_DATE%" /E /I /H /Y

REM Check if backup was successful
if %ERRORLEVEL% EQU 0 (
    echo Backup completed successfully!
    echo Backup files are located at: %BACKUP_DIR_WITH_DATE%
) else (
    echo Error occurred during backup, error code: %ERRORLEVEL%
)

echo Backup process finished.
pause
