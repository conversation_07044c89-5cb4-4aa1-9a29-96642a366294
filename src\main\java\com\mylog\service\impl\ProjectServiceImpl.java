package com.mylog.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Arrays;
import java.math.BigDecimal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.Project;
import com.mylog.repository.ProjectRepository;
import com.mylog.service.ProjectService;
import com.mylog.util.ProjectCodeGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mylog.model.ProjectTask;
import com.mylog.service.TaskService;

@Service
public class ProjectServiceImpl implements ProjectService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectServiceImpl.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private ProjectCodeGenerator projectCodeGenerator;
    
    @Autowired
    private TaskService taskService;
    
    @Override
    public List<Project> findAllProjects() {
        return projectRepository.findAllOrderByCreatedDateDesc();
    }
    
    @Override
    public Page<Project> findAllProjects(Pageable pageable) {
        return projectRepository.findAllOrderByCreatedDateDescPageable(pageable);
    }

    @Override
    public Optional<Project> findProjectById(Long id) {
        return projectRepository.findById(id);
    }

    @Override
    @Transactional
    public Project saveProject(Project project) {
        // 检查项目编码
        if (project.getProjectCode() == null || project.getProjectCode().isEmpty()) {
            if (project.getProjectId() != null) {
                // 如果是更新现有项目，但项目编码为空，则从数据库获取原有的项目编码
                Optional<Project> existingProject = projectRepository.findById(project.getProjectId());
                if (existingProject.isPresent()) {
                    project.setProjectCode(existingProject.get().getProjectCode());
                } else {
                    // 如果找不到现有项目，则生成新的项目编码
                    String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                    Integer maxSequence = projectRepository.findMaxSequenceByDate(dateStr);
                    int nextSequence = (maxSequence == null) ? 0 : maxSequence + 1;
                    project.setProjectCode(projectCodeGenerator.generateProjectCode(nextSequence));
                }
            } else {
                // 新项目，生成新的项目编码
                String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                Integer maxSequence = projectRepository.findMaxSequenceByDate(dateStr);
                int nextSequence = (maxSequence == null) ? 0 : maxSequence + 1;
                project.setProjectCode(projectCodeGenerator.generateProjectCode(nextSequence));
            }
        }
        
        try {
            // 处理状态变更和时间记录
            if (project.getProjectId() != null) {
                // 更新现有项目
                Optional<Project> existingProjectOpt = projectRepository.findById(project.getProjectId());
                if (existingProjectOpt.isPresent()) {
                    Project existingProject = existingProjectOpt.get();
                    String oldStatus = existingProject.getStatus();
                    
                    // 处理实际开始时间记录
                    // 1. 检查状态是否从其他状态变为"进行中"
                    if (!"进行中".equals(oldStatus) && "进行中".equals(project.getStatus())) {
                        // 直接设置实际开始日期为当前时间，无需判断是否为空
                        LocalDateTime now = LocalDateTime.now();
                        project.setActualStartDateTime(now);
                        project.setActualStartDate(now.format(DATE_FORMATTER));
                    } else if ("进行中".equals(project.getStatus())) {
                        // 保留原来的实际开始日期，如果有的话
                        if (existingProject.getActualStartDateTime() != null) {
                            project.setActualStartDateTime(existingProject.getActualStartDateTime());
                            project.setActualStartDate(existingProject.getActualStartDate());
                        } else {
                            // 如果之前没有记录，设置为当前时间
                            LocalDateTime now = LocalDateTime.now();
                            project.setActualStartDateTime(now);
                            project.setActualStartDate(now.format(DATE_FORMATTER));
                        }
                    }
                    
                    // 2. 检查状态是否从"未开始"或"进行中"切换到终止状态（已取消、已暂停、已完成）
                    // 这些状态切换时需要记录实际结束时间
                    boolean isFromActiveStatus = "未开始".equals(oldStatus) || "进行中".equals(oldStatus);
                    boolean isToTerminalStatus = "已取消".equals(project.getStatus()) || 
                                               "已暂停".equals(project.getStatus()) || 
                                               "已完成".equals(project.getStatus());
                    
                    if (isFromActiveStatus && isToTerminalStatus) {
                        // 如果从活跃状态切换到终止状态，记录当前时间为实际结束时间
                        LocalDateTime now = LocalDateTime.now();
                        project.setActualEndDateTime(now);
                        project.setActualEndDate(now.format(DATE_FORMATTER));
                        
                        // 同时确保有实际开始时间记录
                        if (existingProject.getActualStartDateTime() == null) {
                            // 如果之前没有记录实际开始时间，则也记录当前时间为实际开始时间
                            project.setActualStartDateTime(now);
                            project.setActualStartDate(now.format(DATE_FORMATTER));
                      
                        }
                    } 
                    
                    // 直接从现有项目获取CreatedBy和CreatedDate，无需判断是否为空
                    project.setCreatedBy(existingProject.getCreatedBy());
                    project.setCreatedDate(existingProject.getCreatedDate());
                    if (existingProject.getCreatedDateTime() != null) {
                        project.setCreatedDateTime(existingProject.getCreatedDateTime());
                    }
                }
            } else {
                // 新项目
                // 如果状态为"进行中"，直接设置实际开始日期为当前时间，无需判断是否为空
                if ("进行中".equals(project.getStatus())) {
                    LocalDateTime now = LocalDateTime.now();
                    project.setActualStartDateTime(now);
                    project.setActualStartDate(now.format(DATE_FORMATTER));
                }
                
                // 如果状态为"已完成"，直接设置实际结束日期为当前时间，无需判断是否为空
                if ("已完成".equals(project.getStatus())) {
                    LocalDateTime now = LocalDateTime.now();
                    project.setActualEndDateTime(now);
                    project.setActualEndDate(now.format(DATE_FORMATTER));
                }
                
                // 直接设置CreatedDate为当前时间，无需判断是否为空
                LocalDateTime now = LocalDateTime.now();
                project.setCreatedDateTime(now);
                project.setCreatedDate(now.format(DATE_FORMATTER));
            }
            
            return projectRepository.save(project);
        } catch (Exception e) {
            logger.error("保存项目时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void deleteProject(Long id) {
        logger.info("开始级联删除项目ID: {}", id);
        try {
            // 获取项目的所有任务
            List<ProjectTask> tasks = taskService.findTasksByProjectId(id);
            logger.info("项目ID {} 关联的任务数量: {}", id, tasks.size());
            
            // 对每个任务，先删除其子任务，再删除任务本身
            for (ProjectTask task : tasks) {
                try {
                    // 使用已经实现级联删除的方法删除任务
                    taskService.deleteTask(task.getTaskId());
                    logger.info("成功删除项目ID {} 的任务ID: {}", id, task.getTaskId());
                } catch (Exception e) {
                    logger.error("删除项目 {} 的任务 {} 时出错: {}", id, task.getTaskId(), e.getMessage(), e);
                    // 继续删除其他任务，不中断整个过程
                }
            }
            
            // 最后删除项目
            projectRepository.deleteById(id);
            logger.info("成功删除项目ID: {}", id);
        } catch (Exception e) {
            logger.error("删除项目ID {} 时出错: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Project> findProjectsByResponsible(String responsible) {
        return projectRepository.findByResponsible(responsible);
    }
    
    @Override
    public Page<Project> findProjectsByResponsible(String responsible, Pageable pageable) {
        // 使用自定义查询方法
        List<Project> projects = projectRepository.findByResponsible(responsible);
        
        // 按创建日期降序排序
        projects.sort((p1, p2) -> {
            if (p1.getCreatedDateTime() == null || p2.getCreatedDateTime() == null) {
                return 0;
            }
            // 首先按状态排序（进行中的排在前面）
            if ("进行中".equals(p1.getStatus()) && !"进行中".equals(p2.getStatus())) {
                return -1;
            }
            if (!"进行中".equals(p1.getStatus()) && "进行中".equals(p2.getStatus())) {
                return 1;
            }
            // 然后按创建日期降序排序
            return p2.getCreatedDateTime().compareTo(p1.getCreatedDateTime());
        });
        
        // 手动分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), projects.size());
        
        return new PageImpl<>(projects.subList(start, end), pageable, projects.size());
    }

    @Override
    public Long countProjectsByResponsibleAndStatus(String responsible, String status) {
        return projectRepository.countByResponsibleAndStatus(responsible, status);
    }

    @Override
    public Long countProjectsByResponsibleAndCreatedDateAfter(String responsible, String createdAfter) {
        try {
            // 解析日期字符串
            LocalDateTime createdAfterDateTime = LocalDateTime.parse(createdAfter, DATE_FORMATTER);
            
            // 获取该负责人的所有项目
            List<Project> projects = projectRepository.findByResponsible(responsible);
            
            // 筛选创建日期在指定时间之后的项目
            long count = projects.stream()
                    .filter(project -> {
                        LocalDateTime projectCreatedDateTime = project.getCreatedDateTime();
                        return projectCreatedDateTime != null && 
                               projectCreatedDateTime.isAfter(createdAfterDateTime);
                    })
                    .count();
            
            logger.debug("统计负责人 {} 在 {} 之后创建的项目数: {}", responsible, createdAfter, count);
            return count;
        } catch (Exception e) {
            logger.error("统计负责人 {} 半年内新建项目数时出错: {}", responsible, e.getMessage(), e);
            return 0L;
        }
    }
    
    @Override
    public Long countCompletedProjectsByResponsibleAndEndDateAfter(String responsible, String completedAfter) {
        try {
            // 解析日期字符串
            LocalDateTime completedAfterDateTime = LocalDateTime.parse(completedAfter, DATE_FORMATTER);
            
            // 获取该负责人的所有项目
            List<Project> projects = projectRepository.findByResponsible(responsible);
            
            // 筛选已完成且完成日期在指定时间之后的项目
            long count = projects.stream()
                    .filter(project -> {
                        // 项目必须是已完成状态
                        if (!"已完成".equals(project.getStatus())) {
                            return false;
                        }
                        
                        // 获取项目的实际结束时间
                        LocalDateTime projectEndDateTime = project.getActualEndDateTime();
                        
                        // 结束时间必须存在且在指定时间之后
                        return projectEndDateTime != null && 
                               projectEndDateTime.isAfter(completedAfterDateTime);
                    })
                    .count();
            
            logger.debug("统计负责人 {} 在 {} 之后完成的项目数: {}", responsible, completedAfter, count);
            return count;
        } catch (Exception e) {
            logger.error("统计负责人 {} 半年内完成项目数时出错: {}", responsible, e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Long countProjectsByStatus(String status) {
        List<Project> projects = projectRepository.findByStatus(status);
        return (long) projects.size();
    }

    @Override
    public List<Project> findProjectsByStatus(String status) {
        return projectRepository.findByStatus(status);
    }
    
    @Override
    public Page<Project> findProjectsByStatus(String status, Pageable pageable) {
        List<Project> projects = projectRepository.findByStatus(status);
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), projects.size());
        
        return new PageImpl<>(
            projects.subList(start, end),
            pageable,
            projects.size()
        );
    }

    @Override
    public List<Project> searchProjects(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAllProjects();
        }
        return projectRepository.searchProjects(keyword);
    }
    
    @Override
    public Page<Project> searchProjects(String keyword, Pageable pageable) {
        if (keyword == null || keyword.isEmpty()) {
            return projectRepository.findAllOrderByCreatedDateDescPageable(pageable);
        }
        return projectRepository.searchProjectsPageable(keyword, pageable);
    }

    @Override
    public List<Project> findProjectsByDateRange(String dateField, LocalDateTime startDate, LocalDateTime endDate) {
        if (dateField == null || startDate == null || endDate == null) {
            return findAllProjects();
        }
        
        // 由于没有现成的日期范围查询方法，我们使用JPQL查询
        List<Project> projects = findAllProjects();
        
        switch (dateField.toLowerCase()) {
            case "plannedstartdate":
                return projects.stream()
                    .filter(p -> p.getPlannedStartDateTime() != null && 
                           !p.getPlannedStartDateTime().isBefore(startDate) && 
                           !p.getPlannedStartDateTime().isAfter(endDate))
                    .toList();
            case "plannedenddate":
                return projects.stream()
                    .filter(p -> p.getPlannedEndDateTime() != null && 
                           !p.getPlannedEndDateTime().isBefore(startDate) && 
                           !p.getPlannedEndDateTime().isAfter(endDate))
                    .toList();
            case "createddate":
                return projects.stream()
                    .filter(p -> p.getCreatedDateTime() != null && 
                           !p.getCreatedDateTime().isBefore(startDate) && 
                           !p.getCreatedDateTime().isAfter(endDate))
                    .toList();
            default:
                return findAllProjects();
        }
    }
    
    @Override
    public Page<Project> findProjectsByDateRange(String dateField, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        if (dateField == null || startDate == null || endDate == null) {
            return findAllProjects(pageable);
        }
        
        // 获取所有项目
        List<Project> allProjects = findAllProjects();
        List<Project> filteredProjects;
        
        // 根据日期字段过滤
        switch (dateField.toLowerCase()) {
            case "plannedstartdate":
                filteredProjects = allProjects.stream()
                    .filter(p -> p.getPlannedStartDateTime() != null && 
                           !p.getPlannedStartDateTime().isBefore(startDate) && 
                           !p.getPlannedStartDateTime().isAfter(endDate))
                    .collect(Collectors.toList());
                break;
            case "plannedenddate":
                filteredProjects = allProjects.stream()
                    .filter(p -> p.getPlannedEndDateTime() != null && 
                           !p.getPlannedEndDateTime().isBefore(startDate) && 
                           !p.getPlannedEndDateTime().isAfter(endDate))
                    .collect(Collectors.toList());
                break;
            case "createddate":
                filteredProjects = allProjects.stream()
                    .filter(p -> p.getCreatedDateTime() != null && 
                           !p.getCreatedDateTime().isBefore(startDate) && 
                           !p.getCreatedDateTime().isAfter(endDate))
                    .collect(Collectors.toList());
                break;
            default:
                filteredProjects = allProjects;
        }
        
        // 手动分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredProjects.size());
        
        // 创建分页结果
        return new PageImpl<>(
            filteredProjects.subList(start, end),
            pageable,
            filteredProjects.size()
        );
    }

    @Override
    public List<Project> advancedSearchProjects(String projectName, String customerName, String status) {
        List<Project> projects = projectRepository.advancedSearchProjects(projectName, customerName, status);
        // 初始化所有项目的日期字段
        projects.forEach(project -> initializeProjectDates(project));
        return projects;
    }
    
    @Override
    public Page<Project> advancedSearchProjects(String projectName, String customerName, String status, Pageable pageable) {
        Page<Project> projectPage = projectRepository.advancedSearchProjectsPageable(projectName, customerName, status, pageable);
        // 初始化所有项目的日期字段
        projectPage.getContent().forEach(project -> initializeProjectDates(project));
        return projectPage;
    }
    
    @Override
    public List<Project> dynamicSearchProjects(Map<String, String> searchCriteria) {
        // 获取所有项目
        List<Project> allProjects = findAllProjects();
        
        // 如果没有搜索条件，返回所有项目
        if (searchCriteria == null || searchCriteria.isEmpty()) {
            return allProjects;
        }
        
        // 根据搜索条件过滤项目
        List<Project> filteredProjects = allProjects.stream()
            .filter(project -> matchesAllCriteria(project, searchCriteria))
            .collect(Collectors.toList());
        
        // 初始化所有项目的日期字段
        filteredProjects.forEach(project -> initializeProjectDates(project));
        
        return filteredProjects;
    }
    
    @Override
    public Page<Project> dynamicSearchProjects(Map<String, String> searchCriteria, Pageable pageable) {
        // 获取符合条件的项目列表
        List<Project> filteredProjects = dynamicSearchProjects(searchCriteria);
        
        // 手动分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredProjects.size());
        
        // 创建分页结果
        return new PageImpl<>(
            filteredProjects.subList(start, end),
            pageable,
            filteredProjects.size()
        );
    }
    
    /**
     * 检查项目是否匹配所有搜索条件
     * @param project 项目对象
     * @param searchCriteria 搜索条件映射
     * @return 如果项目匹配所有条件则返回true，否则返回false
     */
    private boolean matchesAllCriteria(Project project, Map<String, String> searchCriteria) {
        //logger.info("检查项目ID {} 是否匹配搜索条件", project.getProjectId());
        
        for (Map.Entry<String, String> entry : searchCriteria.entrySet()) {
            String fieldName = entry.getKey();
            String searchValue = entry.getValue();
            
            //logger.info("检查字段: {} = '{}'", fieldName, searchValue);
            
            // 处理创建日期范围条件
            if (fieldName.equals("createdDate_start")) {
                if (searchValue != null && !searchValue.isEmpty()) {
                    logger.info("处理创建日期开始条件: {}", searchValue);
                    try {
                        LocalDateTime startDateTime = parseDateTime(searchValue, true);
                        LocalDateTime projectCreatedDateTime = project.getCreatedDateTime();
                        
                        logger.info("项目创建日期: {}, 搜索开始日期: {}", projectCreatedDateTime, startDateTime);
                        
                        if (projectCreatedDateTime == null || projectCreatedDateTime.isBefore(startDateTime)) {
                            logger.info("项目不符合创建日期开始条件");
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析创建日期开始时间出错: {}", e.getMessage());
                    }
                }
                continue;
            }
            
            if (fieldName.equals("createdDate_end")) {
                if (searchValue != null && !searchValue.isEmpty()) {
                    logger.info("处理创建日期结束条件: {}", searchValue);
                    try {
                        LocalDateTime endDateTime = parseDateTime(searchValue, false);
                        LocalDateTime projectCreatedDateTime = project.getCreatedDateTime();
                        
                        logger.info("项目创建日期: {}, 搜索结束日期: {}", projectCreatedDateTime, endDateTime);
                        
                        if (projectCreatedDateTime == null || projectCreatedDateTime.isAfter(endDateTime)) {
                            logger.info("项目不符合创建日期结束条件");
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析创建日期结束时间出错: {}", e.getMessage());
                    }
                }
                continue;
            }
            
            // 处理实际结束日期范围条件
            if (fieldName.equals("actualEndDate_start")) {
                if (searchValue != null && !searchValue.isEmpty()) {
                    logger.info("处理实际结束日期开始条件: {}", searchValue);
                    try {
                        LocalDateTime startDateTime = parseDateTime(searchValue, true);
                        LocalDateTime projectEndDateTime = project.getActualEndDateTime();
                        
                        logger.info("项目实际结束日期: {}, 搜索开始日期: {}", projectEndDateTime, startDateTime);
                        
                        if (projectEndDateTime == null || projectEndDateTime.isBefore(startDateTime)) {
                            logger.info("项目不符合实际结束日期开始条件");
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析实际结束日期开始时间出错: {}", e.getMessage());
                    }
                }
                continue;
            }
            
            if (fieldName.equals("actualEndDate_end")) {
                if (searchValue != null && !searchValue.isEmpty()) {
                    logger.info("处理实际结束日期结束条件: {}", searchValue);
                    try {
                        LocalDateTime endDateTime = parseDateTime(searchValue, false);
                        LocalDateTime projectEndDateTime = project.getActualEndDateTime();
                        
                        logger.info("项目实际结束日期: {}, 搜索结束日期: {}", projectEndDateTime, endDateTime);
                        
                        if (projectEndDateTime == null || projectEndDateTime.isAfter(endDateTime)) {
                            logger.info("项目不符合实际结束日期结束条件");
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("解析实际结束日期结束时间出错: {}", e.getMessage());
                    }
                }
                continue;
            }
            
            // 处理数值范围条件
            if (fieldName.endsWith("_min") || fieldName.endsWith("_max")) {
                String baseFieldName = fieldName.replace("_min", "").replace("_max", "");
                boolean isMin = fieldName.endsWith("_min");
                
                if (searchValue != null && !searchValue.isEmpty()) {
                    logger.info("处理数值范围条件: {} {} {}", baseFieldName, isMin ? "最小值" : "最大值", searchValue);
                    
                    try {
                        BigDecimal searchNumber = new BigDecimal(searchValue);
                        BigDecimal projectValue = null;
                        
                        switch (baseFieldName) {
                            case "totalCost1":
                                projectValue = project.getTotalCost1() != null ? project.getTotalCost1() : BigDecimal.ZERO;
                                break;
                            case "totalCost2":
                                projectValue = project.getTotalCost2() != null ? project.getTotalCost2() : BigDecimal.ZERO;
                                break;
                            case "visionCost2":
                                projectValue = project.getVisionCost2() != null ? project.getVisionCost2() : BigDecimal.ZERO;
                                break;
                            case "ratedDurationDays":
                                projectValue = project.getRatedDurationDays() != null ? project.getRatedDurationDays() : BigDecimal.ZERO;
                                break;
                            case "difficultyCoefficient":
                                projectValue = project.getDifficultyCoefficient() != null ? project.getDifficultyCoefficient() : BigDecimal.ZERO;
                                break;
                            case "cameraQuantity":
                                projectValue = project.getCameraQuantity() != null ? project.getCameraQuantity() : BigDecimal.ZERO;
                                break;
                            case "quantity":
                                // 设备数量是Double类型，需要特殊处理
                                Double projectQuantity = project.getQuantity() != null ? project.getQuantity() : 0.0;
                                Double searchQuantity = Double.parseDouble(searchValue);
                                
                                logger.info("项目设备数量: {}, 搜索数量: {}", projectQuantity, searchQuantity);
                                
                                if (isMin && projectQuantity < searchQuantity) {
                                    logger.info("项目不符合设备数量最小值条件");
                                    return false;
                                } else if (!isMin && projectQuantity > searchQuantity) {
                                    logger.info("项目不符合设备数量最大值条件");
                                    return false;
                                }
                                continue;
                        }
                        
                        if (projectValue != null) {
                            logger.info("项目{}值: {}, 搜索值: {}", baseFieldName, projectValue, searchNumber);
                            
                            if (isMin && projectValue.compareTo(searchNumber) < 0) {
                                logger.info("项目不符合{}最小值条件", baseFieldName);
                                return false;
                            } else if (!isMin && projectValue.compareTo(searchNumber) > 0) {
                                logger.info("项目不符合{}最大值条件", baseFieldName);
                                return false;
                            }
                        }
                    } catch (NumberFormatException e) {
                        logger.error("解析数值范围条件出错: {}", e.getMessage());
                    }
                }
                continue;
            }
            
            // 处理其他标准条件
            if (!matchesCriterion(project, fieldName, searchValue)) {
                //logger.info("项目不符合条件: {} = '{}'", fieldName, searchValue);
                return false;
            }
        }
        
        //logger.info("项目ID {} 匹配所有搜索条件", project.getProjectId());
        return true;
    }
    
    /**
     * 解析日期时间字符串
     */
    private LocalDateTime parseDateTime(String dateStr, boolean isStartDate) {
        if (dateStr.contains(" ")) {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } else {
            // 只有日期部分，添加时间部分
            String timeStr = isStartDate ? " 00:00:00" : " 23:59:59";
            return LocalDateTime.parse(dateStr + timeStr, DATE_FORMATTER);
        }
    }
    
    /**
     * 判断项目是否匹配单个搜索条件
     * @param project 项目对象
     * @param fieldName 字段名
     * @param searchValue 搜索值
     * @return 是否匹配条件
     */
    private boolean matchesCriterion(Project project, String fieldName, String searchValue) {
        // 对于进行中任务名称搜索，空值有特殊含义：匹配没有进行中任务的项目
        if ("inProgressTaskName".equals(fieldName) && (searchValue == null || searchValue.isEmpty())) {
            try {
                // 获取项目下所有进行中的任务
                List<ProjectTask> inProgressTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(), "进行中");
                // 返回true表示匹配：当项目没有进行中任务时
                return inProgressTasks.isEmpty();
            } catch (Exception e) {
                logger.error("检查项目 {} 是否有进行中任务时出错: {}", project.getProjectId(), e.getMessage());
                return false;
            }
        }
        
        // 对于暂停中任务名称搜索，空值有特殊含义：匹配没有暂停中任务的项目
        if ("pausedTaskName".equals(fieldName) && (searchValue == null || searchValue.isEmpty())) {
            try {
                // 获取项目下所有暂停中的任务
                List<ProjectTask> pausedTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(), "已暂停");
                // 返回true表示匹配：当项目没有暂停中任务时
                return pausedTasks.isEmpty();
            } catch (Exception e) {
                logger.error("检查项目 {} 是否有暂停中任务时出错: {}", project.getProjectId(), e.getMessage());
                return false;
            }
        }
        
        if (searchValue == null || searchValue.isEmpty()) {
            return true; // 其他字段空值条件视为匹配
        }
        
        switch (fieldName) {
            case "projectName":
                return project.getProjectName() != null && 
                       project.getProjectName().toLowerCase().contains(searchValue.toLowerCase());
            case "projectCode":
                return project.getProjectCode() != null && 
                       project.getProjectCode().toLowerCase().contains(searchValue.toLowerCase());
            case "customerName":
                return project.getCustomerName() != null && 
                       project.getCustomerName().toLowerCase().contains(searchValue.toLowerCase());
            case "projectType":
                return project.getProjectType() != null && 
                       project.getProjectType().toLowerCase().contains(searchValue.toLowerCase());
            case "visionType":
                return project.getVisionType() != null && 
                       (project.getVisionType().toLowerCase().contains(searchValue.toLowerCase()) ||
                       Arrays.asList(project.getVisionType().split(",")).stream()
                             .anyMatch(type -> type.trim().toLowerCase().contains(searchValue.toLowerCase())));
            case "responsible":
                return project.getResponsible() != null && 
                       project.getResponsible().toLowerCase().contains(searchValue.toLowerCase());
            case "salesOrderNumber":
                return project.getSalesOrderNumber() != null && 
                       project.getSalesOrderNumber().toLowerCase().contains(searchValue.toLowerCase());
            case "productPartNumber":
                return project.getProductPartNumber() != null && 
                       project.getProductPartNumber().toLowerCase().contains(searchValue.toLowerCase());
            case "status":
                return project.getStatus() != null && 
                       project.getStatus().equals(searchValue);
            case "risk":
                return project.getRisk() != null && 
                       project.getRisk().equals(searchValue);
            case "totalCost1":
                // 总成本1搜索
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchCost = new BigDecimal(searchValue);
                    BigDecimal projectCost = project.getTotalCost1();
                    
                    // 如果总成本1为null，当作0处理
                    if (projectCost == null) {
                        projectCost = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectCost.subtract(searchCost).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getTotalCost1() == null) {
                        // 总成本1为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getTotalCost1().toString().contains(searchValue);
                }
            case "totalCost2":
                // 总成本2搜索
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchCost = new BigDecimal(searchValue);
                    BigDecimal projectCost = project.getTotalCost2();
                    
                    // 如果总成本2为null，当作0处理
                    if (projectCost == null) {
                        projectCost = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectCost.subtract(searchCost).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getTotalCost2() == null) {
                        // 总成本2为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getTotalCost2().toString().contains(searchValue);
                }
            case "visionCost":
                // 普通的单机成本1搜索（如果直接搜索单机成本1值，不使用范围搜索）
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchCost = new BigDecimal(searchValue);
                    BigDecimal projectCost = project.getVisionCost();
                    
                    // 如果单机成本1为null，当作0处理
                    if (projectCost == null) {
                        projectCost = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectCost.subtract(searchCost).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getVisionCost() == null) {
                        // 单机成本1为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getVisionCost().toString().contains(searchValue);
                }
            case "visionCost2":
                // 单机成本2搜索
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchCost2 = new BigDecimal(searchValue);
                    BigDecimal projectCost2 = project.getVisionCost2();
                    
                    // 如果单机成本2为null，当作0处理
                    if (projectCost2 == null) {
                        projectCost2 = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectCost2.subtract(searchCost2).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getVisionCost2() == null) {
                        // 单机成本2为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getVisionCost2().toString().contains(searchValue);
                }
            case "ratedDurationDays":
                // 额定天数搜索
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchDays = new BigDecimal(searchValue);
                    BigDecimal projectDays = project.getRatedDurationDays();
                    
                    // 如果额定天数为null，当作0处理
                    if (projectDays == null) {
                        projectDays = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectDays.subtract(searchDays).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getRatedDurationDays() == null) {
                        // 额定天数为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getRatedDurationDays().toString().contains(searchValue);
                }
            case "difficultyCoefficient":
                // 难度系数搜索
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchCoeff = new BigDecimal(searchValue);
                    BigDecimal projectCoeff = project.getDifficultyCoefficient();
                    
                    // 如果难度系数为null，当作0处理
                    if (projectCoeff == null) {
                        projectCoeff = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectCoeff.subtract(searchCoeff).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getDifficultyCoefficient() == null) {
                        // 难度系数为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getDifficultyCoefficient().toString().contains(searchValue);
                }
            case "cameraQuantity":
                // 相机数量搜索
                try {
                    // 尝试解析为数值进行比较
                    BigDecimal searchCamera = new BigDecimal(searchValue);
                    BigDecimal projectCamera = project.getCameraQuantity();
                    
                    // 如果相机数量为null，当作0处理
                    if (projectCamera == null) {
                        projectCamera = BigDecimal.ZERO;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return projectCamera.subtract(searchCamera).abs().compareTo(new BigDecimal("0.01")) < 0;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getCameraQuantity() == null) {
                        // 相机数量为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getCameraQuantity().toString().contains(searchValue);
                }
            case "quantity":
                // 设备数量搜索
                try {
                    // 尝试解析为数值进行比较
                    Double searchQuantity = Double.parseDouble(searchValue);
                    Double projectQuantity = project.getQuantity();
                    
                    // 如果设备数量为null，当作0处理
                    if (projectQuantity == null) {
                        projectQuantity = 0.0;
                    }
                    
                    // 允许一定的小数精度误差（例如0.01）
                    return Math.abs(projectQuantity - searchQuantity) < 0.01;
                } catch (NumberFormatException e) {
                    // 如果无法解析为数值，则进行字符串比较
                    if (project.getQuantity() == null) {
                        // 设备数量为null时，如果搜索值是"0"或空字符串，则匹配
                        return "0".equals(searchValue) || searchValue.isEmpty();
                    }
                    return project.getQuantity().toString().contains(searchValue);
                }
            case "inProgressTaskName":
                // 进行中任务名称搜索（非空值情况）
                try {
                    // 获取项目下所有进行中的任务
                    List<ProjectTask> inProgressTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(), "进行中");
                    
                    // 检查是否有任务名称包含搜索关键词
                    return inProgressTasks.stream()
                            .anyMatch(task -> task.getTaskName() != null && 
                                    task.getTaskName().toLowerCase().contains(searchValue.toLowerCase()));
                } catch (Exception e) {
                    logger.error("搜索项目 {} 的进行中任务时出错: {}", project.getProjectId(), e.getMessage());
                    return false;
                }
            case "pausedTaskName":
                // 暂停中任务名称搜索（非空值情况）
                try {
                    // 获取项目下所有暂停中的任务
                    List<ProjectTask> pausedTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(), "已暂停");
                    
                    // 检查是否有任务名称包含搜索关键词
                    return pausedTasks.stream()
                            .anyMatch(task -> task.getTaskName() != null && 
                                    task.getTaskName().toLowerCase().contains(searchValue.toLowerCase()));
                } catch (Exception e) {
                    logger.error("搜索项目 {} 的暂停中任务时出错: {}", project.getProjectId(), e.getMessage());
                    return false;
                }
            default:
                return false;
        }
    }
    
    /**
     * 初始化项目的日期字段
     * @param project 需要初始化日期字段的项目
     */
    private void initializeProjectDates(Project project) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        try {
            // 初始化计划开始日期
            if (project.getPlannedStartDateTime() != null) {
                project.setPlannedStartDate(project.getPlannedStartDateTime().format(formatter));
            }
            
            // 初始化计划结束日期
            if (project.getPlannedEndDateTime() != null) {
                project.setPlannedEndDate(project.getPlannedEndDateTime().format(formatter));
            }
            
            // 初始化实际开始日期
            if (project.getActualStartDateTime() != null) {
                project.setActualStartDate(project.getActualStartDateTime().format(formatter));
            }
            
            // 初始化实际结束日期
            if (project.getActualEndDateTime() != null) {
                project.setActualEndDate(project.getActualEndDateTime().format(formatter));
            }
            
            // 初始化创建日期
            if (project.getCreatedDateTime() != null) {
                project.setCreatedDate(project.getCreatedDateTime().format(formatter));
            }
        } catch (Exception e) {
            logger.error("初始化项目日期字段时出错: {}", e.getMessage(), e);
        }
    }

    @Override
    public Page<Project> findNonArchivedProjects(Pageable pageable) {
        logger.debug("查询所有未归档项目");
        return projectRepository.findByArchiveIsNullOrArchiveNot(1, pageable);
    }
    
    @Override
    public Page<Project> findArchivedProjects(Pageable pageable) {
        logger.debug("查询所有已归档项目");
        return projectRepository.findByArchive(1, pageable);
    }
    
    @Override
    public Project archiveProject(Long id) {
        logger.info("归档项目：ID = {}", id);
        Optional<Project> projectOpt = projectRepository.findById(id);
        if (projectOpt.isPresent()) {
            Project project = projectOpt.get();
            project.setArchive(1);
            return projectRepository.save(project);
        }
        return null;
    }
    
    @Override
    public Project unarchiveProject(Long id) {
        logger.info("取消归档项目：ID = {}", id);
        Optional<Project> projectOpt = projectRepository.findById(id);
        if (projectOpt.isPresent()) {
            Project project = projectOpt.get();
            project.setArchive(0);
            return projectRepository.save(project);
        }
        return null;
    }

    @Override
    public Long countNonArchivedProjects() {
        Page<Project> projectPage = projectRepository.findByArchiveIsNullOrArchiveNot(1, PageRequest.of(0, 1));
        return projectPage.getTotalElements();
    }
    
    @Override
    public Long countNonArchivedProjectsByStatus(String status) {
        Page<Project> projectPage = projectRepository.findByArchiveIsNullOrArchiveNotAndStatus(1, status, PageRequest.of(0, 1));
        return projectPage.getTotalElements();
    }
    
    @Override
    public List<Project> findRecentNonArchivedProjects(int limit) {
        Page<Project> projectPage = projectRepository.findByArchiveIsNullOrArchiveNot(1, PageRequest.of(0, limit));
        return projectPage.getContent();
    }

    @Override
    @Transactional
    public void deleteAllProjects() {
        logger.info("开始批量删除所有项目");
        try {
            // 获取所有项目ID
            List<Project> allProjects = projectRepository.findAll();
            logger.info("总共需要删除 {} 个项目", allProjects.size());
            
            int successCount = 0;
            int failCount = 0;
            
            for (Project project : allProjects) {
                try {
                    // 使用已经实现级联删除的方法删除每个项目
                    deleteProject(project.getProjectId());
                    successCount++;
                } catch (Exception e) {
                    logger.error("批量删除时，删除项目ID {} 失败: {}", project.getProjectId(), e.getMessage());
                    failCount++;
                    // 继续处理其他项目，不中断整个过程
                }
            }
            
            logger.info("批量删除项目完成，成功: {}，失败: {}", successCount, failCount);
        } catch (Exception e) {
            logger.error("批量删除项目过程中出错: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Override
    public List<Project> saveAllProjects(List<Project> projects) {
        return projectRepository.saveAll(projects);
    }
    
    @Override
    @Transactional
    public void resetProjectId() {
        // 重置SQLite自增序列
        projectRepository.resetAutoIncrement();
        projectRepository.resetSequenceToZero();
    }
    
    @Override
    public Integer findMaxSequenceByDate(String dateStr) {
        return projectRepository.findMaxSequenceByDate(dateStr);
    }
    
    @Override
    public long getTotalProjectCount() {
        return projectRepository.count();
    }
    
    @Override
    public BigDecimal calculateTotalVisionCost() {
        logger.info("计算所有项目的单机成本1总和");
        try {
            // 获取所有项目
            List<Project> allProjects = projectRepository.findAll();
            
            if (allProjects == null || allProjects.isEmpty()) {
                logger.info("没有找到任何项目，返回零值");
                return BigDecimal.ZERO;
            }
            
            // 计算单机成本1总和
            BigDecimal totalCost = allProjects.stream()
                    .filter(project -> project.getVisionCost() != null)
                    .map(Project::getVisionCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
            logger.info("所有项目的单机成本1总和: {}", totalCost);
            return totalCost;
        } catch (Exception e) {
            logger.error("计算单机成本1总额时出错: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
    
    @Override
    public BigDecimal calculateVisionCostBySearchCriteria(Map<String, String> searchCriteria) {
        logger.info("根据搜索条件计算项目的单机成本1总和, 条件: {}", searchCriteria);
        
        if (searchCriteria == null || searchCriteria.isEmpty()) {
            logger.info("搜索条件为空，返回所有项目的单机成本1总和");
            return calculateTotalVisionCost();
        }
        
        try {
            // 使用动态搜索获取符合条件的所有项目
            List<Project> matchedProjects = dynamicSearchProjects(searchCriteria);
            
            if (matchedProjects == null || matchedProjects.isEmpty()) {
                logger.info("没有符合搜索条件的项目，返回零值");
                return BigDecimal.ZERO;
            }
            
            // 计算单机成本1总和
            BigDecimal totalCost = matchedProjects.stream()
                    .filter(project -> project.getVisionCost() != null)
                    .map(Project::getVisionCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
            logger.info("符合搜索条件的项目单机成本1总和: {}", totalCost);
            return totalCost;
        } catch (Exception e) {
            logger.error("计算符合条件的单机成本1总额时出错: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
    
    @Override
    public BigDecimal calculateTotalCost1() {
        logger.info("计算所有项目的总成本1总和");
        try {
            // 获取所有项目
            List<Project> allProjects = projectRepository.findAll();
            
            if (allProjects == null || allProjects.isEmpty()) {
                logger.info("没有找到任何项目，返回零值");
                return BigDecimal.ZERO;
            }
            
            // 计算总成本1总和
            BigDecimal totalCost = allProjects.stream()
                    .filter(project -> project.getTotalCost1() != null)
                    .map(Project::getTotalCost1)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
            logger.info("所有项目的总成本1总和: {}", totalCost);
            return totalCost;
        } catch (Exception e) {
            logger.error("计算总成本1总额时出错: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
    
    @Override
    public BigDecimal calculateTotalCost1BySearchCriteria(Map<String, String> searchCriteria) {
        logger.info("根据搜索条件计算项目的总成本1总和, 条件: {}", searchCriteria);
        
        if (searchCriteria == null || searchCriteria.isEmpty()) {
            logger.info("搜索条件为空，返回所有项目的总成本1总和");
            return calculateTotalCost1();
        }
        
        try {
            // 使用动态搜索获取符合条件的所有项目
            List<Project> matchedProjects = dynamicSearchProjects(searchCriteria);
            
            if (matchedProjects == null || matchedProjects.isEmpty()) {
                logger.info("没有符合搜索条件的项目，返回零值");
                return BigDecimal.ZERO;
            }
            
            // 计算总成本1总和
            BigDecimal totalCost = matchedProjects.stream()
                    .filter(project -> project.getTotalCost1() != null)
                    .map(Project::getTotalCost1)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
            logger.info("符合搜索条件的项目总成本1总和: {}", totalCost);
            return totalCost;
        } catch (Exception e) {
            logger.error("计算符合条件的总成本1总额时出错: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Long countProjectsByCustomerName(String customerName) {
        try {
            logger.info("统计客户名称为 '{}' 的项目数量", customerName);
            
            if (customerName == null || customerName.trim().isEmpty()) {
                logger.warn("客户名称为空，返回0");
                return 0L;
            }
            
            List<Project> allProjects = findAllProjects();
            
            long count = allProjects.stream()
                    .filter(project -> project.getCustomerName() != null && 
                             project.getCustomerName().equals(customerName.trim()))
                    .count();
                    
            logger.info("客户名称为 '{}' 的项目数量: {}", customerName, count);
            return count;
        } catch (Exception e) {
            logger.error("统计客户名称为 '{}' 的项目数量时出错: {}", customerName, e.getMessage(), e);
            return 0L;
        }
    }
}