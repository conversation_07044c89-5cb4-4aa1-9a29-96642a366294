package com.mylog.test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class TaskSortTest {
    
    static class TestTask {
        private Long taskId;
        private String status;
        private LocalDateTime createdDateTime;
        
        public TestTask(Long taskId, String status, LocalDateTime createdDateTime) {
            this.taskId = taskId;
            this.status = status;
            this.createdDateTime = createdDateTime;
        }
        
        public Long getTaskId() { return taskId; }
        public String getStatus() { return status; }
        public LocalDateTime getCreatedDateTime() { return createdDateTime; }
        
        @Override
        public String toString() {
            return String.format("Task[id=%d, status=%s, created=%s]", 
                taskId, status, createdDateTime);
        }
    }
    
    public static void main(String[] args) {
        LocalDateTime now = LocalDateTime.now();
        
        // 创建测试任务数据
        List<TestTask> tasks = Arrays.asList(
            new TestTask(1L, "已完成", now.minusDays(3)),
            new TestTask(2L, "进行中", now.minusDays(2)),
            new TestTask(3L, "未开始", now.minusDays(1)),
            new TestTask(4L, "进行中", now.minusDays(4)),
            new TestTask(5L, "已暂停", now)
        );
        
        System.out.println("排序前:");
        tasks.forEach(System.out::println);
        
        // 应用排序逻辑
        List<TestTask> sortedTasks = tasks.stream()
                .sorted((t1, t2) -> {
                    // 首先比较状态：进行中排在最前
                    boolean t1InProgress = "进行中".equals(t1.getStatus());
                    boolean t2InProgress = "进行中".equals(t2.getStatus());

                    if (t1InProgress && !t2InProgress) {
                        return -1; // t1排在前面
                    } else if (!t1InProgress && t2InProgress) {
                        return 1; // t2排在前面
                    }

                    // 状态相同时，按创建日期倒序排序（最近的排在前面）
                    if (t1.getCreatedDateTime() == null || t2.getCreatedDateTime() == null) {
                        return 0;
                    }
                    return t2.getCreatedDateTime().compareTo(t1.getCreatedDateTime());
                })
                .collect(Collectors.toList());
        
        System.out.println("\n排序后:");
        sortedTasks.forEach(System.out::println);
        
        // 验证结果
        System.out.println("\n验证结果:");
        boolean correct = true;
        for (int i = 0; i < sortedTasks.size(); i++) {
            TestTask task = sortedTasks.get(i);
            if (i < 2) { // 前两个应该是"进行中"
                if (!"进行中".equals(task.getStatus())) {
                    System.out.println("ERROR: 位置 " + i + " 应该是进行中任务，但是是: " + task.getStatus());
                    correct = false;
                }
            }
        }
        
        if (correct) {
            System.out.println("排序逻辑正确！进行中任务确实排在前面。");
        } else {
            System.out.println("排序逻辑有问题！");
        }
    }
}
