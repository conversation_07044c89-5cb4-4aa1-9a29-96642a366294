package com.mylog.controller.workflow;

import com.mylog.controller.BaseController;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.service.ApprovalRecordService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.service.WorkflowTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程管理主控制器
 */
@Controller
@RequestMapping("/workflow")
public class WorkflowController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowController.class);

    @Autowired
    private WorkflowTemplateService templateService;

    @Autowired
    private WorkflowInstanceService instanceService;

    @Autowired
    private ApprovalRecordService recordService;

    /**
     * 显示流程管理主页面
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public String index(Model model) {
        try {
            logger.info("开始加载流程管理主页面");
            List<WorkflowTemplate> recentTemplates = new ArrayList<>();
            List<WorkflowInstance> recentInstances = new ArrayList<>();
            
            // 获取处理中的流程实例数量
            long processingCount = 0;
            try {
                processingCount = instanceService.countProcessingInstances();
                logger.info("当前处理中的流程实例数量: {}", processingCount);
                model.addAttribute("processingInstanceCount", processingCount);
            } catch (Exception e) {
                logger.error("获取处理中流程实例数量失败: {}", e.getMessage(), e);
                model.addAttribute("processingInstanceCount", 0);
            }
            
            // 获取最近的流程模板
            try {
                // 通过服务获取模板数据
                Pageable templatePageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"));
                Page<WorkflowTemplate> templatesPage = templateService.findAllTemplates(templatePageable);
                
                if (templatesPage != null && !templatesPage.isEmpty()) {
                    recentTemplates = templatesPage.getContent();
                    logger.info("成功获取 {} 个流程模板", recentTemplates.size());
                    // 调试日志：输出每个模板的信息
                    for(WorkflowTemplate template : recentTemplates) {
                        logger.debug("模板: ID={}, 名称={}, 创建时间={}", 
                            template.getTemplateId(), 
                            template.getTemplateName(),
                            template.getCreatedDateTime());
                    }
                } else {
                    logger.warn("未找到任何流程模板");
                }
            } catch (Exception e) {
                logger.error("获取流程模板失败: {}", e.getMessage(), e);
                // 使用空列表
            }
            
            // 获取最近的流程实例
            try {
                // 使用实例服务获取数据
                Pageable instancePageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"));
                Page<WorkflowInstance> instancePage = instanceService.findAllInstances(instancePageable);
                
                if (instancePage != null && !instancePage.isEmpty()) {
                    recentInstances = instancePage.getContent();
                    logger.info("成功获取 {} 个流程实例", recentInstances.size());
                    // 调试日志：输出每个实例的信息
                    for(WorkflowInstance instance : recentInstances) {
                        logger.debug("实例: ID={}, 标题={}, 状态={}, 创建时间={}", 
                            instance.getInstanceId(), 
                            instance.getTitle(),
                            instance.getStatus(),
                            instance.getCreatedDateTime());
                    }
                } else {
                    logger.warn("未找到任何流程实例");
                }
            } catch (Exception e) {
                logger.error("获取流程实例失败: {}", e.getMessage(), e);
                // 使用空列表
            }

            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取用户的待办任务
            List<WorkflowInstance> todoTasks;
            try {
                todoTasks = instanceService.findTodoTasks(currentUsername);
                logger.info("成功获取 {} 个待办任务", todoTasks.size());
            } catch (Exception e) {
                logger.error("获取待办任务失败: {}", e.getMessage(), e);
                todoTasks = java.util.Collections.emptyList();
            }

            // 为最近流程实例计算步骤信息
            java.util.Map<Long, Integer> instanceSteps = new java.util.HashMap<>();
            
            // 遍历实例，计算当前步骤
            for (WorkflowInstance instance : recentInstances) {
                if (instance.getInstanceId() != null) {
                    try {
                        // 获取审批记录
                        var records = recordService.findRecordsByInstanceId(instance.getInstanceId());
                        
                        // 计算当前步骤
                        int currentStep = 0;
                        boolean hasSubmit = false;
                        int approvalSteps = 0;
                        
                        for (var record : records) {
                            if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.SUBMIT) {
                                hasSubmit = true;
                            } else if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.APPROVE
                                    || record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.REJECT) {
                                approvalSteps++;
                            }
                        }
                        
                        // 如果已经提交，当前步骤至少是1
                        currentStep = hasSubmit ? 1 : 0;
                        // 加上审批步骤数
                        currentStep += approvalSteps;
                        
                        // 保存到Map中
                        instanceSteps.put(instance.getInstanceId(), currentStep);
                        logger.debug("计算实例ID {} 当前步骤: {}", instance.getInstanceId(), currentStep);
                    } catch (Exception e) {
                        logger.warn("计算实例ID {} 步骤时出错: {}", instance.getInstanceId(), e.getMessage());
                        // 默认为0步
                        instanceSteps.put(instance.getInstanceId(), 0);
                    }
                }
            }

            // 添加到模型
            model.addAttribute("recentTemplates", recentTemplates);
            model.addAttribute("recentInstances", recentInstances);
            model.addAttribute("todoTasks", todoTasks);
            model.addAttribute("activeMenu", "workflow");
            model.addAttribute("instanceSteps", instanceSteps);
            
            logger.info("成功加载流程管理主页面");
            return "workflow/index";
        } catch (Exception e) {
            logger.error("加载流程管理主页面时出错: {}", e.getMessage(), e);
            // 不要直接抛出异常，而是添加错误消息并返回错误视图
            model.addAttribute("error", "加载流程管理主页面时出错: " + e.getMessage());
            model.addAttribute("activeMenu", "workflow");
            return "error/general";
        }
    }

    /**
     * 显示我的流程主页面
     */
    @GetMapping("/my")
    public String myWorkflow(Model model) {
        try {
            logger.info("开始加载我的流程主页面");
            
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            logger.info("当前用户: {}", currentUsername);

            // 使用Map存储实例的步骤信息
            java.util.Map<Long, Integer> instanceStepsMap = new java.util.HashMap<>();
            
            // 使用服务方法获取用户发起的流程实例
            Pageable instancePageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"));
            Page<WorkflowInstance> myInstances = instanceService.searchInstances(
                    null, null, currentUsername, null, null, null, instancePageable);
            logger.info("使用服务方法成功获取 {} 个用户发起的流程", myInstances.getContent().size());
            
            // 使用服务方法获取待办任务
            List<WorkflowInstance> todoTasks = instanceService.findTodoTasks(currentUsername);
            logger.info("使用服务方法成功获取 {} 个待办任务", todoTasks.size());
            
            // 使用服务方法获取已办任务
            Pageable donePageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"));
            Page<WorkflowInstance> doneTasks = instanceService.findDoneTasks(currentUsername, donePageable);
            logger.info("使用服务方法成功获取 {} 个已办任务", doneTasks.getContent().size());
            
            // 初始化实例步骤信息
            for (WorkflowInstance instance : myInstances.getContent()) {
                if (instance.getInstanceId() != null) {
                    instanceStepsMap.put(instance.getInstanceId(), 0);
                }
            }
            
            for (WorkflowInstance task : todoTasks) {
                if (task.getInstanceId() != null && !instanceStepsMap.containsKey(task.getInstanceId())) {
                    instanceStepsMap.put(task.getInstanceId(), 1); // 默认至少一步
                }
            }
            
            for (WorkflowInstance task : doneTasks.getContent()) {
                if (task.getInstanceId() != null && !instanceStepsMap.containsKey(task.getInstanceId())) {
                    instanceStepsMap.put(task.getInstanceId(), task.getStepCount() != null ? task.getStepCount() : 1);
                }
            }
            
            // 更新实例步骤信息（从审批记录中计算）
            try {
                // 合并所有实例ID
                java.util.Set<Long> allInstanceIds = new java.util.HashSet<>(instanceStepsMap.keySet());
                
                for (Long instanceId : allInstanceIds) {
                    try {
                        // 获取该实例的审批记录
                        var records = recordService.findRecordsByInstanceId(instanceId);
                        
                        // 计算步骤
                        int currentStep = 0;
                        boolean hasSubmit = false;
                        int approvalSteps = 0;
                        
                        for (var record : records) {
                            if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.SUBMIT) {
                                hasSubmit = true;
                            } else if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.APPROVE
                                    || record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.REJECT) {
                                approvalSteps++;
                            }
                        }
                        
                        // 如果已经提交，当前步骤至少是1
                        currentStep = hasSubmit ? 1 : 0;
                        // 加上审批步骤数
                        currentStep += approvalSteps;
                        
                        // 更新步骤信息
                        instanceStepsMap.put(instanceId, currentStep);
                        logger.debug("实例 {} 当前步骤: {}", instanceId, currentStep);
                    } catch (Exception e) {
                        logger.warn("无法获取实例ID: {} 的审批记录: {}", instanceId, e.getMessage());
                        // 保持默认步骤值
                    }
                }
            } catch (Exception e) {
                logger.error("更新实例步骤信息失败: {}", e.getMessage());
                // 使用已有的默认步骤信息
            }

            // 添加到模型
            model.addAttribute("myInstances", myInstances);
            model.addAttribute("todoTasks", todoTasks);
            model.addAttribute("doneTasks", doneTasks);
            model.addAttribute("activeMenu", "myworkflow");
            model.addAttribute("instanceSteps", instanceStepsMap);
            
            logger.info("成功加载我的流程主页面");
            return "workflow/my-workflow";
        } catch (Exception e) {
            logger.error("加载我的流程主页面时出错: {}", e.getMessage(), e);
            model.addAttribute("error", "加载我的流程主页面失败: " + e.getMessage());
            model.addAttribute("activeMenu", "myworkflow");
            return "error/general";
        }
    }
}
