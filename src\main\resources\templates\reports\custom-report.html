<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('自定义报表')}">
    <meta charset="UTF-8">
    <title>自定义报表</title>
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">自定义报表</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToPDF()">导出PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">导出Excel</button>
                        </div>
                        <a th:href="@{/reports}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">报表配置</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/reports/custom-report}" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="reportType" class="form-label">报表类型</label>
                                <select class="form-select" id="reportType" name="reportType">
                                    <option value="task" th:selected="${reportType == 'task'}">任务报表</option>
                                    <option value="project" th:selected="${reportType == 'project'}">项目报表</option>
                                    <option value="risk" th:selected="${reportType == 'risk'}">风险报表</option>
                                    <option value="personnel" th:selected="${reportType == 'personnel'}">人员报表</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="groupBy" class="form-label">分组维度</label>
                                <select class="form-select" id="groupBy" name="groupBy">
                                    <option value="status" th:selected="${groupBy == 'status'}">状态</option>
                                    <option value="responsible" th:selected="${groupBy == 'responsible'}">负责人</option>
                                    <option value="risk" th:selected="${groupBy == 'risk'}">风险等级</option>
                                    <option value="date" th:selected="${groupBy == 'date'}">创建日期</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                            </div>
                            <div class="col-md-2">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">生成报表</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 图表展示区域 -->
                <div class="row" th:if="${reportData != null}">
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0" th:text="${reportTitle ?: '自定义报表'}">自定义报表</h5>
                                <span class="badge bg-info" th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd')}">2023-01-01</span>
                            </div>
                            <div class="card-body">
                                <canvas id="mainChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="card mb-4" th:if="${reportData != null}">
                    <div class="card-header">
                        <h5 class="mb-0">详细数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th th:text="${groupBy == 'status' ? '状态' : (groupBy == 'responsible' ? '负责人' : (groupBy == 'risk' ? '风险等级' : '日期'))}">分组</th>
                                        <th>数量</th>
                                        <th>占比</th>
                                    </tr>
                                </thead>
                                <tbody id="reportDataTable">
                                    <tr th:if="${reportData.isEmpty()}">
                                        <td colspan="3" class="text-center">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 相关任务列表 -->
                <div class="card mb-4" th:if="${tasks != null && !tasks.isEmpty()}">
                    <div class="card-header">
                        <h5 class="mb-0">相关任务列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>任务名称</th>
                                        <th>负责人</th>
                                        <th>风险</th>
                                        <th>进度</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${tasks}">
                                        <td>
                                            <a th:href="@{/tasks/{id}(id=${task.taskId})}" th:text="${task.taskName}">任务名称</a>
                                        </td>
                                        <td th:text="${task.responsible}">负责人</td>
                                        <td>
                                            <span th:class="${task.risk == '高' ? 'badge bg-danger' : (task.risk == '中' ? 'badge bg-warning' : 'badge bg-success')}"
                                                  th:text="${task.risk}">风险</span>
                                        </td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                    th:style="'width: ' + (${task.progressPercentage} ?: 0) + '%'"
                                                    th:text="(${task.progressPercentage} ?: 0) + '%'">0%</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div th:switch="${task.status}">                                                <span th:case="'进行中'" class="badge bg-primary" th:text="${task.status}">进行中</span>
                                                <span th:case="'已完成'" class="badge bg-success" th:text="${task.status}">已完成</span>
                                                <span th:case="'已暂停'" class="badge bg-dark" th:text="${task.status}">已暂停</span>
                                                <span th:case="*" class="badge bg-info" th:text="${task.status}">其他状态</span>
                                            </div>
                                        </td>
                                        <td th:text="${task.createdDate}">创建时间</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 无数据提示 -->
                <div class="alert alert-info" th:if="${noData}">
                    <i class="bi bi-info-circle me-2"></i> 请配置报表条件并点击"生成报表"按钮查看数据。
                </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

    <!-- 自定义报表脚本 -->
    <script th:inline="javascript">
        // 从模型获取报表数据
        const reportData = /*[[${reportData}]]*/ {};
        const groupBy = /*[[${groupBy}]]*/ 'status';
        const reportType = /*[[${reportType}]]*/ 'task';

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 如果有报表数据，则渲染报表
            if (reportData && Object.keys(reportData).length > 0) {
                renderChart();
                populateTable();
            }

            // 根据报表类型动态更新分组维度选项
            document.getElementById('reportType').addEventListener('change', updateGroupByOptions);
        });

        // 根据报表类型更新分组维度选项
        function updateGroupByOptions() {
            const reportType = document.getElementById('reportType').value;
            const groupBySelect = document.getElementById('groupBy');
            const currentValue = groupBySelect.value;

            // 清空当前选项
            groupBySelect.innerHTML = '';

            // 添加通用选项
            const commonOptions = [
                { value: 'date', text: '创建日期' }
            ];

            // 根据报表类型添加特定选项
            let specificOptions = [];

            if (reportType === 'task') {
                specificOptions = [
                    { value: 'status', text: '状态' },
                    { value: 'responsible', text: '负责人' },
                    { value: 'risk', text: '风险等级' }
                ];
            } else if (reportType === 'project') {
                specificOptions = [
                    { value: 'status', text: '状态' },
                    { value: 'responsible', text: '项目负责人' }
                ];
            } else if (reportType === 'risk') {
                specificOptions = [
                    { value: 'risk', text: '风险等级' },
                    { value: 'status', text: '状态' }
                ];
            } else if (reportType === 'personnel') {
                specificOptions = [
                    { value: 'responsible', text: '负责人' },
                    { value: 'efficiency', text: '效率' }
                ];
            }

            // 合并选项并添加到选择器
            const allOptions = [...specificOptions, ...commonOptions];

            allOptions.forEach(option => {
                const optElement = document.createElement('option');
                optElement.value = option.value;
                optElement.textContent = option.text;
                if (option.value === currentValue) {
                    optElement.selected = true;
                }
                groupBySelect.appendChild(optElement);
            });

            // 如果当前值不在新选项中，选择第一个选项
            if (!allOptions.some(o => o.value === currentValue)) {
                groupBySelect.value = allOptions[0].value;
            }
        }

        // 渲染图表
        function renderChart() {
            const ctx = document.getElementById('mainChart').getContext('2d');

            // 准备图表数据
            const labels = Object.keys(reportData);
            const values = Object.values(reportData);

            // 选择合适的图表类型
            let chartType = 'bar';
            let chartOptions = {};

            if (groupBy === 'status' || groupBy === 'risk') {
                chartType = 'pie';
                chartOptions = {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                };
            } else if (groupBy === 'date') {
                chartType = 'line';
                chartOptions = {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                };
            } else {
                chartOptions = {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量'
                            }
                        }
                    }
                };
            }

            // 生成颜色
            const colors = generateColors(labels.length);

            // 创建图表
            new Chart(ctx, {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: '数量',
                        data: values,
                        backgroundColor: colors,
                        borderColor: chartType === 'line' ? '#6f42c1' : colors,
                        borderWidth: 1
                    }]
                },
                options: chartOptions
            });
        }

        // 生成颜色数组
        function generateColors(count) {
            const baseColors = [
                '#6f42c1', // 紫色（主色）
                '#007bff', // 蓝色
                '#28a745', // 绿色
                '#ffc107', // 黄色
                '#dc3545', // 红色
                '#17a2b8', // 青色
                '#fd7e14', // 橙色
                '#20c997', // 青绿色
                '#6c757d'  // 灰色
            ];

            const colors = [];
            for (let i = 0; i < count; i++) {
                colors.push(baseColors[i % baseColors.length]);
            }

            return colors;
        }

        // 填充详细数据表格
        function populateTable() {
            const tableBody = document.getElementById('reportDataTable');
            tableBody.innerHTML = '';

            if (!reportData || Object.keys(reportData).length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.setAttribute('colspan', '3');
                cell.className = 'text-center';
                cell.textContent = '暂无数据';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            // 计算总数
            const total = Object.values(reportData).reduce((sum, value) => sum + value, 0);

            // 对数据进行排序
            const sortedEntries = Object.entries(reportData).sort((a, b) => b[1] - a[1]);

            // 添加表格行
            sortedEntries.forEach(([key, value]) => {
                const row = document.createElement('tr');

                // 分组值
                const keyCell = document.createElement('td');
                keyCell.textContent = key;
                row.appendChild(keyCell);

                // 数量
                const valueCell = document.createElement('td');
                valueCell.textContent = value;
                row.appendChild(valueCell);

                // 占比
                const percentCell = document.createElement('td');
                const percent = ((value / total) * 100).toFixed(2);
                percentCell.textContent = `${percent}%`;
                row.appendChild(percentCell);

                tableBody.appendChild(row);
            });
        }

        // 导出PDF
        function exportToPDF() {
            alert('PDF导出功能正在开发中，请稍后再试。');
        }

        // 导出Excel
        function exportToExcel() {
            alert('Excel导出功能正在开发中，请稍后再试。');
        }
    </script>
</body>
</html>