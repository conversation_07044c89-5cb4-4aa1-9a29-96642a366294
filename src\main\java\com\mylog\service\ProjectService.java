package com.mylog.service;

import com.mylog.model.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ProjectService {
    
    List<Project> findAllProjects();
    
    Page<Project> findAllProjects(Pageable pageable);
    
    Optional<Project> findProjectById(Long id);
    
    Project saveProject(Project project);
    
    void deleteProject(Long id);
    
    List<Project> findProjectsByResponsible(String responsible);
    
    Page<Project> findProjectsByResponsible(String responsible, Pageable pageable);
    
    /**
     * 统计特定负责人和状态的项目数量
     * @param responsible 负责人
     * @param status 状态
     * @return 项目数量
     */
    Long countProjectsByResponsibleAndStatus(String responsible, String status);
    
    /**
     * 统计特定负责人在指定日期之后创建的项目数量
     * @param responsible 负责人
     * @param createdAfter 创建日期（格式：yyyy-MM-dd HH:mm:ss）
     * @return 项目数量
     */
    Long countProjectsByResponsibleAndCreatedDateAfter(String responsible, String createdAfter);
    
    /**
     * 统计特定负责人在指定日期范围内已完成的项目数量
     * @param responsible 负责人
     * @param completedAfter 完成日期下限（格式：yyyy-MM-dd HH:mm:ss）
     * @return 项目数量
     */
    Long countCompletedProjectsByResponsibleAndEndDateAfter(String responsible, String completedAfter);
    
    List<Project> findProjectsByStatus(String status);
    
    Page<Project> findProjectsByStatus(String status, Pageable pageable);
    
    /**
     * 统计特定状态的项目数量
     * @param status 状态
     * @return 项目数量
     */
    Long countProjectsByStatus(String status);
    
    /**
     * 根据关键字搜索项目
     * @param keyword 关键字
     * @return 项目列表
     * @deprecated 请使用 {@link #dynamicSearchProjects(Map)} 替代
     */
    @Deprecated
    List<Project> searchProjects(String keyword);
    
    /**
     * 根据关键字搜索项目（分页版本）
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 分页项目列表
     * @deprecated 请使用 {@link #dynamicSearchProjects(Map, Pageable)} 替代
     */
    @Deprecated
    Page<Project> searchProjects(String keyword, Pageable pageable);
    
    List<Project> findProjectsByDateRange(String dateField, LocalDateTime startDate, LocalDateTime endDate);
    
    Page<Project> findProjectsByDateRange(String dateField, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    /**
     * 多字段组合搜索项目
     * @param projectName 项目名称
     * @param customerName 客户名称
     * @param status 项目状态
     * @return 项目列表
     * @deprecated 请使用 {@link #dynamicSearchProjects(Map)} 替代
     */
    @Deprecated
    List<Project> advancedSearchProjects(String projectName, String customerName, String status);
    
    /**
     * 多字段组合搜索项目（分页版本）
     * @param projectName 项目名称
     * @param customerName 客户名称
     * @param status 项目状态
     * @param pageable 分页参数
     * @return 分页项目列表
     * @deprecated 请使用 {@link #dynamicSearchProjects(Map, Pageable)} 替代
     */
    @Deprecated
    Page<Project> advancedSearchProjects(String projectName, String customerName, String status, Pageable pageable);
    
    /**
     * 动态字段搜索项目
     * @param searchCriteria 搜索条件映射，键为字段名，值为搜索值
     * @return 项目列表
     */
    List<Project> dynamicSearchProjects(Map<String, String> searchCriteria);
    
    /**
     * 动态字段搜索项目（分页版本）
     * @param searchCriteria 搜索条件映射，键为字段名，值为搜索值
     * @param pageable 分页参数
     * @return 分页项目列表
     */
    Page<Project> dynamicSearchProjects(Map<String, String> searchCriteria, Pageable pageable);
    
    /**
     * 查找所有非归档项目
     * @param pageable 分页参数
     * @return 分页非归档项目列表
     */
    Page<Project> findNonArchivedProjects(Pageable pageable);
    
    /**
     * 查找所有归档项目
     * @param pageable 分页参数
     * @return 分页归档项目列表
     */
    Page<Project> findArchivedProjects(Pageable pageable);
    
    /**
     * 归档项目
     * @param id 项目ID
     * @return 更新后的项目
     */
    Project archiveProject(Long id);
    
    /**
     * 取消归档项目
     * @param id 项目ID
     * @return 更新后的项目
     */
    Project unarchiveProject(Long id);
    
    /**
     * 统计未归档项目总数
     * @return 未归档项目数量
     */
    Long countNonArchivedProjects();
    
    /**
     * 统计未归档且处于特定状态的项目数量
     * @param status 项目状态
     * @return 符合条件的项目数量
     */
    Long countNonArchivedProjectsByStatus(String status);
    
    /**
     * 获取最近的未归档项目
     * @param limit 限制数量
     * @return 最近的未归档项目列表
     */
    List<Project> findRecentNonArchivedProjects(int limit);
    
    /**
     * 删除所有项目
     */
    void deleteAllProjects();
    
    /**
     * 批量保存项目
     * @param projects 项目列表
     * @return 保存后的项目列表
     */
    List<Project> saveAllProjects(List<Project> projects);
    
    /**
     * 重置项目表自增ID
     */
    void resetProjectId();
    
    /**
     * 查找指定日期的最大序号
     * @param dateStr 日期字符串，格式为yyyyMMdd
     * @return 最大序号，如果没有则返回null
     */
    Integer findMaxSequenceByDate(String dateStr);

    /**
     * 获取项目总数
     * @return 项目总数
     */
    long getTotalProjectCount();
    
    /**
     * 计算所有项目的单机成本1总和
     * @return 单机成本1总和
     */
    java.math.BigDecimal calculateTotalVisionCost();
    
    /**
     * 计算所有项目的总成本1总和
     * @return 总成本1总和
     */
    java.math.BigDecimal calculateTotalCost1();
    
    /**
     * 根据搜索条件计算项目的单机成本1总和
     * @param searchCriteria 搜索条件
     * @return 单机成本1总和
     */
    java.math.BigDecimal calculateVisionCostBySearchCriteria(Map<String, String> searchCriteria);
    
    /**
     * 根据搜索条件计算项目的总成本1总和
     * @param searchCriteria 搜索条件
     * @return 总成本1总和
     */
    java.math.BigDecimal calculateTotalCost1BySearchCriteria(Map<String, String> searchCriteria);
    
    /**
     * 统计特定客户名称的项目数量
     * @param customerName 客户名称
     * @return 项目数量
     */
    Long countProjectsByCustomerName(String customerName);
}