package com.mylog.util;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 处理任务文件提交的工具类
 */
public class SubmitFileUtils {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    private static final DateTimeFormatter FORMATTERTIMER = DateTimeFormatter.ofPattern("HHmmss");

    // 流程附件保存目录
    public static final String WORKFLOW_ATTACHMENT_DIR = "submit5";

    /**
     * 根据任务名称确定文件应该保存的目录
     *
     * @param taskName 任务名称
     * @return 保存目录名称
     */
    public static String determineSubmitDirectory(String taskName) {
        if (taskName != null) {
            if (taskName.contains("项目评估")) {
                return "submit1";
            } else if (taskName.contains("器件选型")) {
                return "submit2";
            } else if (taskName.contains("BOM制作")) {
                return "submit3";
            }
        }
        // 默认保存到submit4
        return "submit4";
    }

    /**
     * 生成提交文件的新文件名
     *
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param taskName 任务名称
     * @param fileNumber 文件编号（1或2）
     * @param fileExtension 文件扩展名（包括点，如".pdf"）
     * @return 按格式生成的新文件名
     */
    public static String generateSubmitFilename(String projectCode, String projectName, String taskName,
                                               int fileNumber, String fileExtension) {
        // 文件系统不允许的字符替换为下划线
        String safeProjectName = projectName.replaceAll("[<>:\"/\\\\|?*]", "_");
        String safeTaskName = taskName.replaceAll("[<>:\"/\\\\|?*]", "_");
        String timestamp = LocalDateTime.now().format(FORMATTERTIMER);
        return projectCode + "_" + safeProjectName + "_" + safeTaskName + "_" + timestamp + "_" + fileNumber + fileExtension;
    }

    /**
     * 获取文件的完整保存路径
     *
     * @param basePath 基础数据路径
     * @param submitDir 提交目录
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param taskName 任务名称
     * @param fileNumber 文件编号
     * @param fileExtension 文件扩展名
     * @return 完整的文件保存路径
     */
    public static Path getSubmitFilePath(String basePath, String submitDir,
                                        String projectCode, String projectName, String taskName,
                                        int fileNumber, String fileExtension) {
        String filename = generateSubmitFilename(projectCode, projectName, taskName, fileNumber, fileExtension);
        return Paths.get(basePath, submitDir, filename);
    }    /**
     * 生成流程附件的文件名
     *
     * @param instanceTitle 流程标题
     * @param processId 流程ID
     * @param fileNumber 文件编号
     * @param fileExtension 文件扩展名（包括点，如".pdf"）
     * @return 按格式生成的新文件名
     */
    public static String generateWorkflowAttachmentFilename(String instanceTitle, String processId,
                                                          int fileNumber, String fileExtension) {
        // 文件系统不允许的字符替换为下划线
        String safeTitle = instanceTitle.replaceAll("[<>:\"/\\\\|?*]", "_");
        String timestamp = LocalDateTime.now().format(FORMATTERTIMER);
        return safeTitle + "_流程" + processId + "_" + timestamp + "_" + fileNumber + fileExtension;
    }    /**
     * 获取流程附件的完整保存路径
     *
     * @param basePath 基础数据路径
     * @param instanceTitle 流程标题
     * @param processId 流程ID
     * @param fileNumber 文件编号
     * @param fileExtension 文件扩展名
     * @return 完整的文件保存路径
     */
    public static Path getWorkflowAttachmentPath(String basePath, String instanceTitle,
                                               String processId, int fileNumber, String fileExtension) {
        String filename = generateWorkflowAttachmentFilename(instanceTitle, processId, fileNumber, fileExtension);
        return Paths.get(basePath, WORKFLOW_ATTACHMENT_DIR, filename);
    }
}