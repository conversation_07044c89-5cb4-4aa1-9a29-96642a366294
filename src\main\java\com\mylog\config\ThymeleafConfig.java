package com.mylog.config;

import com.mylog.util.DateTimeUtils;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thymeleaf.dialect.AbstractProcessorDialect;
import org.thymeleaf.processor.IProcessor;
import org.thymeleaf.standard.StandardDialect;
import org.thymeleaf.standard.expression.StandardExpressions;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.context.ITemplateContext;
import org.thymeleaf.engine.AttributeName;
import org.thymeleaf.model.IProcessableElementTag;
import org.thymeleaf.processor.element.AbstractAttributeTagProcessor;
import org.thymeleaf.processor.element.IElementTagStructureHandler;
import org.thymeleaf.standard.expression.IStandardExpression;
import org.thymeleaf.standard.expression.IStandardExpressionParser;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 自定义Thymeleaf方言配置，提供日期时间格式化功能
 */
@Configuration
public class ThymeleafConfig {

    /**
     * 注册自定义方言
     */
    @Bean
    public DateTimeDialect dateTimeDialect() {
        return new DateTimeDialect();
    }

    /**
     * 自定义日期时间方言
     */
    public static class DateTimeDialect extends AbstractProcessorDialect {

        private static final String DIALECT_NAME = "DateTime Format Dialect";
        private static final String PREFIX = "dt";
        
        public DateTimeDialect() {
            super(DIALECT_NAME, PREFIX, StandardDialect.PROCESSOR_PRECEDENCE);
        }

        @Override
        public Set<IProcessor> getProcessors(String dialectPrefix) {
            Set<IProcessor> processors = new HashSet<>();
            
            // 添加属性处理器
            processors.add(new DateTimeFormatAttrProcessor(dialectPrefix));
            
            return processors;
        }
    }

    /**
     * 日期时间格式化属性处理器
     */
    private static class DateTimeFormatAttrProcessor extends AbstractAttributeTagProcessor {

        private static final String ATTR_NAME = "format";
        private static final int PRECEDENCE = 10000;

        public DateTimeFormatAttrProcessor(String dialectPrefix) {
            super(
                TemplateMode.HTML,  // 模板模式
                dialectPrefix,      // 方言前缀
                null,               // 标签名 (null表示适用于所有标签)
                false,              // 是否应用于特定标签
                ATTR_NAME,          // 属性名
                true,               // 应用属性是否需要被移除
                PRECEDENCE,         // 优先级
                true                // 异步支持
            );
        }

        @Override
        protected void doProcess(
                ITemplateContext context, 
                IProcessableElementTag tag,
                AttributeName attributeName,
                String attributeValue,
                IElementTagStructureHandler structureHandler) {
                
            // 解析表达式
            final IStandardExpressionParser parser = 
                StandardExpressions.getExpressionParser(context.getConfiguration());
            
            final IStandardExpression expression = parser.parseExpression(context, attributeValue);
            final Object expressionResult = expression.execute(context);
            
            if (expressionResult == null) {
                structureHandler.setBody("", false);
                return;
            }
            
            // 根据值类型进行不同处理
            String formattedDateTime;
            
            if (expressionResult instanceof LocalDateTime) {
                // 如果是LocalDateTime类型，直接使用DateTimeUtils格式化
                formattedDateTime = DateTimeUtils.formatDateTime((LocalDateTime) expressionResult);
            } else if (expressionResult instanceof String) {
                // 如果是字符串，先尝试解析为LocalDateTime，然后格式化
                String dateTimeStr = (String) expressionResult;
                
                if (dateTimeStr.contains("T")) {
                    // 包含T，可能是ISO格式
                    formattedDateTime = DateTimeUtils.convertIsoToStandard(dateTimeStr);
                } else {
                    formattedDateTime = dateTimeStr; // 不做转换
                }
            } else {
                // 其他类型，直接转为字符串
                formattedDateTime = expressionResult.toString();
            }
            
            // 将格式化后的字符串设置为标签内容
            structureHandler.setBody(formattedDateTime != null ? formattedDateTime : "", false);
        }
    }
}
