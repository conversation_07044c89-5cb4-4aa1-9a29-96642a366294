<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('工期管理')}">
    <meta charset="UTF-8">
    <title>工期管理</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script#customScript})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">工期管理 <small class="fs-6">（共 <span th:text="${workHoursPage != null ? workHoursPage.totalElements : 0}">0</span> 条记录）</small></h1>            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a th:href="@{/work-hours/new}" class="btn btn-sm btn-primary" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
                        <i class="bi bi-plus-circle me-1"></i>新增工期记录
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-warning" sec:authorize="hasRole('ADMIN')" 
                            onclick="repairWorkHoursRecords()" id="repairBtn">
                        <i class="bi bi-tools me-1"></i>修补工期记录
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" sec:authorize="hasRole('ADMIN')" 
                            onclick="fixResponsiblePersonData()" id="fixResponsibleBtn">
                        <i class="bi bi-person-check me-1"></i>修复所有责任人数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索条件 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-search me-2"></i>搜索条件
                    <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#searchForm">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse show" id="searchForm">
                <div class="card-body">
                    <form th:action="@{/work-hours}" method="get" class="row g-3">
                        <input type="hidden" name="size" value="20">
                        <div class="col-md-3">
                            <label class="form-label">业务类型</label>
                            <select name="businessType" class="form-select">
                                <option value="">全部</option>
                                <option value="项目" th:selected="${param.businessType != null && param.businessType[0] == '项目'}">项目</option>
                                <option value="任务" th:selected="${param.businessType != null && param.businessType[0] == '任务'}">任务</option>
                                <option value="培训" th:selected="${param.businessType != null && param.businessType[0] == '培训'}">培训</option>
                                <option value="会议" th:selected="${param.businessType != null && param.businessType[0] == '会议'}">会议</option>
                                <option value="其他" th:selected="${param.businessType != null && param.businessType[0] == '其他'}">其他</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">业务ID</label>
                            <input type="number" name="businessId" class="form-control" th:value="${param.businessId != null ? param.businessId[0] : ''}" placeholder="请输入业务ID">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">开始时间</label>
                            <input type="date" name="startDate" class="form-control" th:value="${param.startDate != null ? param.startDate[0] : ''}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">结束时间</label>
                            <input type="date" name="endDate" class="form-control" th:value="${param.endDate != null ? param.endDate[0] : ''}">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search me-1"></i>搜索
                            </button>
                            <a th:href="@{/work-hours(size=20)}" class="btn btn-outline-secondary ms-2">
                                <i class="bi bi-arrow-clockwise me-1"></i>重置
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 责任人绩效分统计图 -->
        <div class="card mb-4" id="bonusChartCard" th:if="${workHoursPage != null}">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bar-chart-line me-2"></i>责任人绩效分统计</h5>
            </div>
            <div class="card-body">
                <div style="height: 320px;">
                    <canvas id="bonusByResponsibleChart"></canvas>
                </div>
                <div class="text-muted small mt-2">基于当前列表数据统计，数据变化后自动刷新。</div>
            </div>
        </div>

        <!-- 工期记录列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">工期记录列表</h5>
                <div class="btn-group" sec:authorize="hasRole('ADMIN')">
                    <button type="button" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-file-earmark-excel me-1"></i>导出Excel
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info" th:if="${workHoursPage == null || workHoursPage.empty}">
                    <i class="bi bi-info-circle me-2"></i>
                    暂无工期记录。
                </div>

                <!-- 工期记录表格 -->
                <div class="table-responsive" th:if="${workHoursPage != null && !workHoursPage.empty}">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th style="white-space: nowrap;">ID</th>
                                <th style="white-space: nowrap;">业务类型</th>
                                <th style="white-space: nowrap;">业务ID</th>
                                <th style="white-space: nowrap;">累计工期</th>
                                <th style="white-space: nowrap;">工期变化</th>
                                <th style="white-space: nowrap;">原因</th>
                                <th style="white-space: nowrap;">责任人</th>
                                <th style="white-space: nowrap;">创建人</th>
                                <th style="white-space: nowrap;">备注</th>
                                <th style="white-space: nowrap;">绩效分</th>
                                <th style="white-space: nowrap;">创建时间</th>
                                <th style="white-space: nowrap;" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="workHours : ${workHoursPage.content}">
                                <td th:text="${workHours.id}">1</td>
                                <td>
                                    <span th:class="${'badge ' +
                                        (workHours.businessType == '项目' ? 'bg-primary' :
                                        (workHours.businessType == '任务' ? 'bg-success' :
                                        (workHours.businessType == '培训' ? 'bg-info' :
                                        (workHours.businessType == '会议' ? 'bg-warning' : 'bg-secondary'))))}"
                                        th:text="${workHours.businessType}">项目</span>
                                </td>
                                <td>
                                    <a th:if="${workHours.businessType == '项目'}" 
                                       th:href="@{/projects/{id}(id=${workHours.businessId})}" 
                                       class="text-decoration-underline fw-bold text-primary"
                                       th:text="${workHours.businessId}"
                                       title="查看项目详情">1001</a>
                                    <a th:if="${workHours.businessType == '任务'}" 
                                       th:href="@{/tasks/{id}(id=${workHours.businessId})}" 
                                       class="text-decoration-underline fw-bold text-success"
                                       th:text="${workHours.businessId}"
                                       title="查看任务详情">1001</a>
                                    <span th:if="${workHours.businessType != '项目' && workHours.businessType != '任务'}" 
                                          th:text="${workHours.businessId}"
                                          class="text-muted">1001</span>
                                </td>
                                <td>
                                    <span class="fw-bold text-primary" th:text="${#numbers.formatDecimal(workHours.hoursInventory, 1, 1)} + 'h'">8.0h</span>
                                </td>
                                <td>
                                    <span th:class="${workHours.hoursChange >= 0 ? 'text-success' : 'text-danger'}"
                                          th:text="${workHours.hoursChange >= 0 ? '+' : ''} + ${#numbers.formatDecimal(workHours.hoursChange, 1, 1)} + 'h'">+2.0h</span>
                                </td>                                <td th:text="${workHours.reason}" class="text-truncate" style="max-width: 200px;" th:title="${workHours.reason}">工作原因</td>
                                <td th:text="${workHours.responsiblePerson ?: '-'}" class="text-truncate" style="max-width: 100px;" th:title="${workHours.responsiblePerson}">责任人</td>
                                <td th:text="${workHours.creator}" class="text-truncate" style="max-width: 100px;" th:title="${workHours.creator}">创建人</td>
                                <td th:text="${workHours.remark}" class="text-truncate" style="max-width: 150px;" th:title="${workHours.remark}">备注</td>
                                <td>
                                    <span th:if="${workHours.bonus != null and workHours.bonus > 0}" 
                                          class="fw-bold text-success" 
                                          th:text="'¥' + ${#numbers.formatDecimal(workHours.bonus, 1, 2)}">¥100.00</span>
                                    <span th:if="${workHours.bonus == null or workHours.bonus == 0}" 
                                          class="text-muted">-</span>
                                </td>
                                <td th:text="${workHours.createdTime}" style="white-space: nowrap;">2025-01-01 09:00:00</td>
                                <td sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
                                    <div class="btn-group btn-group-sm">
                                        <a th:href="@{/work-hours/{id}(id=${workHours.id})}" class="btn btn-outline-primary" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a th:href="@{/work-hours/{id}/edit(id=${workHours.id})}" class="btn btn-outline-warning" title="编辑" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" title="删除" 
                                                sec:authorize="hasRole('ADMIN')"
                                                onclick="confirmDelete(this)" 
                                                th:data-url="@{/work-hours/delete/{id}(id=${workHours.id})}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="card-footer" th:if="${workHoursPage != null && workHoursPage.totalPages > 0}">
                    <div th:replace="~{fragments/pagination :: pagination(${workHoursPage}, @{/work-hours})}"></div>
                </div>
                </div>
            </div>
        </div>
    </div>    <script id="customScript">
        // 删除确认功能
        function confirmDelete(button) {
            const url = button.getAttribute('data-url');
            if (confirm('确定要删除这条工期记录吗？此操作不可恢复。')) {
                // 创建隐藏表单进行POST删除
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = url;
                
                // 添加CSRF令牌
                const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
                
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 修复所有责任人数据功能
        function fixResponsiblePersonData() {
            const fixResponsibleBtn = document.getElementById('fixResponsibleBtn');
            
            if (!confirm('确定要修复所有责任人数据吗？\n\n此操作将：\n1. 查找所有工期记录\n2. 根据业务类型和业务ID找到对应的项目或任务\n3. 更新工期记录中的责任人字段\n\n请确认是否继续？')) {
                return;
            }
            
            // 禁用按钮，显示加载状态
            fixResponsibleBtn.disabled = true;
            fixResponsibleBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>正在修复...';
            
            // 获取CSRF令牌
            const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
            
            // 发送AJAX请求
            fetch('/work-hours/fix-responsible-person', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [csrfHeader]: csrfToken
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                fixResponsibleBtn.disabled = false;
                fixResponsibleBtn.innerHTML = '<i class="bi bi-person-check me-1"></i>修复所有责任人数据';
                
                if (data.success) {
                    alert(`修复完成！\n\n处理结果：\n- 总记录数：${data.totalRecords}\n- 成功修复数：${data.fixedRecords}\n- 失败记录数：${data.failedRecords}`);
                    // 刷新页面以显示更新的数据
                    window.location.reload();
                } else {
                    alert('修复失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                // 恢复按钮状态
                fixResponsibleBtn.disabled = false;
                fixResponsibleBtn.innerHTML = '<i class="bi bi-person-check me-1"></i>修复所有责任人数据';
                
                console.error('修复责任人数据时发生错误:', error);
                alert('修复失败：网络错误或服务器异常');
            });
        }

        // 修补工期记录功能
        function repairWorkHoursRecords() {
            const repairBtn = document.getElementById('repairBtn');
            
            if (!confirm('确定要执行修补工期记录操作吗？\n\n此操作将：\n1. 查找所有额定工期大于0的任务\n2. 检查是否存在对应的工期记录\n3. 为缺失的记录自动添加工期记录\n\n请确认是否继续？')) {
                return;
            }
              // 禁用按钮，显示加载状态
            repairBtn.disabled = true;
            repairBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>正在修补...';
            
            // 获取CSRF令牌
            const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
            
            // 发送AJAX请求
            fetch('/work-hours/repair', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [csrfHeader]: csrfToken
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                repairBtn.disabled = false;
                repairBtn.innerHTML = '<i class="bi bi-tools me-1"></i>修补工期记录';
                
                if (data.success) {
                    alert(`修补完成！\n\n处理结果：\n- 发现任务数：${data.totalTasks}\n- 已有记录数：${data.existingRecords}\n- 新增记录数：${data.createdRecords}`);
                    // 刷新页面以显示新增的记录
                    window.location.reload();
                } else {
                    alert('修补失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                // 恢复按钮状态
                repairBtn.disabled = false;
                repairBtn.innerHTML = '<i class="bi bi-tools me-1"></i>修补工期记录';
                
                console.error('修补工期记录时发生错误:', error);
                alert('修补失败：网络错误或服务器异常');
            });
        }

        // 责任人绩效分图表：从表格汇总并渲染
        (function() {
            function parseNumber(text) {
                if (!text) return 0;
                const cleaned = text.replace(/[¥,\s]/g, '').trim();
                const n = parseFloat(cleaned);
                return isNaN(n) ? 0 : n;
            }

            function collectChartData() {
                const tbody = document.querySelector('.table-responsive tbody');
                const map = new Map();
                if (!tbody) return { labels: [], data: [] };
                tbody.querySelectorAll('tr').forEach(tr => {
                    const tds = tr.querySelectorAll('td');
                    if (tds.length < 10) return;
                    let person = (tds[6]?.textContent || '').trim(); // 第7列：责任人
                    if (!person || person === '-') person = '未指定';
                    const bonusText = (tds[9]?.textContent || '').trim(); // 第10列：绩效分
                    const bonus = parseNumber(bonusText);
                    map.set(person, (map.get(person) || 0) + bonus);
                });
                const entries = Array.from(map.entries()).sort((a,b) => b[1] - a[1]);
                return {
                    labels: entries.map(e => e[0]),
                    data: entries.map(e => Number(e[1].toFixed(2)))
                };
            }

            let bonusChart;
            function renderBonusChart() {
                const canvas = document.getElementById('bonusByResponsibleChart');
                if (!canvas) return;
                const ctx = canvas.getContext('2d');
                const { labels, data } = collectChartData();
                if (bonusChart) { bonusChart.destroy(); bonusChart = null; }
                if (!labels.length) { ctx && ctx.clearRect(0, 0, canvas.width, canvas.height); return; }
                bonusChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels,
                        datasets: [{
                            label: '绩效分累计',
                            data,
                            backgroundColor: 'rgba(13,110,253,0.35)',
                            borderColor: 'rgba(13,110,253,1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y', // 横向柱状
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { beginAtZero: true, ticks: { callback: (v) => '¥' + v } }
                        },
                        plugins: {
                            legend: { display: false },
                            tooltip: { callbacks: { label: (ctx) => '¥' + ctx.parsed.x } }
                        }
                    }
                });
            }

            function ensureChartJsAndRender() {
                if (window.Chart) { renderBonusChart(); return; }
                const s = document.createElement('script');
                s.src = 'https://cdn.jsdelivr.net/npm/chart.js';
                s.onload = renderBonusChart;
                s.onerror = () => console.warn('Chart.js加载失败，无法渲染图表');
                document.head.appendChild(s);
            }

            function debounce(fn, delay = 200) {
                let t; return function(...args) { clearTimeout(t); t = setTimeout(() => fn.apply(this, args), delay); };
            }

            document.addEventListener('DOMContentLoaded', () => {
                ensureChartJsAndRender();
                const tbody = document.querySelector('.table-responsive tbody');
                if (tbody) {
                    const observer = new MutationObserver(debounce(ensureChartJsAndRender, 200));
                    observer.observe(tbody, { childList: true });
                }
            });

            // 提供手动刷新钩子（若未来有局部刷新场景）
            window.refreshBonusChart = ensureChartJsAndRender;
        })();
    </script>
</body>
</html>
