-- 重命名模板编码字段为模板标题，并取消唯一性约束

-- 对于MySQL，先删除唯一性约束
-- 注意：实际的约束名可能不同，请根据您的数据库进行调整
-- 以下是常见的删除唯一性约束的方式
-- MySQL: ALTER TABLE workflow_templates DROP INDEX uk_workflow_templates_template_code;
-- PostgreSQL: ALTER TABLE workflow_templates DROP CONSTRAINT uk_workflow_templates_template_code;
-- H2: ALTER TABLE workflow_templates DROP CONSTRAINT IF EXISTS uk_workflow_templates_template_code;

-- templateTitle
ALTER TABLE workflow_templates RENAME COLUMN templateCode TO templateTitle;

-- 添加注释
-- MySQL语法：ALTER TABLE workflow_templates MODIFY COLUMN templateTitle VARCHAR(50) NOT NULL COMMENT '模板标题，原模板编码';
-- PostgreSQL/H2语法：
COMMENT ON COLUMN workflow_templates.templateTitle IS '模板标题，原模板编码';