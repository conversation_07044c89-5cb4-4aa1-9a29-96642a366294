package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Entity
@Table(name = "user_activity_logs")
@Data
public class UserActivityLog {
    
    public enum ActivityType {
        LOGIN,           // 登录
        LOGOUT,          // 登出
        CREATE,          // 创建操作
        UPDATE,          // 更新操作
        DELETE,          // 删除操作
        VIEW,            // 查看操作
        DOWNLOAD,        // 下载操作
        EXPORT,          // 导出操作
        SETTINGS_CHANGE, // 设置更改（包括状态变更）
        OTHER            // 其他操作
    }
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private Long userId;
    
    @Column(nullable = false)
    private String username;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ActivityType activityType;
    
    @Column(nullable = false, length = 1000)
    private String description;
    
    @Column(length = 50)
    private String ipAddress;
    
    @Column(length = 100)
    private String entityType;  // 操作的实体类型，如 "Project", "Task" 等
    
    @Column
    private Long entityId;      // 操作的实体ID，如项目ID、任务ID等
    
    @Column(length = 50)
    private String accessType;  // 访问终端的操作系统类型，如 "移动端（iOS）" 或 "桌面端（Windows）"
    
    @Column(length = 50, nullable = false)
    private String createdDate;  // 格式化后的创建时间字符串
    
    @Transient
    private String formattedTimestamp; // 用于前端显示的格式化时间戳
    
    // 无参构造函数
    public UserActivityLog() {
        this.createdDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    // 带主要参数的构造函数
    public UserActivityLog(Long userId, String username, ActivityType activityType, String description) {
        this.userId = userId;
        this.username = username;
        this.activityType = activityType;
        this.description = description;
        this.createdDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    // 带所有参数的构造函数
    public UserActivityLog(Long userId, String username, ActivityType activityType, 
                          String description, String ipAddress,
                          String entityType, Long entityId) {
        this.userId = userId;
        this.username = username;
        this.activityType = activityType;
        this.description = description;
        this.ipAddress = ipAddress;
        this.entityType = entityType;
        this.entityId = entityId;
        this.createdDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    // 兼容性方法：将 timestamp 设置为 createdDate
    public void setTimestamp(LocalDateTime timestamp) {
        if (timestamp != null) {
            this.createdDate = timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
    }
    
    // 兼容性方法：从 createdDate 获取 timestamp
    public LocalDateTime getTimestamp() {
        if (this.createdDate != null && !this.createdDate.isEmpty()) {
            try {
                return LocalDateTime.parse(this.createdDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                // 如果解析失败，返回当前时间
                return LocalDateTime.now();
            }
        }
        return null;
    }
}