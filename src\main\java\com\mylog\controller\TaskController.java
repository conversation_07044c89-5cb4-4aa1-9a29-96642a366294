/**
     * 修复所有任务的绩效分（重新保存所有任务）
     */
    /**
     * 修复所有任务的绩效分（重新保存所有任务）
     */
package com.mylog.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.mylog.model.Message;
import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.model.SubTask;
import com.mylog.model.Submit2;
import com.mylog.model.user.User;
import com.mylog.service.MessageService;
import com.mylog.service.OptionsService;
import com.mylog.service.ProjectService;
import com.mylog.service.SubTaskService;
import com.mylog.service.Submit2Service;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.WorkHoursLogService;
import com.mylog.util.WeixinMessageUtil;

import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;

import java.util.stream.Collectors;

@Controller
@RequestMapping("/tasks")
public class TaskController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(TaskController.class);

    @Autowired
    private TaskService taskService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private OptionsService optionsService;

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityLogService activityLogService;
    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private WorkHoursLogService workHoursLogService;

    @Autowired
    private WeixinMessageUtil weixinMessageUtil;

    @PostConstruct
    public void init() {
    }

    @GetMapping("/create")
    public String createTaskForm(@RequestParam("projectId") Long projectId, Model model, HttpServletRequest request) {
        ProjectTask task = new ProjectTask();
        task.setProjectId(projectId);
        task.setStatus("未开始");

        // 设置默认负责人为当前登录用户
        String currentUsername = SecurityContextHolder.getContext().getAuthentication().getName();
        task.setResponsible(currentUsername);

        model.addAttribute("task", task);
        model.addAttribute("taskNames", optionsService.getTaskNames());
        model.addAttribute("personnel", optionsService.getPersonnel());
        model.addAttribute("taskTypes", optionsService.getTaskTypes()); // 获取项目信息并传递项目额定工期
        Double projectRatedDuration = 0.0;
        try {
            Optional<Project> projectOpt = projectService.findProjectById(projectId);
            if (projectOpt.isPresent()) {
                Project project = projectOpt.get();
                if (project.getRatedDurationDays() != null) {
                    projectRatedDuration = project.getRatedDurationDays().doubleValue();
                }
                // 设置项目信息到任务中，方便前端显示
                task.setProject(project);
            }
        } catch (Exception e) {
            logger.error("获取项目 {} 信息时出错: {}", projectId, e.getMessage());
        }
        model.addAttribute("projectRatedDuration", projectRatedDuration);

        // 获取来源页面URL
        String referer = request.getHeader("Referer");
        model.addAttribute("referer", referer);
        model.addAttribute("currentUrl", request.getRequestURL().toString() + "?projectId=" + projectId);

        return "tasks/form";
    }

    @GetMapping("/new")
    public String newTaskForm(@RequestParam("projectId") Long projectId, Model model, HttpServletRequest request) {
        // 直接调用createTaskForm方法处理
        return createTaskForm(projectId, model, request);
    }

    @PostMapping("/save")
    public String saveTask(ProjectTask task, RedirectAttributes redirectAttributes,
            @RequestParam(value = "referer", required = false) String referer) {
        String username = null;
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            username = authentication.getName();
        } catch (Exception e) {
            logger.error("获取当前用户信息时出错: {}", e.getMessage());
        }

        // 检查当前用户的权限
        boolean isAdmin = SecurityContextHolder.getContext().getAuthentication()
                .getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = SecurityContextHolder.getContext().getAuthentication()
                .getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));
        boolean isProjectResponsible = false;
        boolean isTaskResponsible = true; // 默认认为任务负责人是当前用户
        boolean isTaskCreator = false;

        if (task.getTaskId() != null) {
            Optional<ProjectTask> existingTaskOpt = taskService.findTaskById(task.getTaskId());
            if (existingTaskOpt.isPresent()) {
                ProjectTask existingTask = existingTaskOpt.get();
                isTaskResponsible = username != null && username.equals(existingTask.getResponsible());
                isTaskCreator = username != null && username.equals(existingTask.getCreatedBy());

                try {
                    if (existingTask.getProjectId() != null) {
                        Optional<Project> projectOpt = projectService.findProjectById(existingTask.getProjectId());
                        if (projectOpt.isPresent()) {
                            Project project = projectOpt.get();
                            isProjectResponsible = username != null && username.equals(project.getResponsible());
                        } else {
                            logger.warn("任务 {} 关联的项目 {} 不存在", existingTask.getTaskId(), existingTask.getProjectId());
                            redirectAttributes.addFlashAttribute("error", "任务关联的项目不存在,无法继续操作");
                            return "redirect:/tasks/" + existingTask.getTaskId();
                        }
                    }
                } catch (Exception e) {
                    logger.error("获取任务 {} 的项目信息时出错: {}", existingTask.getTaskId(), e.getMessage());
                    redirectAttributes.addFlashAttribute("error", "获取项目信息时出错: " + e.getMessage());
                    return "redirect:/tasks/" + existingTask.getTaskId();
                }
            }
        }
        // 1. 管理员可以编辑任何任务
        // 2. 经理可以编辑自己负责的任务或自己负责的项目中的任务
        // 3. 任务创建者可以编辑自己创建的任务 // 4. 邓利鹏用户可以编辑任何任务
        // 5. 普通用户不能编辑任何任务
        if (!isAdmin && !(isManager && (isProjectResponsible || isTaskResponsible))
                && !isTaskCreator && (username == null || !username.equals("邓利鹏"))) {
            redirectAttributes.addFlashAttribute("error", "您没有权限创建或修改任务");
            if (task.getTaskId() != null) {
                return "redirect:/tasks/" + task.getTaskId();
            } else {
                return referer != null && !referer.isEmpty() ? "redirect:" + referer : "redirect:/tasks";
            }
        }
        if (task.getProgress() == null) {
            task.setProgress(0);
        } // 检查是否是新建任务
        boolean isUpdate = task.getTaskId() != null;
        String oldResponsible = null;
        String oldStatus = null;
        BigDecimal oldRatedDurationDays = null;

        // 如果是更新，获取旧的责任人信息、状态和额定工期
        if (isUpdate) {
            logger.debug("更新现有任务: ID={}", task.getTaskId());
            Optional<ProjectTask> existingTaskOpt = taskService.findTaskById(task.getTaskId());
            if (existingTaskOpt.isPresent()) {
                ProjectTask existingTask = existingTaskOpt.get();
                oldResponsible = existingTask.getResponsible();
                oldStatus = existingTask.getStatus();
                oldRatedDurationDays = existingTask.getRatedDurationDays();

                // 如果提交的任务负责人为空，保留原有的负责人（防止表单禁用字段导致的null值）
                if (task.getResponsible() == null || task.getResponsible().trim().isEmpty()) {
                    logger.debug("任务负责人为空，保留原有负责人: {}", existingTask.getResponsible());
                    task.setResponsible(existingTask.getResponsible());
                }

                // 保留创建日期
                task.setCreatedDate(existingTask.getCreatedDate());

                // 保留创建者
                task.setCreatedBy(existingTask.getCreatedBy());

                // 保留最后评论日期和评论天数
                task.setLastCommentDate(existingTask.getLastCommentDate());
                task.setCommentDays(existingTask.getCommentDays());

                // 保留现有的日期值，防止互相清空
                if (task.getActualStartDate() == null && existingTask.getActualStartDate() != null) {
                    task.setActualStartDate(existingTask.getActualStartDate());
                    task.setActualStartDateTime(existingTask.getActualStartDateTime());
                    logger.debug("保留现有的实际开始日期: {}", task.getActualStartDate());
                }
                if (task.getActualEndDate() == null && existingTask.getActualEndDate() != null) {
                    task.setActualEndDate(existingTask.getActualEndDate());
                    task.setActualEndDateTime(existingTask.getActualEndDateTime());
                    logger.debug("保留现有的实际结束日期: {}", task.getActualEndDate());
                }

                // 检查状态是否从其他状态变为进行中
                if (!oldStatus.equals("进行中") && task.getStatus().equals("进行中")) {
                    // 设置实际开始日期为当前时间
                    LocalDateTime now = LocalDateTime.now();
                    task.setActualStartDateTime(now);
                }

                // 检查状态是否从其他状态变为已完成
                if (!oldStatus.equals("已暂停") && task.getStatus().equals("已完成")) {
                    // 设置实际结束日期为当前时间
                    LocalDateTime now = LocalDateTime.now();
                    task.setActualEndDateTime(now);

                    // 如果状态变为已完成，自动设置进度为100%
                    task.setProgress(100);

                }

                // 检查状态是否从其他状态变为已暂停
                if (!oldStatus.equals("已完成") && task.getStatus().equals("已暂停")) {
                    // 设置实际结束日期为当前时间
                    LocalDateTime now = LocalDateTime.now();
                    task.setActualEndDateTime(now);

                }

                calculateDuration(task);

                // 检查状态：变为"已完成"或者从"进行中"变为"已暂停"，则添加一条工期记录
                if ((task.getStatus().equals("已完成") || task.getStatus().equals("已暂停"))) {
                    try {
                        // 计算工期变化
                        Double daysChange = 0.0;
                        String reason = "";

                        if (task.getStatus().equals("已完成")) {
                            // 状态变为已完成，记录完成的工期
                            if (task.getDurationDays() != null) {
                                daysChange = task.getDurationDays().doubleValue();
                            }
                            reason = "任务状态从进行中变为已完成，记录实际工期";
                        } else if (task.getStatus().equals("已暂停")) {
                            // 状态变为已暂停，记录暂停前的工期
                            if (task.getDurationDays() != null) {
                                daysChange = task.getDurationDays().doubleValue();
                            }
                            reason = "任务状态从进行中变为已暂停，记录暂停前工期";
                        }
                        // 添加工期记录
                        Double daysRated = null;
                        if (task.getRatedDurationDays() != null) {
                            daysRated = task.getRatedDurationDays().doubleValue();
                        } else {
                            // 如果没有设置额定工期，使用0.0作为默认值
                            daysRated = 0.0;
                        }

                        String responsiblePerson = task.getResponsible() != null ? task.getResponsible() : "未知责任人";
                        // 添加工期记录
                        workHoursLogService.addWorkHours(
                                "任务",
                                task.getTaskId().intValue(),
                                daysChange,
                                daysRated,
                                reason,
                                username,
                                String.format("任务[%s]状态从[%s]变为[%s]", task.getTaskName(), oldStatus, task.getStatus()),
                                responsiblePerson);

                        logger.info("已为任务{}添加工期记录: 变化={}, 原因={}", task.getTaskId(), daysChange, reason);

                    } catch (Exception e) {
                        // 记录错误但不影响任务保存流程
                        logger.error("为任务{}添加工期记录时出错: {}", task.getTaskId(), e.getMessage(), e);
                    }
                }
            }
        } else {
            // 新任务，设置创建日期和评论天数
            LocalDateTime now = LocalDateTime.now();
            task.setCreatedDateTime(now);
            task.setCommentDays(-1.0); // 新建任务时设置评论天数为-1

            // 检查状态是否从其他状态变为进行中
            if (task.getStatus().equals("进行中")) {
                // 设置实际开始日期为当前时间

                task.setActualStartDateTime(now);
            }

            // 设置创建者，这里使用当前登录用户的用户名
            try {
                task.setCreatedBy(username);
            } catch (Exception e) {
                logger.error("设置任务创建者时出错: {}", e.getMessage());
                // 如果出错，设置一个默认值
                task.setCreatedBy("system");
            }

            // 如果是新任务且状态为"已完成"，自动设置进度为100%
            if ("已完成".equals(task.getStatus())) {
                task.setProgress(100);
            }
        } // 最后再次确认进度值不为null
        if (task.getProgress() == null) {
            task.setProgress(0);

         // 数据验证：责任人不能为空
        if (task.getResponsible() == null || task.getResponsible().trim().isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "任务责任人不能为空，请选择一个责任人");
            if (task.getTaskId() != null) {
                return "redirect:/tasks/" + task.getTaskId() + "/edit";
            } else {
                return referer != null && !referer.isEmpty() ? "redirect:" + referer : "redirect:/tasks";
            }
        }

       } // 数据验证：责任人为"未分配"和状态为"进行中"不允许同时存在
        if ("未分配".equals(task.getResponsible()) && "进行中".equals(task.getStatus())) {
            redirectAttributes.addFlashAttribute("error", "责任人为\"未分配\"时，任务状态不能设置为\"进行中\"");
            if (task.getTaskId() != null) {
                return "redirect:/tasks/" + task.getTaskId() + "/edit";
            } else {
                return referer != null && !referer.isEmpty() ? "redirect:" + referer : "redirect:/tasks";
            }
        }

        // 自动计算工期（天数）
        calculateDuration(task);

        try {
            // 保存任务数据
            ProjectTask savedTask = taskService.saveTask(task);
            logger.info("任务保存成功: ID={}", savedTask.getTaskId());
            logger.debug("保存后的实际开始日期: {}", savedTask.getActualStartDate());
            logger.debug("保存后的实际结束日期: {}", savedTask.getActualEndDate());
            logger.debug("保存后的工期（天数）: {}", savedTask.getDurationDays());
            logger.debug("保存后的进度: {}%", savedTask.getProgress());

            // 如果是新任务，尝试添加一条评论"本任务第一次创建"
            if (!isUpdate) {
                try {
                    SubTask commentTask = new SubTask();
                    commentTask.setTaskId(savedTask.getTaskId());
                    commentTask.setLogContent("本评论由系统在任务创建时自动添加");
                    commentTask.setCreatedDateTime(LocalDateTime.now());
                    commentTask.setCreatedBy(username != null ? username : "system");

                    // 保存评论
                    subTaskService.saveSubTask(commentTask);
                    logger.info("已为新任务添加首条评论");
                } catch (Exception e) {
                    // 记录错误但不影响任务保存流程
                    logger.error("为新任务添加首条评论时出错: {}", e.getMessage(), e);
                }
            }

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(username);
            if (currentUser.isPresent()) {
                String activityDescription = String.format("%s了任务 [%s]",
                        isUpdate ? "更新" : "创建",
                        savedTask.getTaskName());

                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        username,
                        activityDescription,
                        getClientIpAddress(),
                        "Task",
                        savedTask.getTaskId(),
                        getAccessType());

                // 如果是更新任务，且负责人发生变化，记录变更并添加评论
                if (isUpdate && oldResponsible != null && !oldResponsible.equals(task.getResponsible())) {
                    String changeDescription = String.format("任务 [%s] 的负责人从 [%s] 变更到 [%s]",
                            savedTask.getTaskName(),
                            oldResponsible,
                            task.getResponsible());

                    activityLogService.logSettingsChange(
                            currentUser.get().getUserId(),
                            username,
                            changeDescription,
                            getClientIpAddress(),
                            "Task",
                            savedTask.getTaskId(),
                            getAccessType());

                    // 添加责任人变更的自动评论
                    SubTask commentTask = new SubTask();
                    commentTask.setTaskId(savedTask.getTaskId());
                    commentTask.setLogContent(String.format("把责任人从%s变更为%s", oldResponsible, task.getResponsible()));
                    commentTask.setCreatedDateTime(LocalDateTime.now());
                    commentTask.setCreatedBy(username != null ? username : "system");

                    // 保存评论
                    subTaskService.saveSubTask(commentTask);
                    logger.info("已添加责任人变更评论");

                    // 向新负责人发送分配消息
                    sendTaskResponsibleChangeMessage(task.getResponsible(), savedTask,
                            task.getProject() != null ? task.getProject().getProjectName() : "", true);

                    // 向旧负责人发送取消消息
                    if (oldResponsible != null && !oldResponsible.isEmpty()) {
                        sendTaskResponsibleChangeMessage(oldResponsible, savedTask,
                                task.getProject() != null ? task.getProject().getProjectName() : "", false);
                    }
                } else if (!isUpdate && task.getResponsible() != null && !task.getResponsible().isEmpty()) {
                    // 新项目且指定了责任人
                    logger.info("新建任务，向责任人 {} 发送分配消息", task.getResponsible());
                    sendTaskResponsibleChangeMessage(task.getResponsible(), savedTask,
                            task.getProject() != null ? task.getProject().getProjectName() : "", true);
                }

                // 如果是更新任务，且状态发生变化，记录变更
                if (isUpdate && oldStatus != null && !oldStatus.equals(task.getStatus())) {
                    String changeDescription = String.format("任务 [%s] 的状态从 [%s] 变更到 [%s]",
                            savedTask.getTaskName(),
                            oldStatus,
                            task.getStatus());

                    activityLogService.logSettingsChange(
                            currentUser.get().getUserId(),
                            username,
                            changeDescription,
                            getClientIpAddress(),
                            "Task",
                            savedTask.getTaskId(),
                            getAccessType());

                    // 如果状态变为"进行中"，添加状态变更评论
                    if (true)// (task.getStatus().equals("进行中"))
                    {
                        SubTask commentTask = new SubTask();
                        commentTask.setTaskId(savedTask.getTaskId());
                        commentTask.setLogContent(changeDescription);
                        commentTask.setCreatedDateTime(LocalDateTime.now());
                        commentTask.setCreatedBy(username != null ? username : "system");

                        // 保存评论
                        subTaskService.saveSubTask(commentTask);
                        logger.info("已添加状态变更评论");
                    }
                }

                // 如果是更新任务，且额定工期（天）发生变化，记录变更
                if (isUpdate && hasRatedDurationChanged(oldRatedDurationDays, task.getRatedDurationDays())) {
                    String oldRatedDaysStr = oldRatedDurationDays != null
                            ? String.format("%.2f天", oldRatedDurationDays.doubleValue())
                            : "未设置";
                    String newRatedDaysStr = task.getRatedDurationDays() != null
                            ? String.format("%.2f天", task.getRatedDurationDays().doubleValue())
                            : "未设置";

                    String changeDescription = String.format("任务 [%s] 的额定工期从 [%s] 变更到 [%s]",
                            savedTask.getTaskName(),
                            oldRatedDaysStr,
                            newRatedDaysStr);

                    activityLogService.logSettingsChange(
                            currentUser.get().getUserId(),
                            username,
                            changeDescription,
                            getClientIpAddress(),
                            "Task",
                            savedTask.getTaskId(),
                            getAccessType());

                    // 记录额定工期变更到工期日志
                    try {
                        Double newRatedDuration = task.getRatedDurationDays() != null
                                ? task.getRatedDurationDays().doubleValue()
                                : 0.0;
                                
                                String responsiblePerson = task.getResponsible() != null ? task.getResponsible() : "未知责任人";

                        workHoursLogService.addWorkHours(
                                "任务",
                                savedTask.getTaskId().intValue(),
                                0.0, // 实际工期变化为0，因为这是额定工期的调整
                                newRatedDuration, // 新的额定工期
                                String.format("额定工期从%s变更为%s（手动调整）", oldRatedDaysStr, newRatedDaysStr),
                                username,
                                "额定工期手动调整",
                                responsiblePerson
                                );

                        logger.info("已记录额定工期变更工期日志: {} -> {}", oldRatedDaysStr, newRatedDaysStr);
                    } catch (Exception e) {
                        logger.error("记录额定工期变更工期日志时出错: {}", e.getMessage(), e);
                    }
                }
            }

            redirectAttributes.addFlashAttribute("message", "保存成功！");

            // 如果是编辑现有任务，重定向到任务详情页
            if (savedTask.getTaskId() != null) {
                return "redirect:/tasks/" + savedTask.getTaskId();
            }
        } catch (Exception e) {
            logger.error("保存任务失败: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", "保存失败：" + e.getMessage());
        }

        // 如果有来源页面，重定向回去，否则重定向到任务列表
        if (referer != null && !referer.isEmpty()) {
            return "redirect:" + referer;
        } else {
            return "redirect:/tasks";
        }
    }

    /**
     * 自动计算工期（天数）
     *
     * @param task 任务对象
     */
    private void calculateDuration(ProjectTask task) {
        try {
            LocalDateTime startDateTime = task.getActualStartDateTime();
            LocalDateTime endDateTime = task.getActualEndDateTime();

            if (startDateTime != null && endDateTime != null) {
                // 计算两个日期之间的天数差（支持小数）
                long days = java.time.temporal.ChronoUnit.DAYS.between(startDateTime, endDateTime);
                long hours = java.time.temporal.ChronoUnit.HOURS.between(startDateTime, endDateTime) % 24;
                long minutes = java.time.temporal.ChronoUnit.MINUTES.between(startDateTime, endDateTime) % 60;

                // 计算小数部分（小时和分钟转换为天的小数）
                double hoursFraction = hours / 24.0;
                double minutesFraction = minutes / (24.0 * 60);
                double totalDays = days + hoursFraction + minutesFraction;

                // 创建BigDecimal并保留两位小数
                BigDecimal durationDays = new BigDecimal(totalDays).setScale(2, RoundingMode.HALF_UP);

                // 确保工期至少为0
                task.setDurationDays(durationDays.max(BigDecimal.ZERO));
                logger.debug("自动计算工期: {} 天", task.getDurationDays());
            } else {
                // 如果开始日期或结束日期为空，则工期设为null
                task.setDurationDays(null);
                logger.debug("无法计算工期，开始日期或结束日期为空");
            }
        } catch (Exception e) {
            // 处理计算过程中可能出现的异常
            logger.error("计算工期时出错: {}", e.getMessage(), e);
            task.setDurationDays(null);
        }
    }

    /**
     * 检查额定工期是否发生变化
     * 专门处理BigDecimal的比较，避免NULL和ZERO的比较问题
     *
     * @param oldValue 旧的额定工期值
     * @param newValue 新的额定工期值
     * @return true如果发生了实际变化，false如果没有变化
     */
    private boolean hasRatedDurationChanged(BigDecimal oldValue, BigDecimal newValue) {
        // 如果两个值都为null，认为没有变化
        if (oldValue == null && newValue == null) {
            return false;
        }
        // 如果一个为null，另一个不为null，认为发生变化
        if (oldValue == null || newValue == null) {
            // 但是要特殊处理：如果其中一个是null，另一个是0，认为没有实际变化
            BigDecimal nonNullValue = oldValue != null ? oldValue : newValue;
            if (nonNullValue != null) {
                return nonNullValue.compareTo(BigDecimal.ZERO) != 0;
            }
            return false;
        }

        // 两个都不为null时，使用compareTo进行精确比较
        // compareTo方法会正确处理精度问题
        return oldValue.compareTo(newValue) != 0;
    }

    /**
     * 发送任务责任人变更消息
     *
     * @param receiver    接收者
     * @param task        任务
     * @param projectName 项目名称
     * @param isAssigned  是否分配任务
     */
    private void sendTaskResponsibleChangeMessage(String receiver, ProjectTask task, String projectName,
            boolean isAssigned) {
        if (receiver == null || receiver.isEmpty()) {
            return;
        }

        try {
            Message message = new Message();
            message.setReceiver(receiver);

            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取项目信息
            String actualProjectName = projectName;
            String projectCode = "";

            if (task.getProjectId() != null) {
                try {
                    Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                    if (projectOpt.isPresent()) {
                        Project project = projectOpt.get();
                        actualProjectName = project.getProjectName();
                        projectCode = project.getProjectCode() != null ? project.getProjectCode() : "";
                    }
                } catch (Exception e) {
                    logger.error("获取项目信息时出错: {}", e.getMessage());
                }
            }

            // 如果项目名称仍然为空，设置一个默认值
            if (actualProjectName == null || actualProjectName.isEmpty()) {
                actualProjectName = "未知项目";
            }

            // 设置消息标题，无论接收者是谁
            if (isAssigned) {
                message.setMessageTitle("任务责任人分配");
                message.setMessageContent(
                        "你被" + currentUsername + "分配为任务 [" + task.getTaskName() + "] 的负责人。该任务属于项目 [" +
                                actualProjectName + "]"
                                + (projectCode.isEmpty() ? "" : " (编号: " + projectCode + ")") + "。");

                // 如果接收者不是"未分配"且不是当前用户，发送企业微信消息
                if (!receiver.contains("未分配") && !receiver.contains(currentUsername)) {
                    // 使用StringBuilder构建内容，更高效
                    StringBuilder contentBuilder = new StringBuilder();

                    // 查找接收者的weixinID
                    String weixinID = "";
                    Optional<User> receiverUser = userService.findUserByUsername(receiver);
                    if (receiverUser.isPresent() && receiverUser.get().getWeixinID() != null
                            && !receiverUser.get().getWeixinID().isEmpty()) {
                        weixinID = receiverUser.get().getWeixinID();
                        logger.debug("找到用户 {} 的微信ID: {}", receiver, weixinID);
                    } else {
                        logger.warn("未找到用户 {} 的微信ID，使用用户名代替", receiver);
                        weixinID = receiver; // 如果没有weixinID，则使用用户名
                    }

                    // 添加到Markdown表格中
                    contentBuilder.append(" <@").append(weixinID)
                            .append(">，你被" + currentUsername + "分配为任务 [" + task.getTaskName()
                                    + "](https://prj.cpolar.cn/tasks/" + task.getTaskId() + ") 的负责人。该任务属于项目 [" +
                                    actualProjectName + "]"
                                    + (projectCode.isEmpty() ? "" : " (编号: " + projectCode + ")") + "。");

                    String content = contentBuilder.toString();

                    List<String> mentionlist = new ArrayList<>();
                    mentionlist.add(receiver);
                    weixinMessageUtil.sendWeixinMessage("任务分配", content, mentionlist);
                }
            } else {
                message.setMessageTitle("任务责任人取消");
                message.setMessageContent(
                        "您被" + currentUsername + "取消为任务 [" + task.getTaskName() + "] 的负责人。该任务属于项目 [" +
                                actualProjectName + "]"
                                + (projectCode.isEmpty() ? "" : " (编号: " + projectCode + ")") + "。");
            }

            message.setRelatedType("Task");
            message.setRelatedId(task.getTaskId());
            message.setRead(false);

            // 设置创建日期
            LocalDateTime now = LocalDateTime.now();
            message.setCreatedDate(now);

            messageService.saveMessage(message);
        } catch (Exception e) {
            logger.error("发送任务责任人变更消息时出错: {}", e.getMessage(), e);
        }
    }

    @GetMapping("/{id}")
    public String viewTask(@PathVariable("id") Long id, Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "0") int submitPage,
            @RequestParam(defaultValue = "5") int submitSize) {

        logger.info("查看任务详情: ID = {}", id);

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 设置用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
            });

            // 获取任务详情
            Optional<ProjectTask> taskOpt = taskService.findTaskById(id);
            if (!taskOpt.isPresent()) {
                throw new EntityNotFoundException("找不到ID为 " + id + " 的任务");
            }

            ProjectTask task = taskOpt.get();

            // 加载项目信息，增加防错处理
            if (task.getProjectId() != null) {
                try {
                    Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                    if (projectOpt.isPresent()) {
                        Project project = projectOpt.get();
                        // 确保项目的关键属性不为null
                        if (project.getProjectName() == null) {
                            project.setProjectName("未命名项目");
                        }
                        if (project.getVisionType() == null) {
                            project.setVisionType("默认");
                        }
                        task.setProject(project);
                        logger.debug("已加载项目信息: ID={}, 名称={}, 视觉类型={}", 
                            project.getProjectId(), project.getProjectName(), project.getVisionType());
                    } else {
                        logger.warn("任务 {} 关联的项目 {} 不存在", task.getTaskId(), task.getProjectId());
                        // 创建一个空的项目对象以防止空指针
                        Project emptyProject = new Project();
                        emptyProject.setProjectId(task.getProjectId());
                        emptyProject.setProjectName("项目不存在");
                        emptyProject.setVisionType("默认");
                        task.setProject(emptyProject);
                    }
                } catch (Exception e) {
                    logger.error("加载任务 {} 的项目信息时出错: {}", task.getTaskId(), e.getMessage());
                    // 出错时也创建一个空的项目对象
                    Project emptyProject = new Project();
                    emptyProject.setProjectId(task.getProjectId());
                    emptyProject.setProjectName("加载失败");
                    emptyProject.setVisionType("默认");
                    task.setProject(emptyProject);
                }
            } else {
                logger.warn("任务 {} 没有关联项目", task.getTaskId());
                // 如果没有关联项目，也设置一个空项目对象
                Project emptyProject = new Project();
                emptyProject.setProjectName("无项目");
                emptyProject.setVisionType("默认");
                task.setProject(emptyProject);
            }

            // 查询任务的评论列表
            PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "sequenceNumber"));
            Page<SubTask> subTaskPage = subTaskService.findSubTasksByTaskId(id, pageRequest); // 查询任务的提交记录列表
            PageRequest submitPageRequest = PageRequest.of(submitPage, submitSize,
                    Sort.by(Sort.Direction.DESC, "submitDate"));
            Page<Submit2> submit2Page = submit2Service.findSubmitsByTaskId(id, submitPageRequest);

            // 查询任务相关的工期记录列表
            List<com.mylog.model.WorkHoursLog> workHoursList = workHoursLogService
                    .findWorkHoursLogsByBusinessTypeAndBusinessId("任务", id.intValue());

            // 添加到模型
            model.addAttribute("task", task);
            model.addAttribute("subTaskPage", subTaskPage);
            model.addAttribute("submit2Page", submit2Page);
            model.addAttribute("workHoursList", workHoursList);

            // 添加分页参数到模型，用于分页URL构建
            model.addAttribute("page", page);
            model.addAttribute("size", size);
            model.addAttribute("submitPage", submitPage);
            model.addAttribute("submitSize", submitSize);

            // 设置活动菜单
            String previousActiveMenu = "mytasks";

            // 确定任务来源
            Optional<ProjectTask> taskOptional = taskService.findTaskById(id);
            if (taskOptional.isPresent()) {
                ProjectTask taskObj = taskOptional.get();

                // 根据任务类型确定来源菜单
                if (taskObj.getType() != null && taskObj.getType().contains("专项")) {
                    previousActiveMenu = "specialtasks";
                } else if (taskObj.getRisk() != null && taskObj.getRisk().equals("高")) {
                    previousActiveMenu = "difficulttasks";
                } else {
                    previousActiveMenu = "mytasks";
                }
            }

            model.addAttribute("previousActiveMenu", previousActiveMenu);


            // 业务逻辑，举例：前缀为数字且某些条件下强制审批
            boolean isForceApprovalNeeded = false;
           
            // 获取project对象,增加防错处理
            String visionType = null;
            try {
                if (task.getProject() != null) {
                    visionType = task.getProject().getVisionType();
                    logger.debug("成功获取项目视觉类型: {}", visionType);
                } else {
                    logger.warn("任务 {} 无关联项目", task.getTaskId());
                }
            } catch (Exception e) {
                logger.error("获取项目视觉类型时出错: {}", e.getMessage());
            }
            
            Double totalRatioForPrefix = 0.0;   //无奖金
            //如果task.getTaskName()名称前两个字符不为数字，即非订单任务，则归类于有奖金任务

            if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                String prefix = task.getTaskName().substring(0, 2);
                if (!prefix.matches("\\d+")) {  // 非数字前缀有奖金
                    totalRatioForPrefix = 1.0;    
                } else {     // 数字前缀，进一步查询配置是否有奖金
                    totalRatioForPrefix = optionsService.getTaskDurationRatio("任务名称", task.getTaskName(), visionType);
                }
            }

            if (totalRatioForPrefix != null && totalRatioForPrefix > 0) {  // 有奖金任务
                isForceApprovalNeeded=true; // 如果有奖金任务，则强制审批
            } else {
                isForceApprovalNeeded = false; // 如果没有奖金任务，则不强制审批
                // 如果没有奖金任务，则不强制审批,任务类型为“培训”的除外
                if (task.getType() != null && task.getType().contains("培训")) {
                    isForceApprovalNeeded = true; // 培训任务强制审批
                }
            }


            model.addAttribute("isForceApprovalNeeded", isForceApprovalNeeded);

            return "tasks/view";
        } catch (EntityNotFoundException e) {
            logger.error("找不到任务: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("查看任务详情时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping("/{id}/edit")
    public String editTaskForm(@PathVariable("id") Long id, Model model, HttpServletRequest request,
            RedirectAttributes redirectAttributes) {
        Optional<ProjectTask> taskOpt = taskService.findTaskById(id);
        if (!taskOpt.isPresent()) {
            return "redirect:/projects";
        }

        ProjectTask task = taskOpt.get();

        // 加载项目信息
        if (task.getProjectId() != null) {
            try {
                Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                if (projectOpt.isPresent()) {
                    Project project = projectOpt.get();
                    // 确保项目名称不为空
                    if (project.getProjectName() == null) {
                        project.setProjectName("未命名项目");
                    }
                    task.setProject(project);
                    logger.debug("已加载项目信息: {}", project.getProjectName());
                } else {
                    // 项目不存在，设置project为null
                    logger.warn("项目ID {} 已不存在", task.getProjectId());
                    task.setProject(null);
                }
            } catch (Exception e) {
                logger.error("加载项目 {} 信息时出错: {}", task.getProjectId(), e.getMessage());
                // 防止空指针异常，创建一个空的项目对象
                Project emptyProject = new Project();
                emptyProject.setProjectId(task.getProjectId());
                emptyProject.setProjectName("未命名项目");
                task.setProject(emptyProject);
            }
        }

        // 获取当前用户信息和权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        boolean isTaskResponsible = currentUsername.equals(task.getResponsible());
        boolean isProjectResponsible = task.getProject() != null
                && currentUsername.equals(task.getProject().getResponsible());
        boolean isTaskCreator = currentUsername.equals(task.getCreatedBy()); // 权限检查：
        // 1. 管理员可以编辑任何任务
        // 2. 经理可以编辑自己负责的任务或自己负责的项目中的任务
        // 3. 任务创建者可以编辑自己创建的任务
        // 4. 邓利鹏用户可以编辑任何任务
        // 5. 普通用户不能编辑任何任务
        if (!isAdmin && !(isManager && (isProjectResponsible || isTaskResponsible)) && !isTaskCreator
                && !currentUsername.equals("邓利鹏")) {
            redirectAttributes.addFlashAttribute("error", "您没有权限编辑此任务");
            return "redirect:/tasks/" + id;
        }

        // 检查任务是否正在审批中，如果是则不允许编辑
        if (!taskService.isTaskEditable(id)) {
            redirectAttributes.addFlashAttribute("error", "该任务正在审批流程中，暂时无法编辑");
            return "redirect:/tasks/" + id;
        }

        // 确保任务的进度信息正确

        model.addAttribute("task", task);
        model.addAttribute("personnel", optionsService.getPersonnel());
        model.addAttribute("taskNames", optionsService.getTaskNames());
        model.addAttribute("taskTypes", optionsService.getTaskTypes()); // 只传递项目的额定工期值，而不是整个project对象（安全性和效率考虑）
        Double projectRatedDuration = 0.0;
        if (task.getProject() != null && task.getProject().getRatedDurationDays() != null) {
            projectRatedDuration = task.getProject().getRatedDurationDays().doubleValue();
        }
        model.addAttribute("projectRatedDuration", projectRatedDuration);

        // 获取来源页面URL
        String referer = request.getHeader("Referer");
        model.addAttribute("referer", referer);

        // 分析referer来确定前一个页面的activeMenu
        String previousActiveMenu = "mytasks"; // 默认值
        if (referer != null) {
            if (referer.contains("/tasks/difficult")) {
                previousActiveMenu = "difficulttasks";
            } else if (referer.contains("/tasks/special")) {
                previousActiveMenu = "specialtasks";
            } else if (referer.contains("/tasks/my-tasks")) {
                previousActiveMenu = "mytasks";
            } else if (referer.contains("/projects")) {
                previousActiveMenu = "projects";
            }
        }
        model.addAttribute("previousActiveMenu", previousActiveMenu);

        // 添加当前页面URL到模型中，用于前端处理历史记录
        model.addAttribute("currentUrl", request.getRequestURL().toString());

        return "tasks/form";
    }

    @PostMapping("/delete")
    public String deleteTask(@RequestParam("taskId") Long id, RedirectAttributes redirectAttributes) {
        logger.info("尝试删除任务ID: {}", id);

        // 获取当前用户信息和权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 获取任务信息
        Optional<ProjectTask> taskOpt = taskService.findTaskById(id);
        if (!taskOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "找不到指定的任务");
            return "redirect:/tasks/order-tasks";
        }

        ProjectTask task = taskOpt.get();
        boolean isTaskResponsible = currentUsername.equals(task.getResponsible());
        boolean isTaskCreator = currentUsername.equals(task.getCreatedBy());

        // 获取项目信息
        Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
        boolean isProjectResponsible = false;
        Project project = null;

        if (projectOpt.isPresent()) {
            project = projectOpt.get();
            isProjectResponsible = currentUsername.equals(project.getResponsible());

            // 检查项目是否已归档，如果已归档，则不允许删除任务
            if (project.getArchive() != null && project.getArchive() == 1) {
                redirectAttributes.addFlashAttribute("error", "已归档项目的任务不能删除");
                return "redirect:/tasks/" + id;
            }
        } // 权限检查：
        // 1. 管理员可以删除任何任务
        // 2. 经理可以删除自己负责的项目中的任务或自己负责的任务
        // 3. 任务创建者可以删除自己创建的任务
        // 4. 普通用户不能删除任何任务
        if (!isAdmin && !(isManager && (isProjectResponsible || isTaskResponsible)) && !isTaskCreator) {
            redirectAttributes.addFlashAttribute("error", "您没有权限删除此任务");
            return "redirect:/tasks/" + id;
        }
        // 检查任务是否正在审批中，如果是则不允许删除
        if (!taskService.isTaskEditable(id)) {
            redirectAttributes.addFlashAttribute("error", "该任务正在审批流程中，暂时无法删除");
            return "redirect:/tasks/" + id;
        }

        // 删除任务前获取项目ID用于返回
        Long projectId = task.getProjectId();

        try {
            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                String ipAddress = getClientIpAddress();
                // 增加项目名称
                String description = "删除了任务 " + task.getTaskName() + " 所属项目 " + task.getProject().getProjectName();

                activityLogService.logDelete(
                        user.getUserId(),
                        currentUsername,
                        description,
                        ipAddress,
                        "Task",
                        id,
                        getAccessType());

                logger.info("用户 {} {} ID: {}", currentUsername, description, id);
            }

            // 执行删除操作
            taskService.deleteTask(id);
            redirectAttributes.addFlashAttribute("message", "任务删除成功");

        } catch (Exception e) {
            logger.error("删除任务时出错: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", "删除任务时出错: " + e.getMessage());
            return "redirect:/tasks/" + id;
        }

        // 如果来自项目页面，返回到项目详情页
        return "redirect:/projects/" + projectId;
    }

    @GetMapping({ "/my-tasks", "/order-tasks" })
    public String myTasks(Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        logger.info("加载我的订单任务列表");

        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            });

            // 创建分页请求，不指定排序，排序逻辑在service层实现
            PageRequest pageRequest = PageRequest.of(page, size);

            // 获取当前用户的订单类型任务
            Page<ProjectTask> taskPage = taskService.findOrderTasksByResponsible(currentUsername, pageRequest);

            // 为每个任务加载项目信息并确保进度信息正确
            for (ProjectTask task : taskPage.getContent()) {
                // 加载项目信息
                if (task.getProjectId() != null) {
                    try {
                        Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                        if (projectOpt.isPresent()) {
                            Project project = projectOpt.get();
                            task.setProject(project);
                        } else {
                            // 项目不存在，设置project为null，前端将显示"项目不存在"
                            logger.warn("任务引用的项目ID {} 不存在", task.getProjectId());
                            task.setProject(null);
                        }
                    } catch (EntityNotFoundException e) {
                        // 显式捕获EntityNotFoundException
                        logger.warn("项目ID {} 不存在: {}", task.getProjectId(), e.getMessage());
                        task.setProject(null);
                    } catch (Exception e) {
                        // 捕获其他可能的异常
                        logger.error("加载项目 {} 时出错: {}", task.getProjectId(), e.getMessage());
                        task.setProject(null);
                    }
                }

                // 确保任务的进度信息正确
                if (task.getProgress() == null) {
                    task.setProgress(0);
                }
            }

            // 计算所有任务的评论天数
            taskService.calculateCommentDays(taskPage.getContent());
            logger.info("已计算我的订单任务中 {} 个任务的评论天数", taskPage.getContent().size());

            // 获取当前用户进行中的订单任务数量
            Long inProgressCount = taskService.countTasksByResponsibleAndStatus(currentUsername, "进行中");
            model.addAttribute("inProgressTaskCount", inProgressCount);

            // 添加视觉类型列表
            model.addAttribute("visionTypes", optionsService.getVisionTypes());

            // 添加人员列表
            model.addAttribute("personnel", optionsService.getPersonnel());

            // 添加到模型
            model.addAttribute("taskPage", taskPage);
            model.addAttribute("activeMenu", "mytasks");

            // 设置默认主题（如果用户没有主题设置）
            if (!model.containsAttribute("userTheme")) {
                model.addAttribute("userTheme", "theme-light");
            }

            return "tasks/my-tasks";

        } catch (Exception e) {
            logger.error("加载任务列表时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 重定向到高级搜索
     */
    @GetMapping("/search")
    public String redirectToAdvancedSearch(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Model model) {

        // 如果有关键词，使用简单搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            logger.info("用户 {} 搜索任务: {}", keyword);

            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            });

            // 创建分页请求，不指定排序
            PageRequest pageRequest = PageRequest.of(page, Math.min(size, 20));

            // 搜索任务
            Page<ProjectTask> taskPage = taskService.searchTasks(keyword, pageRequest);

            logger.info("搜索结果: {} 个任务", taskPage.getTotalElements()); // 为每个任务加载项目信息并确保进度信息正确
            for (ProjectTask task : taskPage.getContent()) {
                Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                projectOpt.ifPresent(project -> task.setProject(project));

                // 确保任务的进度信息正确
                if (task.getProgress() == null) {
                    task.setProgress(0);
                }
            }

            // 计算所有任务的评论天数
            taskService.calculateCommentDays(taskPage.getContent());
            logger.info("已计算搜索结果中 {} 个任务的评论天数", taskPage.getContent().size());

            // 获取当前用户进行中任务数量
            Long inProgressCount = taskService.countTasksByResponsibleAndStatus(currentUsername, "进行中");
            model.addAttribute("inProgressTaskCount", inProgressCount);

            // 添加到模型
            model.addAttribute("taskPage", taskPage);
            model.addAttribute("keyword", keyword);
            model.addAttribute("activeMenu", "mytasks");

            // 设置默认主题（如果用户没有主题设置）
            if (!model.containsAttribute("userTheme")) {
                model.addAttribute("userTheme", "theme-light");
            }

            return "tasks/my-tasks";
        } else {
            // 如果没有关键词，重定向到我的任务列表
            return "redirect:/tasks/order-tasks?page=" + page + "&size=" + size;
        }
    }

    /**
     * 高级搜索任务
     *
     * @param fieldNames 字段名称列表
     * @param returnTo   返回位置
     * @param request    HTTP请求对象，用于获取动态参数
     * @param page       页码
     * @param size       每页大小
     * @param model      模型
     * @return 任务列表页面
     */
    @GetMapping("/advanced-search")
    public String advancedSearchTasks(
            @RequestParam(required = false) List<String> fieldNames,
            @RequestParam(required = false, defaultValue = "tasks") String returnTo,
            @RequestParam(required = false) String originalSource,
            @RequestParam(required = false) String _controller,
            HttpServletRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Model model) {

        // 检查控制器标识
        if (_controller != null) {
            logger.info("指定的控制器: {}", _controller);
            if (!"TaskController".equals(_controller)) {
                logger.error("错误: 请求发送到了错误的控制器 期望 TaskController 但收到{}", _controller);
            }
        }

        // 详细记录请求信息
        logger.info("===== 高级搜索请求开始 =====");
        logger.info("请求URI: {}", request.getRequestURI());
        logger.info("完整URL: {}",
                request.getRequestURL() + (request.getQueryString() != null ? "?" + request.getQueryString() : ""));
        logger.info("请求方法: {}", request.getMethod());
        logger.info("返回位置参数: {}", returnTo);
        logger.info("原始来源参数: {}", originalSource);
        logger.info("页码: {}, 每页大小: {}", page, size);
        logger.info("字段名称列表: {}", fieldNames);

        // 记录所有请求参数
        logger.info("所有请求参数:");
        request.getParameterMap().forEach((key, values) -> {
            StringBuilder valueStr = new StringBuilder();
            for (String value : values) {
                valueStr.append(value).append(", ");
            }
            logger.info("  {} = {}", key, valueStr);
        });

        // 记录请求头信息
        logger.info("请求头信息:");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            logger.info("  {} = {}", headerName, request.getHeader(headerName));
        }

        // 记录返回位置
        logger.info("高级搜索应该返回: {}", returnTo);

        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        // 记录请求来源
        String referer = request.getHeader("Referer");
        logger.info("高级搜索请求来源: {}", referer);

        // 获取用户主题
        userService.findUserByUsername(currentUsername).ifPresent(user -> {
            String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
            model.addAttribute("userTheme", "theme-" + theme);
            logger.info("用户主题: {}", theme);
        });
        boolean isFromManagement = false;
        String viewName = "tasks/my-tasks"; // 默认视图

        // 优先检查originalSource参数（用于保持固定的来源标识）
        if ("dashboard".equals(originalSource)) {
            isFromManagement = true;
            viewName = "tasks/management-tasks";
            logger.info("检测到来自Dashboard页面的originalSource参数");
        }
        // 检查referer是否含有management或来自dashboard页面
        else if (referer != null && (referer.contains("/tasks/management") || referer.contains("/dashboard"))) {
            isFromManagement = true;
            viewName = "tasks/management-tasks";
            logger.info("检测到来自任务管理页面或Dashboard页面的请求");
        }

        // 检查返回位置参数
        if ("management".equals(returnTo)) {
            isFromManagement = true;
            viewName = "tasks/management-tasks";
            logger.info("返回位置参数指定为管理页面");
        } else if ("my-tasks".equals(returnTo) || "order-tasks".equals(returnTo)) {
            isFromManagement = false;
            viewName = "tasks/my-tasks";
            logger.info("返回位置参数指定为我的订单任务页面");
        }

        // 如果没有指定字段，则返回相应视图
        if (fieldNames == null || fieldNames.isEmpty()) {
            logger.info("无搜索字段，返回到{}页面", isFromManagement ? "任务管理" : "我的任务");
            return isFromManagement ? "redirect:/tasks/management" : "redirect:/tasks/order-tasks";
        }

        // 创建查询条件映射
        Map<String, Object> searchCriteria = new HashMap<>();
        for (String fieldName : fieldNames) { // 处理特殊的时间范围条件
            if (fieldName.equals("createdDate")) {
                String startDate = request.getParameter("field_createdDate_start");
                String endDate = request.getParameter("field_createdDate_end");

                logger.info("处理创建时间条件 - 开始日期: {}", startDate);
                logger.info("处理创建时间条件 - 结束日期: {}", endDate);

                if (startDate != null && !startDate.isEmpty()) {
                    searchCriteria.put("createdDate_start", startDate);
                }
                if (endDate != null && !endDate.isEmpty()) {
                    searchCriteria.put("createdDate_end", endDate);
                }
            } // 处理结束时间范围条件
            else if (fieldName.equals("actualEndDate")) {
                String startDate = request.getParameter("field_actualEndDate_start");
                String endDate = request.getParameter("field_actualEndDate_end");

                logger.info("处理结束时间条件 - 开始日期: {}", startDate);
                logger.info("处理结束时间条件 - 结束日期: {}", endDate);

                if (startDate != null && !startDate.isEmpty()) {
                    searchCriteria.put("actualEndDate_start", startDate);
                }
                if (endDate != null && !endDate.isEmpty()) {
                    searchCriteria.put("actualEndDate_end", endDate);
                }
            }
            // 处理进度范围
            else if (fieldName.equals("progress")) {
                String minProgress = request.getParameter("field_progress_min");
                String maxProgress = request.getParameter("field_progress_max");

                logger.info("处理进度条件 - 最小进度: {}", minProgress);
                logger.info("处理进度条件 - 最大进度: {}", maxProgress);

                if (minProgress != null && !minProgress.isEmpty()) {
                    try {
                        searchCriteria.put("progress_min", Integer.parseInt(minProgress));
                    } catch (NumberFormatException e) {
                        logger.error("解析最小进度时出错: {}", e.getMessage());
                    }
                }
                if (maxProgress != null && !maxProgress.isEmpty()) {
                    try {
                        searchCriteria.put("progress_max", Integer.parseInt(maxProgress));
                    } catch (NumberFormatException e) {
                        logger.error("解析最大进度时出错: {}", e.getMessage());
                    }
                }
            } // 处理评论天数范围
            else if (fieldName.equals("commentDays")) {
                String minDays = request.getParameter("field_commentDays_min");
                String maxDays = request.getParameter("field_commentDays_max");

                logger.info("处理评论天数条件 - 最小天数: {}, 最大天数: {}", minDays, maxDays);

                if (minDays != null && !minDays.isEmpty()) {
                    try {
                        double minValue = Double.parseDouble(minDays);
                        searchCriteria.put("commentDays_min", minValue);
                        logger.info("添加评论天数最小值条件: commentDays_min = {}", minValue);
                    } catch (NumberFormatException e) {
                        logger.error("解析最小评论天数时出错: {}", e.getMessage());
                    }
                }
                if (maxDays != null && !maxDays.isEmpty()) {
                    try {
                        double maxValue = Double.parseDouble(maxDays);
                        searchCriteria.put("commentDays_max", maxValue);
                        logger.info("添加评论天数最大值条件: commentDays_max = {}", maxValue);
                    } catch (NumberFormatException e) {
                        logger.error("解析最大评论天数时出错: {}", e.getMessage());
                    }
                }

                // 确保任务在搜索前计算评论天数
                logger.info("评论天数搜索: 确保所有任务已计算评论天数");
                // 记录搜索条件中的评论天数范围
                logger.info("评论天数搜索条件汇总: 最小值={}, 最大值={}",
                        searchCriteria.containsKey("commentDays_min") ? searchCriteria.get("commentDays_min") : "不限",
                        searchCriteria.containsKey("commentDays_max") ? searchCriteria.get("commentDays_max") : "不限");

                // 输出字段名称信息，确认字段名称正确添加
                logger.info("评论天数字段名称 'commentDays' 已添加到fieldNames中");
            }
            // 处理任务阶段范围
            else if (fieldName.equals("taskStage")) {
                String minStage = request.getParameter("field_taskStage_min");
                String maxStage = request.getParameter("field_taskStage_max");

                logger.info("处理任务阶段条件 - 最小值: {}", minStage);
                logger.info("处理任务阶段条件 - 最大值: {}", maxStage);

                if (minStage != null && !minStage.isEmpty()) {
                    try {
                        searchCriteria.put("taskStage_min", Integer.parseInt(minStage));
                    } catch (NumberFormatException e) {
                        logger.error("解析任务阶段最小值时出错: {}", e.getMessage());
                    }
                }
                if (maxStage != null && !maxStage.isEmpty()) {
                    try {
                        searchCriteria.put("taskStage_max", Integer.parseInt(maxStage));
                    } catch (NumberFormatException e) {
                        logger.error("解析任务阶段最大值时出错: {}", e.getMessage());
                    }
                }
            }
            // 处理额定工期范围
            else if (fieldName.equals("ratedDurationDays")) {
                String minDuration = request.getParameter("field_ratedDurationDays_min");
                String maxDuration = request.getParameter("field_ratedDurationDays_max");

                logger.info("处理额定工期条件 - 最小值: {}", minDuration);
                logger.info("处理额定工期条件 - 最大值: {}", maxDuration);

                if (minDuration != null && !minDuration.trim().isEmpty()) {
                    try {
                        searchCriteria.put("ratedDurationDays_min", new BigDecimal(minDuration.trim()));
                        logger.info("添加额定工期最小值搜索条件: {}", minDuration.trim());
                    } catch (NumberFormatException e) {
                        logger.error("解析额定工期最小值时出错: {}", e.getMessage());
                    }
                }
                if (maxDuration != null && !maxDuration.trim().isEmpty()) {
                    try {
                        searchCriteria.put("ratedDurationDays_max", new BigDecimal(maxDuration.trim()));
                        logger.info("添加额定工期最大值搜索条件: {}", maxDuration.trim());
                    } catch (NumberFormatException e) {
                        logger.error("解析额定工期最大值时出错: {}", e.getMessage());
                    }
                }
            }
            // 处理剩余工期范围
            else if (fieldName.equals("remainingDurationDays")) {
                String minDuration = request.getParameter("field_remainingDurationDays_min");
                String maxDuration = request.getParameter("field_remainingDurationDays_max");

                logger.info("处理剩余工期条件 - 最小值: {}", minDuration);
                logger.info("处理剩余工期条件 - 最大值: {}", maxDuration);

                if (minDuration != null && !minDuration.trim().isEmpty()) {
                    try {
                        searchCriteria.put("remainingDurationDays_min", new BigDecimal(minDuration.trim()));
                        logger.info("添加剩余工期最小值搜索条件: {}", minDuration.trim());
                    } catch (NumberFormatException e) {
                        logger.error("解析剩余工期最小值时出错: {}", e.getMessage());
                    }
                }
                if (maxDuration != null && !maxDuration.trim().isEmpty()) {
                    try {
                        searchCriteria.put("remainingDurationDays_max", new BigDecimal(maxDuration.trim()));
                        logger.info("添加剩余工期最大值搜索条件: {}", maxDuration.trim());
                    } catch (NumberFormatException e) {
                        logger.error("解析剩余工期最大值时出错: {}", e.getMessage());
                    }
                }
            }
            // 处理任务类型条件
            else if (fieldName.equals("type")) {
                String type = request.getParameter("field_type");

                logger.info("处理任务类型条件: {}", type);

                if (type != null && !type.trim().isEmpty()) {
                    searchCriteria.put("type", type.trim());
                    logger.info("添加任务类型搜索条件: {}", type.trim());
                }
            }
            // 处理其他常规字段
            else {
                String fieldValue = request.getParameter("field_" + fieldName);
                if (fieldValue != null && !fieldValue.trim().isEmpty()) {
                    searchCriteria.put(fieldName, fieldValue.trim());
                    logger.info("字段 {} 搜索条件: {}", fieldName, fieldValue.trim());
                }
            }
        }

        // 确保只有在非管理视图时才添加当前用户限制
        if (!isFromManagement) {
            searchCriteria.put("responsible", currentUsername);
            logger.info("添加负责人限制: {}", currentUsername);
        }

        if (searchCriteria.size() == 0) {
            // 如果没有有效搜索条件（或者只有responsible字段），则返回对应的视图
            logger.info("没有有效的搜索条件，返回到{}页面", isFromManagement ? "任务管理" : "我的任务");
            return isFromManagement ? "redirect:/tasks/management" : "redirect:/tasks/order-tasks";
        }

        // 如果没有有效的查询条件，则返回所有项目
        if (searchCriteria.isEmpty()) {
            logger.info("没有有效的搜索条件，返回所有项目");
            return isFromManagement ? "redirect:/tasks/management" : "redirect:/tasks/order-tasks";
        }
        logger.info("执行动态搜索，条件数量: {}", searchCriteria.size());

        // 使用分页请求
        PageRequest pageRequest = PageRequest.of(page, Math.min(size, 20), Sort.by("createdDate").descending());

        // 执行搜索
        Page<ProjectTask> taskPage = taskService.dynamicSearchTasks(searchCriteria, pageRequest);

        logger.info("搜索结果: 总数 = {}, 当前页任务数 = {}",
                taskPage.getTotalElements(),
                taskPage.getContent().size());

        // 为每个任务安全加载项目信息
        for (ProjectTask task : taskPage.getContent()) {
            if (task.getProjectId() != null) {
                try {
                    // 确保先将task.project设置为null，防止有可能存在懒加载代理对象
                    task.setProject(null);

                    // 直接从数据库加载项目，避免使用可能导致懒加载异常的代理对象
                    Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());

                    if (projectOpt.isPresent()) {
                        Project project = projectOpt.get();
                        // 主动访问项目名称，确保不是懒加载代理对象
                        try {
                            String projectName = project.getProjectName();
                            if (projectName == null) {
                                project.setProjectName("未命名项目");
                            }
                            // 设置项目对象到任务
                            task.setProject(project);
                            logger.debug("已加载项目信息: {}", project.getProjectName());
                        } catch (Exception e) {
                            logger.warn("访问项目{}名称时出错: {}", task.getProjectId(), e.getMessage());
                            task.setProject(null);
                        }
                    } else {
                        // 项目不存在，设置project为null，前端将显示"项目不存在"
                        logger.warn("任务引用的项目ID {} 不存在", task.getProjectId());
                        task.setProject(null);
                    }
                } catch (EntityNotFoundException e) {
                    // 显式捕获EntityNotFoundException
                    logger.warn("项目ID {} 不存在: {}", task.getProjectId(), e.getMessage());
                    task.setProject(null);
                } catch (Exception e) {
                    // 捕获其他可能的异常
                    logger.error("加载项目 {} 时出错: {}", task.getProjectId(), e.getMessage());
                    task.setProject(null);
                }
            }

            // 确保任务的进度信息正确
            if (task.getProgress() == null) {
                task.setProgress(0);
            }
        } // 计算所有任务的评论天数
        taskService.calculateCommentDays(taskPage.getContent());
        logger.info("已计算搜索结果中 {} 个任务的评论天数", taskPage.getContent().size());

        // 累计工期和剩余工期现在直接从数据库字段获取，无需手动计算
        logger.info("高级搜索页面加载完成，累计工期和剩余工期将从数据库字段直接显示");

        // 获取当前用户进行中任务数量（针对order-tasks页面）
        if (!isFromManagement) {
            Long inProgressCount = taskService.countTasksByResponsibleAndStatus(currentUsername, "进行中");
            model.addAttribute("inProgressTaskCount", inProgressCount);
        } else {
            // 对于管理页面，获取所有进行中任务数量
            Long inProgressCount = taskService.countTasksByStatus("进行中");
            model.addAttribute("allInProgressTaskCount", inProgressCount);
        }

        // 添加人员列表到模型中，用于负责人下拉选择
        model.addAttribute("personnel", optionsService.getPersonnel());
        model.addAttribute("visionTypes", optionsService.getVisionTypes());
        model.addAttribute("taskTypes", optionsService.getTaskTypes());
        model.addAttribute("taskPage", taskPage);
        model.addAttribute("activeMenu", isFromManagement ? "taskmanagement" : "mytasks");
        model.addAttribute("isAdvancedSearch", true);

        // 如果是管理页面视图，计算绩效分总和（全量，不分页）
        if (isFromManagement) {
            List<ProjectTask> allTasks = taskService.dynamicSearchTasks(searchCriteria, null).getContent();
            double bonusTotal = 0.0;
            for (ProjectTask task : allTasks) {
                if (task.getBonus() != null) {
                    bonusTotal += task.getBonus().doubleValue();
                }
            }
            model.addAttribute("bonusTotal", bonusTotal);
        }

        // 将搜索条件添加到模型，用于表单回显和分页
        for (Map.Entry<String, Object> entry : searchCriteria.entrySet()) {
            // 移除对负责人字段的排除，确保所有字段都能显示
            model.addAttribute("field_" + entry.getKey(), entry.getValue());
            logger.info("添加字段到模型: {} = {}", entry.getKey(), entry.getValue());

            // 添加用于分页的字段名，确保分页时保留搜索条件
            if (!model.containsAttribute("fieldNames")) {
                model.addAttribute("fieldNames", new ArrayList<String>());
            }
            @SuppressWarnings("unchecked")
            List<String> modelFieldNames = (List<String>) model.getAttribute("fieldNames");
            if (modelFieldNames != null && !modelFieldNames.contains(entry.getKey())) {
                modelFieldNames.add(entry.getKey());
                logger.info("添加字段名到fieldNames列表: {}", entry.getKey());
            }
        } // 防止视图名称被篡改
        if (viewName.contains("project")) {
            logger.error("错误: 视图名称包含'project'! 强制修正为正确的任务视图");
            viewName = isFromManagement ? "tasks/management-tasks" : "tasks/my-tasks";
        }

        logger.info("返回视图: {}", viewName);
        logger.info("===== 高级搜索请求结束 =====");

        return viewName;
    }

    /**
     * 提交任务（标记为已完成）
     *
     * @param id                 任务ID
     * @param redirectAttributes 重定向属性
     * @return 重定向到任务详情页
     */
    @PostMapping("/{id}/complete")
    public String completeTask(
            @PathVariable("id") Long id,
            RedirectAttributes redirectAttributes,
            HttpServletRequest request) {

        logger.info("提交任务请求: ID = {}", id);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 获取任务信息
        Optional<ProjectTask> taskOpt = taskService.findTaskById(id);
        if (!taskOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "找不到指定的任务");
            return "redirect:/tasks/order-tasks";
        }

        ProjectTask task = taskOpt.get();
        boolean isTaskResponsible = currentUsername.equals(task.getResponsible()); // 权限检查：
        // 1. 管理员可以完成任何任务
        // 2. 经理只能完成自己负责的任务
        // 3. 操作员不能完成任何任务
        if (!isAdmin && !(isManager && isTaskResponsible)) {
            redirectAttributes.addFlashAttribute("error", "您没有权限完成此任务");
            return "redirect:/tasks/" + id;
        }

        try {
            // 获取原始任务状态用于记录
            String oldStatus = task.getStatus();

            // 完成任务（设置状态为"已完成"，进度为100%，并设置实际结束日期）
            ProjectTask completedTask = taskService.completeTask(id); // 记录活动日志 - 使用SETTINGS_CHANGE类型，因为这是状态变更
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                String ipAddress = getClientIpAddress();
                // 增加项目名称
                String description = "任务 [" + completedTask.getTaskName() + "] 的状态从 [" + oldStatus + "] 变更到 [已完成] 所属项目 "
                        + completedTask.getProject().getProjectName();

                activityLogService.logSettingsChange(
                        user.getUserId(),
                        currentUsername,
                        description,
                        ipAddress,
                        "Task",
                        completedTask.getTaskId(),
                        getAccessType());

                logger.info("用户 {} {} ID: {}", currentUsername, description, completedTask.getTaskId());
            }

            redirectAttributes.addFlashAttribute("message", "任务已成功完成");

            // 获取来源页面URL
            String referer = request.getHeader("Referer");

            // 返回到来源页面
            if (referer != null && !referer.isEmpty()) {
                return "redirect:" + referer;
            }

            return "redirect:/tasks/" + id;
        } catch (Exception e) {
            logger.error("完成任务时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "完成任务时出错，请稍后再试");
            return "redirect:/tasks/" + id;
        }
    }

    @GetMapping("/special")
    public String mySpecialTasks(Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        logger.info("查询用户 {} 的专项任务列表", currentUsername);

        try {
            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            });

            // 创建分页请求，不指定排序，排序逻辑在service层实现
            PageRequest pageRequest = PageRequest.of(page, Math.min(size, 20));

            // 获取当前用户的专项任务
            Page<ProjectTask> taskPage = taskService.findSpecialTasksByResponsible(currentUsername, pageRequest);

            logger.info("找到用户 {} 的专项任务共 {} 个", currentUsername, taskPage.getTotalElements());

            // 为每个任务加载项目信息并确保进度信息正确
            for (ProjectTask task : taskPage.getContent()) {
                // 加载项目信息
                if (task.getProjectId() != null) {
                    try {
                        Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                        if (projectOpt.isPresent()) {
                            Project project = projectOpt.get();
                            // 确保项目名称不为空
                            if (project.getProjectName() == null) {
                                project.setProjectName("未命名项目");
                            }
                            task.setProject(project);
                            logger.debug("已加载项目信息: {}", project.getProjectName());
                        } else {
                            // 项目不存在，设置project为null
                            logger.warn("项目ID {} 已不存在", task.getProjectId());
                            task.setProject(null);
                        }
                    } catch (Exception e) {
                        // 处理异常情况
                        logger.error("加载项目 {} 信息时出错: {}", task.getProjectId(), e.getMessage());
                        task.setProject(null);
                    }
                } // 确保任务的进度信息正确
                if (task.getProgress() == null) {
                    task.setProgress(0);
                }

                logger.debug("任务 [{}] 进度: {}%, 比例: {}", task.getTaskName(), task.getProgress(), task.getRatio());
            }

            // 计算所有任务的评论天数
            taskService.calculateCommentDays(taskPage.getContent());
            logger.info("已计算专项任务中 {} 个任务的评论天数", taskPage.getContent().size());

            // 获取当前用户进行中的专项任务数量
            Long inProgressCount = taskService.countInProgressSpecialTasksByResponsible(currentUsername);
            model.addAttribute("inProgressTaskCount", inProgressCount);

            // 添加到模型
            model.addAttribute("taskPage", taskPage);
            model.addAttribute("activeMenu", "specialtasks");

            // 设置默认主题（如果用户没有主题设置）
            if (!model.containsAttribute("userTheme")) {
                model.addAttribute("userTheme", "theme-light");
            }

            return "tasks/my-special-tasks";

        } catch (Exception e) {
            logger.error("加载专项任务列表时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping("/difficult")
    public String myDifficultTasks(Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        logger.info("查询用户 {} 的难点任务列表", currentUsername);

        try {
            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            });

            // 创建分页请求，不指定排序，排序逻辑在service层实现
            PageRequest pageRequest = PageRequest.of(page, Math.min(size, 20));

            // 获取当前用户的难点任务
            Page<ProjectTask> taskPage = taskService.findDifficultTasksByResponsible(currentUsername, pageRequest);

            logger.info("找到用户 {} 的难点任务共 {} 个", currentUsername, taskPage.getTotalElements());

            // 为每个任务加载项目信息并确保进度信息正确
            for (ProjectTask task : taskPage.getContent()) {
                // 加载项目信息
                if (task.getProjectId() != null) {
                    try {
                        Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                        if (projectOpt.isPresent()) {
                            Project project = projectOpt.get();
                            // 确保项目名称不为空
                            if (project.getProjectName() == null) {
                                project.setProjectName("未命名项目");
                            }
                            task.setProject(project);
                            logger.debug("已加载项目信息: {}", project.getProjectName());
                        } else {
                            // 项目不存在，设置project为null
                            logger.warn("项目ID {} 已不存在", task.getProjectId());
                            task.setProject(null);
                        }
                    } catch (Exception e) {
                        // 处理异常情况
                        logger.error("加载项目 {} 信息时出错: {}", task.getProjectId(), e.getMessage());
                        task.setProject(null);
                    }
                }

                // 确保任务的进度信息正确
                if (task.getProgress() == null) {
                    task.setProgress(0);
                }

                logger.debug("任务 [{}] 进度: {}%, 比例: {}", task.getTaskName(), task.getProgress(), task.getRatio());
            }

            // 计算所有任务的评论天数
            taskService.calculateCommentDays(taskPage.getContent());
            logger.info("已计算难点任务中 {} 个任务的评论天数", taskPage.getContent().size());

            // 获取当前用户进行中的难点任务数量
            Long inProgressCount = taskService.countInProgressDifficultTasksByResponsible(currentUsername);
            model.addAttribute("inProgressTaskCount", inProgressCount);

            // 添加到模型
            model.addAttribute("taskPage", taskPage);
            model.addAttribute("activeMenu", "difficulttasks");

            // 设置默认主题（如果用户没有主题设置）
            if (!model.containsAttribute("userTheme")) {
                model.addAttribute("userTheme", "theme-light");
            }

            return "tasks/my-difficult-tasks";

        } catch (Exception e) {
            logger.error("加载难点任务列表时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 显示所有任务（任务管理页面）
     */
    @GetMapping({ "/management", "/management-tasks" })
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String managementTasks(Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) List<String> fieldNames,
            HttpServletRequest request) {

        logger.info("加载任务管理页面");

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取用户主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                logger.info("用户主题: {}", theme);
            }); // 创建分页请求，不指定排序，排序逻辑在service层实现
            PageRequest pageRequest = PageRequest.of(page, Math.min(size, 20));

            // 获取所有任务（不分页，用于统计总绩效分）
            List<ProjectTask> allTasks = taskService.findAllTasks();
            double bonusTotal = 0.0;
            for (ProjectTask task : allTasks) {
                if (task.getBonus() != null) {
                    bonusTotal += task.getBonus().doubleValue();
                }
            }
            model.addAttribute("bonusTotal", bonusTotal);

            // 分页数据
            Page<ProjectTask> taskPage = taskService.findAllTasks(pageRequest);

            // 为每个任务加载项目信息并确保进度信息正确
            for (ProjectTask task : taskPage.getContent()) {
                // 加载项目信息
                if (task.getProjectId() != null) {
                    try {
                        Optional<Project> projectOpt = projectService.findProjectById(task.getProjectId());
                        if (projectOpt.isPresent()) {
                            Project project = projectOpt.get();
                            // 确保项目名称不为空
                            if (project.getProjectName() == null) {
                                project.setProjectName("未命名项目");
                            }
                            task.setProject(project);
                            logger.debug("已加载项目信息: {}", project.getProjectName());
                        } else {
                            // 项目不存在，设置project为null
                            logger.warn("项目ID {} 已不存在", task.getProjectId());
                            task.setProject(null);
                        }
                    } catch (Exception e) {
                        // 处理异常情况
                        logger.error("加载项目 {} 信息时出错: {}", task.getProjectId(), e.getMessage());
                        task.setProject(null);
                    }
                }

                // 确保任务的进度信息正确
                if (task.getProgress() == null) {
                    task.setProgress(0);
                }
            } // 计算所有任务的评论天数
            taskService.calculateCommentDays(taskPage.getContent());
            logger.info("已计算任务管理页面中 {} 个任务的评论天数", taskPage.getContent().size());

            // 累计工期和剩余工期现在直接从数据库字段获取，无需手动计算
            logger.info("任务管理页面加载完成，累计工期和剩余工期将从数据库字段直接显示");

            // 获取所有进行中任务数量
            Long allInProgressTaskCount = taskService.countTasksByStatus("进行中");
            model.addAttribute("allInProgressTaskCount", allInProgressTaskCount);

            // 添加人员列表到模型中，用于负责人下拉选择
            model.addAttribute("personnel", optionsService.getPersonnel());
            model.addAttribute("visionTypes", optionsService.getVisionTypes());
            model.addAttribute("taskTypes", optionsService.getTaskTypes()); // 添加到模型
            model.addAttribute("taskPage", taskPage);
            model.addAttribute("activeMenu", "taskmanagement");

            // 设置默认主题（如果用户没有主题设置）
            if (!model.containsAttribute("userTheme")) {
                model.addAttribute("userTheme", "theme-light");
            }

            return "tasks/management-tasks";

        } catch (Exception e) {
            logger.error("加载任务管理页面时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新所有进行中任务的实际工期
     */
    @PostMapping("/update-all-in-progress-tasks-duration")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    @ResponseBody
    public Map<String, Object> updateAllInProgressTasksDuration() {
        logger.info("管理员请求更新所有进行中任务的实际工期");

        Map<String, Object> result = new HashMap<>();

        try {
            String updateResult = taskService.updateAllInProgressTasksDuration();
            result.put("success", true);
            result.put("message", updateResult);
            logger.info("更新所有进行中任务工期完成：{}", updateResult);
        } catch (Exception e) {
            logger.error("更新所有进行中任务工期时出错：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 修复所有任务的累计工期和剩余工期
     */
    // 使用volatile确保多线程可见性
    private static volatile boolean isUpdatingAllTasks = false;

    @PostMapping("/update-all-tasks-duration-fields")
    @PreAuthorize("hasRole('ADMIN')")
    @ResponseBody
    public Map<String, Object> updateAllTasksDurationFields() {
        logger.info("管理员请求修复所有任务的累计工期和剩余工期");

        Map<String, Object> result = new HashMap<>();
        
        // 检查是否已经有更新任务在进行中
        if (isUpdatingAllTasks) {
            result.put("success", false);
            result.put("message", "已有一个更新任务正在进行中，请等待当前更新完成后再试");
            result.put("showAlert", true);
            return result;
        }
        
        // 设置更新标志
        isUpdatingAllTasks = true;

        try {
            // 获取所有任务，但只获取ID字段
            List<ProjectTask> allTasks = taskService.findAllTasksForUpdate();
            List<Long> allTaskIds = allTasks.stream()
                .map(ProjectTask::getTaskId)
                .collect(Collectors.toList());
            int totalTasks = allTaskIds.size();
            logger.info("找到 {} 个任务需要更新工期字段", totalTasks);

            // 设置批处理大小
            final int BATCH_SIZE = 50;
            int successCount = 0;
            int errorCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            // 分批处理任务
            for (int i = 0; i < allTaskIds.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, allTaskIds.size());
                List<Long> batchIds = allTaskIds.subList(i, endIndex);
                
                // 处理当前批次
                for (Long taskId : batchIds) {
                    try {
                        // 每次只加载一个任务
                        Optional<ProjectTask> taskOpt = taskService.findTaskById(taskId);
                        if (taskOpt.isPresent()) {
                            ProjectTask task = taskOpt.get();
                            // 调用保存方法，这会触发工期字段的自动计算和更新
                            taskService.saveTask(task);
                            successCount++;
                        } else {
                            errorCount++;
                            errorMessages.append(String.format("任务ID %d 不存在; ", taskId));
                        }
                    } catch (Exception e) {
                        errorCount++;
                        String errorMsg = String.format("任务ID %d 更新失败: %s", taskId, e.getMessage());
                        errorMessages.append(errorMsg).append("; ");
                        logger.error("更新任务 {} 的工期字段时出错: {}", taskId, e.getMessage());
                    }
                }

                // 完成一个批次后清理内存
                System.gc();

                // 记录进度
                int currentProgress = Math.min(endIndex, totalTasks);
                logger.info("已处理 {}/{} 个任务 ({}%)", 
                    currentProgress, 
                    totalTasks, 
                    String.format("%.2f", (currentProgress * 100.0 / totalTasks)));
                
                // 每批次处理后短暂休息，避免数据库压力过大
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }

            }

            String message = String.format("更新完成！成功: %d 个，失败: %d 个", successCount, errorCount);
            if (errorCount > 0) {
                // 限制错误消息的长度，避免返回过长的字符串
                String errorDetail = errorMessages.length() > 500 ? 
                    errorMessages.substring(0, 500) + "..." : 
                    errorMessages.toString();
                message += "。部分错误详情: " + errorDetail;
            }

            logger.info("批量更新所有任务工期字段完成: {}", message);

            result.put("success", true);
            result.put("message", message);
            result.put("successCount", successCount);
            result.put("errorCount", errorCount);
            result.put("showAlert", true);

        } catch (Exception e) {
            String errorMsg = "批量更新所有任务工期字段时出错: " + e.getMessage();
            logger.error(errorMsg, e);

            result.put("success", false);
            result.put("message", errorMsg);
            result.put("showAlert", true);
        } finally {
            // 无论成功还是失败，都要重置更新标志
            isUpdatingAllTasks = false;
            logger.info("重置任务更新标志状态");
        }

        return result;
    }
}
