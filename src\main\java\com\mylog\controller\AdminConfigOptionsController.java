package com.mylog.controller;

import com.mylog.model.config.ConfigOption;
import com.mylog.service.ConfigOptionService;
import com.mylog.service.EnhancedOptionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

@Controller
@RequestMapping("/admin/config-options")
public class AdminConfigOptionsController {
    
    @Autowired
    private ConfigOptionService configOptionService;
    
    @Autowired
    private EnhancedOptionsService enhancedOptionsService;
    
    /**
     * 配置选项管理页面
     */
    @GetMapping
    public String index(Model model) {
        List<String> categories = configOptionService.getAllCategories();
        model.addAttribute("categories", categories);
        model.addAttribute("activeMenu", "config-options");
        return "admin/config-options/index";
    }
    
    /**
     * 查看指定类别的配置选项
     */
    @GetMapping("/category/{category}")
    public String viewCategory(@PathVariable String category, Model model) {
        List<ConfigOption> options = configOptionService.getConfigOptionsByCategory(category);
        List<String> categories = configOptionService.getAllCategories();
        
        model.addAttribute("category", category);
        model.addAttribute("options", options);
        model.addAttribute("categories", categories);
        model.addAttribute("activeMenu", "config-options");
        return "admin/config-options/category";
    }
    
    /**
     * 编辑配置选项表单
     */
    @GetMapping("/edit")
    public String editForm(
            @RequestParam String category,
            @RequestParam String value,
            Model model) {
        ConfigOption option = configOptionService.getConfigOption(category, value)
                .orElse(new ConfigOption(category, value, 0));
        
        model.addAttribute("option", option);
        model.addAttribute("isNew", option.getSortOrder() == null || option.getSortOrder() == 0);
        model.addAttribute("activeMenu", "config-options");
        return "admin/config-options/edit";
    }
    
    /**
     * 保存配置选项
     */
    @PostMapping("/save")
    public String save(
            @RequestParam String category,
            @RequestParam String value,
            @RequestParam(required = false) Integer sortOrder,
            @RequestParam(required = false) Double ratio,
            @RequestParam(required = false) String remark,
            RedirectAttributes redirectAttributes) {
        
        try {
            ConfigOption option = configOptionService.getConfigOption(category, value)
                    .orElse(new ConfigOption());
            
            option.setCategory(category);
            option.setValue(value);
            option.setSortOrder(sortOrder != null ? sortOrder : 
                configOptionService.getMaxSortOrderByCategory(category) + 1);
            option.setRatio(ratio);
            option.setRemark(remark);
            
            configOptionService.saveConfigOption(option);
            redirectAttributes.addFlashAttribute("success", "配置选项保存成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "保存失败: " + e.getMessage());
        }
        
        return "redirect:/admin/config-options/category/" + category;
    }
    
    /**
     * 删除配置选项
     */
    @PostMapping("/delete")
    public String delete(
            @RequestParam String category,
            @RequestParam String value,
            RedirectAttributes redirectAttributes) {
        
        try {
            configOptionService.deleteConfigOption(category, value);
            redirectAttributes.addFlashAttribute("success", "配置选项删除成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "删除失败: " + e.getMessage());
        }
        
        return "redirect:/admin/config-options/category/" + category;
    }
    
    /**
     * 批量更新比率
     */
    @PostMapping("/batch-update-ratio")
    public String batchUpdateRatio(
            @RequestParam String category,
            @RequestParam List<String> values,
            @RequestParam List<Double> ratios,
            RedirectAttributes redirectAttributes) {
        
        try {
            for (int i = 0; i < values.size() && i < ratios.size(); i++) {
                String value = values.get(i);
                Double ratio = ratios.get(i);
                if (ratio != null) {
                    enhancedOptionsService.updateOptionRatio(category, value, ratio);
                }
            }
            redirectAttributes.addFlashAttribute("success", "批量更新比率成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "批量更新失败: " + e.getMessage());
        }
        
        return "redirect:/admin/config-options/category/" + category;
    }
}
