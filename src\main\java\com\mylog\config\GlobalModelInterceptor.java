package com.mylog.config;

import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import com.mylog.service.TaskService;
import com.mylog.service.ProjectService;
import com.mylog.service.WorkflowInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class GlobalModelInterceptor implements HandlerInterceptor {

    @Autowired
    private TaskService taskService;
    
    @Autowired
    private ProjectService projectService;
    
    @Autowired
    private WorkflowInstanceService workflowInstanceService;

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        if (modelAndView != null) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String currentUsername = authentication.getName();
                
                // 获取进行中的订单任务数量
                Long inProgressTaskCount = taskService.countOrderTasksByResponsibleAndStatus(currentUsername, "进行中");
                
                // 获取进行中的难点焦点任务数量
                Long inProgressDifficultTaskCount = taskService.countInProgressDifficultTasksByResponsible(currentUsername);
                
                // 获取进行中的专项任务数量
                Long inProgressSpecialTaskCount = taskService.countInProgressSpecialTasksByResponsible(currentUsername);
                
                // 获取进行中的分管任务数量
                Long inProgressDelegatedTaskCount = taskService.countInProgressDelegatedTasks(currentUsername);
                
                // 获取进行中的教育培训任务数量
                Long inProgressTrainingTaskCount = taskService.countInProgressTrainingTasks(currentUsername);
                
                // 获取我负责的进行中项目数量
                Long myInProgressProjectCount = projectService.countProjectsByResponsibleAndStatus(currentUsername, "进行中");
                
                // 获取所有进行中项目数量
                Long inProgressProjectCount = projectService.countProjectsByStatus("进行中");
                
                // 获取待审批任务数量
                Long todoTasksCount = (long) workflowInstanceService.findTodoTasks(currentUsername).size();
                
                // 获取我发起的处理中流程数量
                Long myProcessingWorkflowCount = (long) workflowInstanceService.findInstancesByInitiatorAndStatus(currentUsername, WorkflowStatus.PROCESSING).size();
                
                // 添加到模型中
                modelAndView.addObject("inProgressTaskCount", inProgressTaskCount);
                modelAndView.addObject("inProgressDifficultTaskCount", inProgressDifficultTaskCount);
                modelAndView.addObject("inProgressSpecialTaskCount", inProgressSpecialTaskCount);
                modelAndView.addObject("inProgressDelegatedTaskCount", inProgressDelegatedTaskCount);
                modelAndView.addObject("inProgressTrainingTaskCount", inProgressTrainingTaskCount);
                modelAndView.addObject("myInProgressProjectCount", myInProgressProjectCount);
                modelAndView.addObject("inProgressProjectCount", inProgressProjectCount);
                modelAndView.addObject("todoTasksCount", todoTasksCount);
                modelAndView.addObject("myProcessingWorkflowCount", myProcessingWorkflowCount);
            }
        }
    }
}