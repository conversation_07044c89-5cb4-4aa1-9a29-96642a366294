package com.mylog.service;

import com.mylog.dto.EventReminderDTO;
import com.mylog.model.EventReminder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 事件提醒服务接口
 */
public interface EventReminderService {
    
    /**
     * 创建提醒
     */
    EventReminderDTO createReminder(EventReminderDTO reminderDTO);
    
    /**
     * 更新提醒
     */
    EventReminderDTO updateReminder(Long id, EventReminderDTO reminderDTO);
    
    /**
     * 删除提醒
     */
    void deleteReminder(Long id);
    
    /**
     * 根据ID获取提醒
     */
    Optional<EventReminderDTO> getReminderById(Long id);
    
    /**
     * 根据事件ID获取提醒列表
     */
    List<EventReminderDTO> getRemindersByEventId(Long eventId);
    
    /**
     * 获取需要发送的提醒
     */
    List<EventReminderDTO> getPendingReminders(LocalDateTime currentTime);
    
    /**
     * 标记提醒为已发送
     */
    void markReminderAsSent(Long reminderId);
    
    /**
     * 批量标记提醒为已发送
     */
    void markRemindersAsSent(List<Long> reminderIds);
    
    /**
     * 发送提醒通知
     */
    void sendReminderNotification(EventReminderDTO reminder);
    
    /**
     * 处理待发送的提醒
     */
    void processPendingReminders();
    
    /**
     * 为事件创建默认提醒
     */
    List<EventReminderDTO> createDefaultReminders(Long eventId, LocalDateTime eventStartTime);
    
    /**
     * 删除事件的所有提醒
     */
    void deleteRemindersByEventId(Long eventId);
}
