<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程步骤管理')}">
    <meta charset="UTF-8">
    <title>流程步骤管理</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                流程步骤管理
                <small class="text-muted" th:if="${template != null}" th:text="'- ' + ${template.templateName}"></small>
            </h1>
            <div>
                <a th:href="@{/workflow/templates}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回模板列表
                </a>
                <a th:if="${template != null}" th:href="@{/workflow/steps/create(templateId=${template.templateId})}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> 添加步骤
                </a>
            </div>
        </div>

        <!-- 模板信息卡片 -->
        <div class="row mb-4" th:if="${template != null}">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">模板信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <p class="mb-1 fw-bold">模板名称</p>
                                <p th:text="${template.templateName}">模板名称</p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1 fw-bold">模板标题</p>
                                <!-- 修复这里，将templateCode改为templateTitle -->
                                <p th:text="${template.templateTitle}">模板标题</p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1 fw-bold">适用范围</p>
                                <p th:text="${template.applicableScope}">适用范围</p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1 fw-bold">状态</p>
                                <p>
                                    <span th:if="${template.enabled}" class="badge bg-success">启用</span>
                                    <span th:unless="${template.enabled}" class="badge bg-secondary">禁用</span>
                                </p>
                            </div>
                            <div class="col-md-12 mt-2">
                                <p class="mb-1 fw-bold">描述</p>
                                <p th:text="${template.description ?: '无'}">描述</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 步骤列表 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">步骤列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 成功/错误消息 -->
                        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <span th:text="${message}">操作成功</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                  
                        <!-- 步骤列表表格 -->
                        <div class="table-responsive" th:if="${steps != null && !steps.isEmpty()}">
                            <h5>原始步骤列表</h5>
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 80px;">顺序</th>
                                        <th>步骤名称</th>
                                        <th>审批人类型</th>
                                        <th>审批人配置</th>
                                        <th>允许退回</th>
                                        <th>允许转交</th>
                                        <th>条件分支</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="stepTableBody">
                                    <!-- 简化的动态显示步骤行 -->
                                    <tr th:each="workflowStep : ${steps}" th:data-id="${workflowStep.stepId}" th:data-order="${workflowStep.stepOrder}">
                                        <td>
                                            <span class="badge bg-primary" th:text="${workflowStep.stepOrder}">1</span>
                                            <div class="btn-group-vertical ms-1">
                                                <button type="button" class="btn btn-sm btn-outline-secondary move-up" title="上移"
                                                        th:disabled="${workflowStep.stepOrder == 1}">
                                                    <i class="bi bi-arrow-up"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary move-down" title="下移"
                                                        th:disabled="${workflowStep.stepOrder == steps.size()}">
                                                    <i class="bi bi-arrow-down"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td th:text="${workflowStep.stepName}">步骤名称</td>
                                        <td th:text="${workflowStep.approverType != null ? (
                                            workflowStep.approverType.name() == 'FIXED_USER' ? '固定用户' : 
                                            workflowStep.approverType.name() == 'ROLE' ? '角色' :
                                            workflowStep.approverType.name() == 'DEPARTMENT_HEAD' ? '部门主管' : 
                                            workflowStep.approverType.name() == 'DYNAMIC' ? '动态指定' : '未知'
                                            ) : '无类型'}">审批人类型</td>
                                        <td th:text="${workflowStep.approverConfig}">审批人配置</td>
                                        <td>
                                            <i class="bi" th:classappend="${workflowStep.allowReject ? 'bi-check-lg text-success' : 'bi-x-lg text-danger'}"></i>
                                        </td>
                                        <td>
                                            <i class="bi" th:classappend="${workflowStep.allowTransfer ? 'bi-check-lg text-success' : 'bi-x-lg text-danger'}"></i>
                                        </td>
                                        <td>
                                            <i class="bi" th:classappend="${workflowStep.isConditionBranch ? 'bi-check-lg text-success' : 'bi-x-lg text-danger'}"></i>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:href="@{/workflow/steps/{id}/edit(id=${workflowStep.stepId})}" class="btn btn-sm btn-outline-primary" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" title="删除"
                                                        th:attr="data-step-id=${workflowStep.stepId},data-step-name=${workflowStep.stepName}"
                                                        onclick="confirmDelete(this.getAttribute('data-step-id'), this.getAttribute('data-step-name'))">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${steps == null || steps.isEmpty()}">
                            <p class="text-muted mb-0">暂无流程步骤</p>
                            <a th:if="${template != null}" th:href="@{/workflow/steps/create(templateId=${template.templateId})}" class="btn btn-primary mt-3">
                                <i class="bi bi-plus-lg"></i> 添加第一个步骤
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除步骤 "<span id="deleteStepName"></span>" 吗？</p>
                    <p class="text-danger">此操作不可逆，删除后将无法恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" th:action="@{/workflow/steps/delete}" method="post">
                        <input type="hidden" id="deleteStepId" name="stepId">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局定义confirmDelete函数，确保在任何情况下都可以访问
        function confirmDelete(stepId, stepName) {
            console.log('确认删除步骤：ID=' + stepId + ', 名称=' + stepName);
            document.getElementById('deleteStepId').value = stepId;
            document.getElementById('deleteStepName').textContent = stepName;
            
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
    </script>

    <script th:inline="javascript">
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始检查表格');
            console.log('------------ 表格统计开始 ------------');

            // 获取后端数据行数，这是实际应该显示的行数
            const backendRowCount = /*[[${stepsCount != null ? stepsCount : 0}]]*/ 0;
            console.log('后端数据显示步骤总数:', backendRowCount);

            // 修复简单表格显示问题
            fixSimpleTableDisplay();

            // 修复原始表格显示问题
            fixOriginalTableDisplay();

            console.log('------------ 表格统计结束 ------------');

            // 初始化删除确认功能
            initDeleteConfirmation();

            // 初始化步骤顺序调整功能
            initStepReordering();

            /**
             * 清理表格中的空白节点和非元素节点
             */
            function cleanupTableNodes(tableBody) {
                const tableIdForLog = tableBody.id || (tableBody.closest('table') ? tableBody.closest('table').className : '未知表格tbody');
                console.log(`[清理节点] 开始清理: ${tableIdForLog}`);
                console.log(`[清理节点] 清理前 - childNodes.length: ${tableBody.childNodes.length}, children.length: ${tableBody.children.length}`);

                let node = tableBody.firstChild;
                let removedCount = 0;
                while (node) {
                    const nextNode = node.nextSibling;
                    if (node.nodeType !== Node.ELEMENT_NODE) { // Node.ELEMENT_NODE (1) 代表元素节点
                        // 如需详细调试，可取消以下注释:
                        // console.log(`[清理节点] 准备移除节点: 类型=${node.nodeType}, 名称=${node.nodeName}, 内容='${(node.nodeValue || "").trim()}'`);
                        tableBody.removeChild(node);
                        removedCount++;
                    }
                    node = nextNode;
                }
                console.log(`[清理节点] 共移除了 ${removedCount} 个非元素节点.`);
                console.log(`[清理节点] 清理后 - childNodes.length: ${tableBody.childNodes.length}, children.length: ${tableBody.children.length}`);
            }

            /**
             * 修复简单表格显示问题的函数
             */
            function fixSimpleTableDisplay() {
                const simpleTableContainer = document.querySelector('.table-responsive.mb-4');
                if (!simpleTableContainer) {
                    console.log('【简单表】未找到简单表格容器');
                    return;
                }
                
                const simpleTableBody = simpleTableContainer.querySelector('tbody');
                if (!simpleTableBody) {
                    console.log('【简单表】未找到简单表格的tbody');
                    return;
                }
                
                cleanupTableNodes(simpleTableBody);
                
                const rows = simpleTableBody.children; // 清理后，.children 只应包含元素节点 (<tr>)
                console.log('【简单表】实际行数 (清理后, 来自 children):', rows.length);

                if (rows.length !== backendRowCount) {
                    console.error('【简单表】【错误】行数与后端数据不匹配!',
                                '表格显示行数:', rows.length,
                                '后端期望行数:', backendRowCount);
                }
                
                // 验证每行数据是否完整
                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i];
                    const allCells = row.getElementsByTagName('td');
                    console.log(`【简单表】第${i+1}行数据:`,
                        'ID:', allCells[0] ? allCells[0].textContent.trim() : '无',
                        '名称:', allCells[1] ? allCells[1].textContent.trim() : '无',
                        '顺序:', allCells[2] ? allCells[2].textContent.trim() : '无');
                }
            }
            
            /**
             * 修复原始表格显示问题的函数
             */
            function fixOriginalTableDisplay() {
                try {
                    console.log('【原始表】开始检查...');
                    
                    const originalTableContainer = document.querySelector('.table-responsive:not(.mb-4)');
                    if (!originalTableContainer) {
                        console.log('【原始表】未找到原始表格容器');
                        return;
                    }
                    
                    // 简单直接地获取表格的行数，不进行复杂处理
                    const table = originalTableContainer.querySelector('table');
                    if (!table) {
                        console.log('【原始表】未找到表格元素');
                        return;
                    }
                    
                    const rows = table.querySelectorAll('tr');
                    console.log('【原始表】总行数 (包括表头):', rows.length);
                    
                    // 减去表头行，得到数据行数量
                    const dataRowCount = rows.length - 1; // 假设只有一行表头
                    console.log('【原始表】数据行数 (不包括表头):', dataRowCount);
                    
                    // 与后端期望行数比较
                    if (dataRowCount !== backendRowCount) {
                        console.warn('【原始表】数据行数与后端期望行数不匹配',
                                    '表格行数:', dataRowCount,
                                    '后端期望行数:', backendRowCount);
                    } else {
                        console.log('【原始表】数据行数与后端期望行数匹配');
                    }
                } catch (error) {
                    // 捕获可能的JavaScript错误，确保不破坏页面功能
                    console.error('【原始表】处理过程中出错:', error.message);
                }
            }
            
            /**
             * 初始化删除确认功能
             */
            function initDeleteConfirmation() {
                window.confirmDelete = function(stepId, stepName) {
                    document.getElementById('deleteStepId').value = stepId;
                    document.getElementById('deleteStepName').textContent = stepName;
                    
                    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                    deleteModal.show();
                };
            }
            
            /**
             * 初始化步骤顺序调整功能
             */
            function initStepReordering() {
                const moveUpButtons = document.querySelectorAll('.move-up');
                const moveDownButtons = document.querySelectorAll('.move-down');
                
                moveUpButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const row = this.closest('tr');
                        const stepId = row.dataset.id;
                        const currentOrder = parseInt(row.dataset.order);
                        const newOrder = currentOrder - 1;
                        reorderStep(stepId, newOrder);
                    });
                });
                
                moveDownButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const row = this.closest('tr');
                        const stepId = row.dataset.id;
                        const currentOrder = parseInt(row.dataset.order);
                        const newOrder = currentOrder + 1;
                        reorderStep(stepId, newOrder);
                    });
                });
            }
            
            /**
             * 重新排序步骤
             */
            function reorderStep(stepId, newOrder) {
                fetch('/workflow/steps/reorder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        'templateId': /*[[${template.templateId}]]*/ '',
                        'stepId': stepId,
                        'newOrder': newOrder
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // 成功后刷新页面
                    } else {
                        alert('调整步骤顺序失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('调整步骤顺序时发生错误:', error);
                    alert('调整步骤顺序时出错');
                });
            }
        });
    </script>
</body>
</html>
