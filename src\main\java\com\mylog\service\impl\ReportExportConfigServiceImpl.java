package com.mylog.service.impl;

import com.mylog.model.ReportExportConfig;
import com.mylog.repository.ReportExportConfigRepository;
import com.mylog.service.ReportExportConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ReportExportConfigServiceImpl implements ReportExportConfigService {

    private final ReportExportConfigRepository configRepository;

    @Override
    public Optional<ReportExportConfig> getConfig() {
        return Optional.ofNullable(configRepository.findFirstByOrderById());
    }

    @Override
    @Transactional
    public ReportExportConfig saveConfig(ReportExportConfig config) {
        return configRepository.save(config);
    }

    @Override
    @Transactional
    public void updateLastExportTime() {
        ReportExportConfig config = configRepository.findFirstByOrderById();
        if (config != null) {
            config.setLastExportTime(LocalDateTime.now());
            configRepository.save(config);
        }
    }

    @Override
    public boolean shouldExportToday() {
        ReportExportConfig config = configRepository.findFirstByOrderById();
        if (config == null || !config.isEnabled()) {
            return false;
        }

        DayOfWeek today = java.time.LocalDate.now().getDayOfWeek();

        switch (today) {
            case MONDAY:
                return config.isMonday();
            case TUESDAY:
                return config.isTuesday();
            case WEDNESDAY:
                return config.isWednesday();
            case THURSDAY:
                return config.isThursday();
            case FRIDAY:
                return config.isFriday();
            case SATURDAY:
                return config.isSaturday();
            case SUNDAY:
                return config.isSunday();
            default:
                return false;
        }
    }
}