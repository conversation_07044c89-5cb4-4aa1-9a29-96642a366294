package com.mylog.controller;

import com.mylog.model.ReportExportConfig;
import com.mylog.service.ReportExportConfigService;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.beans.PropertyEditorSupport;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Controller
@RequestMapping("/admin/report-export-config")
@RequiredArgsConstructor
public class AdminReportExportConfigController {

    private final ReportExportConfigService reportExportConfigService;

    @GetMapping
    public String showConfig(Model model) {
        // 获取配置，如果不存在则创建新的
        Optional<ReportExportConfig> configOpt = reportExportConfigService.getConfig();
        ReportExportConfig config = configOpt.orElse(new ReportExportConfig());

        model.addAttribute("config", config);
        model.addAttribute("activeMenu", "report-export-config");
        return "admin/report-export-config";
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 注册 LocalTime 类型的属性编辑器，只处理小时部分
        binder.registerCustomEditor(LocalTime.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                if (text == null || text.trim().isEmpty()) {
                    setValue(null);
                } else {
                    // 处理格式如 "14:00"
                    String[] parts = text.split(":");
                    if (parts.length > 0) {
                        try {
                            int hour = Integer.parseInt(parts[0]);
                            // 创建只有小时的 LocalTime，分钟和秒都设为 0
                            setValue(LocalTime.of(hour, 0, 0));
                        } catch (NumberFormatException e) {
                            setValue(null);
                        }
                    } else {
                        setValue(null);
                    }
                }
            }

            @Override
            public String getAsText() {
                LocalTime value = (LocalTime) getValue();
                if (value == null) {
                    return "";
                }
                return value.format(DateTimeFormatter.ofPattern("H:00"));
            }
        });
    }

    @PostMapping("/save")
    public String saveConfig(ReportExportConfig config, RedirectAttributes redirectAttributes) {
        // 获取现有配置，保留lastExportTime字段的值
        Optional<ReportExportConfig> existingConfigOpt = reportExportConfigService.getConfig();
        if (existingConfigOpt.isPresent()) {
            ReportExportConfig existingConfig = existingConfigOpt.get();
            // 保留最后导出时间
            config.setLastExportTime(existingConfig.getLastExportTime());
        }

        reportExportConfigService.saveConfig(config);
        redirectAttributes.addFlashAttribute("successMessage", "配置已保存");
        return "redirect:/admin/report-export-config";
    }
}