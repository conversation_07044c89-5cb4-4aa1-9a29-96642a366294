package com.mylog.controller;

import com.mylog.model.user.User;
import com.mylog.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 用户API控制器
 * 处理用户相关的REST API请求
 */
@RestController
@RequestMapping("/api/user")
public class UserApiController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserApiController.class);
    
    @Autowired
    private UserService userService;
      /**
     * 获取当前登录用户信息
     * 返回格式直接符合前端期望：{id: 1, username: "user", role: "ADMIN"}
     */
    @GetMapping(value = "/current", produces = "application/json")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Map<String, Object>> getCurrentUser() {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            
            logger.info("API请求：获取当前用户信息，用户名: {}", currentUsername);
            
            Optional<User> userOpt = userService.findUserByUsername(currentUsername);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                Map<String, Object> userData = new HashMap<>();
                userData.put("id", user.getUserId());
                userData.put("username", user.getUsername());
                userData.put("role", user.getRole().toString());
                
                logger.info("成功获取用户信息: ID={}, 用户名={}", user.getUserId(), user.getUsername());
                return ResponseEntity.ok(userData);
            } else {
                logger.warn("用户不存在: {}", currentUsername);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取当前用户信息时出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
