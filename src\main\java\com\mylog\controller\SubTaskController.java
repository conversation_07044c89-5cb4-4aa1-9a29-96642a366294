package com.mylog.controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.mylog.model.ProjectTask;
import com.mylog.model.SubTask;
import com.mylog.model.Submit2;
import com.mylog.service.OptionsService;
import com.mylog.service.SubTaskService;
import com.mylog.service.Submit2Service;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;

import jakarta.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/subtasks")
public class SubTaskController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SubTaskController.class);

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private OptionsService optionsService;

    @GetMapping("/new")
    public String newSubTaskForm(@RequestParam("taskId") Long taskId, Model model, HttpServletRequest request,
            jakarta.servlet.http.HttpServletResponse response) {
        // 添加响应头，防止页面被缓存
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");

        Optional<ProjectTask> taskOpt = taskService.findTaskById(taskId);
        if (!taskOpt.isPresent()) {
            return "redirect:/projects";
        }

        SubTask subTask = new SubTask();
        subTask.setTaskId(taskId);
        subTask.setSequenceNumber(subTaskService.getNextSequenceNumber(taskId));

        model.addAttribute("subTask", subTask);
        model.addAttribute("task", taskOpt.get());

        // 获取来源页面URL
        String referer = request.getHeader("Referer");
        model.addAttribute("referer", referer);

        // 添加当前页面URL到模型中，用于前端处理历史记录
        model.addAttribute("currentUrl", request.getRequestURL().toString() + "?taskId=" + taskId);

        return "subtasks/form";
    }

    @PostMapping("/save")
    public String saveSubTask(SubTask subTask, RedirectAttributes redirectAttributes,
            @RequestParam(value = "referer", required = false) String referer,
            jakarta.servlet.http.HttpServletResponse response) throws java.io.IOException {
        // 验证必填字段
        if (subTask.getTaskId() == null) {
            redirectAttributes.addFlashAttribute("error", "任务ID不能为空！");
            response.sendRedirect(referer != null && !referer.isEmpty() ? referer : "/projects");
            return null;
        }

        if (subTask.getLogContent() == null || subTask.getLogContent().trim().isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "日志内容不能为空！");
            response.sendRedirect("/tasks/" + subTask.getTaskId());
            return null;
        }

        try {
            // 始终设置创建时间，确保不会为空
            LocalDateTime now = LocalDateTime.now();
            subTask.setCreatedDateTime(now);

            // 设置创建者
            try {
                org.springframework.security.core.Authentication authentication = org.springframework.security.core.context.SecurityContextHolder
                        .getContext().getAuthentication();
                String currentUsername = authentication.getName();
                subTask.setCreatedBy(currentUsername);
            } catch (Exception e) {
                // 如果获取当前用户失败，设置一个默认值
                subTask.setCreatedBy("system");
            }

            // 保存子任务
            subTaskService.saveSubTask(subTask);

            // 采用activityLogService在这里子任务保存的日志
            try {
                // 记录活动日志
                Optional<com.mylog.model.user.User> currentUser = userService
                        .findUserByUsername(subTask.getCreatedBy());
                if (currentUser.isPresent()) {
                    com.mylog.model.user.User user = currentUser.get();
                    String ipAddress = getClientIpAddress();
                    // 获取任务名称，避免空指针异常
                    String taskName = "未知";
                    try {
                        if (subTask.getTask() != null) {
                            taskName = subTask.getTask().getTaskName();
                        } else {
                            // 如果task关联为null，尝试通过taskId查询任务
                            Optional<ProjectTask> task = taskService.findTaskById(subTask.getTaskId());
                            if (task.isPresent()) {
                                taskName = task.get().getTaskName();
                            }
                        }
                    } catch (Exception ex) {
                        logger.warn("获取任务名称失败: {}", ex.getMessage());
                    }

                    String description = String.format("为任务 [%s] 添加了评论", taskName);

                    activityLogService.logCreate(
                            user.getUserId(),
                            subTask.getCreatedBy(),
                            description,
                            ipAddress,
                            "Task",
                            subTask.getTaskId(),
                            getAccessType());
                }
            } catch (Exception e) {
                logger.error("记录活动日志时出错: {}", e.getMessage(), e);
            }

            // 更新父任务的最后评论日期和评论天数
            Optional<ProjectTask> taskOpt = taskService.findTaskById(subTask.getTaskId());
            if (taskOpt.isPresent()) {
                ProjectTask task = taskOpt.get();

                // 如果任务名称前两个字符以“11”或“12”开头，则获取最后的任务提交时间
                if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                    String prefix = task.getTaskName().substring(0, 2);
                    if ("11".equals(prefix) || "12".equals(prefix)) {
                        // 从Submit2Service获取该任务的所有提交记录
                        List<Submit2> submits = submit2Service.findAllSubmitsByTaskId(task.getTaskId());
                        // 找出包含"审批通过"的最后一次提交记录的时间
                        LocalDateTime lastSubmitTime = submits.stream()
                                .filter(submit -> submit.getRemarks() != null && submit.getRemarks().contains("审批通过"))
                                .map(Submit2::getSubmitDateTime)
                                .filter(time -> time != null)
                                .max(LocalDateTime::compareTo)
                                .orElse(task.getCreatedDateTime()); // 如果没有找到审批通过的记录，使用任务创建时间

                        // 更新最后评论时间
                        if (lastSubmitTime != null) {
                            task.setLastCommentDateTime(lastSubmitTime);
                        }

                    } else {
                        // 如果任务类型不是培训，则使用当前时间作为最后评论日期
                        task.setLastCommentDateTime(now);
                    }
                }
                // 更新评论天数
                List<ProjectTask> tasks = new ArrayList<>();
                tasks.add(task);
                taskService.calculateCommentDays(tasks);

                // 保存更新后的任务
                taskService.saveTask(task);
            }

            redirectAttributes.addFlashAttribute("message", "子任务保存成功！");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "保存子任务失败：" + e.getMessage());
            e.printStackTrace();
        }

        response.sendRedirect("/tasks/" + subTask.getTaskId());
        return null;
    }

    @PostMapping("/delete")
    public String deleteSubTask(@RequestParam("subTaskId") Long id,
            @RequestParam("taskId") Long taskId,
            RedirectAttributes redirectAttributes,
            jakarta.servlet.http.HttpServletResponse response) throws java.io.IOException {
        // 获取当前用户信息，检查是否是管理员
        org.springframework.security.core.Authentication authentication = org.springframework.security.core.context.SecurityContextHolder
                .getContext().getAuthentication();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));

        // 仅管理员可以删除评论
        if (!isAdmin) {
            redirectAttributes.addFlashAttribute("error", "只有管理员才能删除评论");
            response.sendRedirect("/tasks/" + taskId);
            return null;
        }

        // 执行删除操作
        subTaskService.deleteSubTask(id);
        redirectAttributes.addFlashAttribute("message", "评论删除成功！");

        response.sendRedirect("/tasks/" + taskId);
        return null;
    }

    /**
     * 显示评论管理页面
     */
    @GetMapping("/management")
    @PreAuthorize("hasRole('ADMIN') or authentication.name == '邓利鹏'")
    public String commentManagement(Model model,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        logger.info("加载评论管理页面，页码: {}, 每页大小: {}", page, size);

        try {
            // 设置分页，按创建时间降序排序
            Pageable pageable = PageRequest.of(page, size, Sort.by("CreatedDate").descending());

            // 获取分页的评论列表
            Page<SubTask> commentPage = subTaskService.findAllSubTasksPaged(pageable);

            // 处理任务名称显示格式，添加任务ID
            processTaskNameDisplay(commentPage.getContent());

            logger.info("成功获取评论列表，总数: {}, 当前页: {}, 每页大小: {}",
                       commentPage.getTotalElements(), page, size);

            // 添加到模型
            model.addAttribute("commentPage", commentPage);
            model.addAttribute("activeMenu", "commentmanagement");
            model.addAttribute("totalComments", commentPage.getTotalElements());

            // 添加人员列表到模型中，用于创建人下拉选择
            List<String> personnelList = optionsService.getPersonnel();
            logger.info("评论管理页面 - 获取到的人员数据数量: {}", personnelList.size());
            if (!personnelList.isEmpty()) {
                logger.info("评论管理页面 - 前几个人员: {}", personnelList.subList(0, Math.min(3, personnelList.size())));
            }
            model.addAttribute("personnel", personnelList);

            return "comments/management";

        } catch (Exception e) {
            logger.error("加载评论管理页面时出错: {}", e.getMessage(), e);
            model.addAttribute("error", "加载评论列表失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 高级搜索评论
     */
    @GetMapping("/advanced-search")
    @PreAuthorize("hasRole('ADMIN') or authentication.name == '邓利鹏'")
    public String advancedSearchComments(
            @RequestParam(required = false) List<String> fieldNames,
            HttpServletRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Model model) {

        logger.info("接收到评论高级搜索请求");
        logger.info("字段列表: {}", fieldNames);
        logger.info("请求参数: {}", request.getParameterMap());

        // 如果没有指定字段，则返回所有评论
        if (fieldNames == null || fieldNames.isEmpty()) {
            logger.info("没有搜索字段，返回所有评论");
            return commentManagement(model, page, size);
        }

        // 构建搜索条件
        Map<String, String> searchCriteria = new HashMap<>();

        for (String fieldName : fieldNames) {
            if (fieldName == null || fieldName.trim().isEmpty()) {
                continue;
            }

            logger.info("处理搜索字段: {}", fieldName);

            // 处理日期范围搜索
            if ("createdDate".equals(fieldName)) {
                String startDate = request.getParameter("field_createdDate_start");
                String endDate = request.getParameter("field_createdDate_end");

                logger.info("日期范围搜索 - 开始: {}, 结束: {}", startDate, endDate);

                if (startDate != null && !startDate.trim().isEmpty()) {
                    searchCriteria.put("createdDate_start", startDate.trim());
                    logger.info("添加创建时间开始条件: {}", startDate.trim());
                }

                if (endDate != null && !endDate.trim().isEmpty()) {
                    searchCriteria.put("createdDate_end", endDate.trim());
                    logger.info("添加创建时间结束条件: {}", endDate.trim());
                }
            } else {
                // 处理普通字段
                String value = request.getParameter("field_" + fieldName);
                logger.info("字段 {} 的值: {}", fieldName, value);

                if (value != null && !value.trim().isEmpty()) {
                    searchCriteria.put(fieldName, value.trim());
                    logger.info("添加搜索条件: {} = {}", fieldName, value.trim());
                }
            }
        }

        logger.info("最终搜索条件: {}", searchCriteria);

        // 如果没有有效的查询条件，则返回所有评论
        if (searchCriteria.isEmpty()) {
            logger.info("没有有效的搜索条件，返回所有评论");
            return commentManagement(model, page, size);
        }

        try {
            logger.info("执行动态搜索，条件数量: {}", searchCriteria.size());
            Pageable pageable = PageRequest.of(page, size, Sort.by("CreatedDate").descending());
            Page<SubTask> commentPage = subTaskService.dynamicSearchSubTasks(searchCriteria, pageable);

            // 处理任务名称显示格式，添加任务ID
            processTaskNameDisplay(commentPage.getContent());

            logger.info("搜索结果: 总数 = {}, 当前页评论数 = {}",
                       commentPage.getTotalElements(),
                       commentPage.getContent().size());

            // 添加到模型
            model.addAttribute("commentPage", commentPage);
            model.addAttribute("activeMenu", "commentmanagement");
            model.addAttribute("totalComments", commentPage.getTotalElements());
            model.addAttribute("isAdvancedSearch", true);
            model.addAttribute("searchCriteria", searchCriteria);

            // 添加人员列表到模型中，用于创建人下拉选择
            List<String> personnelList = optionsService.getPersonnel();
            logger.info("评论高级搜索页面 - 获取到的人员数据数量: {}", personnelList.size());
            if (!personnelList.isEmpty()) {
                logger.info("评论高级搜索页面 - 前几个人员: {}", personnelList.subList(0, Math.min(3, personnelList.size())));
            }
            model.addAttribute("personnel", personnelList);

            return "comments/management";

        } catch (Exception e) {
            logger.error("搜索评论时出错: {}", e.getMessage(), e);
            model.addAttribute("error", "搜索评论失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 处理任务名称显示格式，在任务名称后添加任务ID
     * 避免重复添加ID号的问题
     */
    private void processTaskNameDisplay(List<SubTask> subTasks) {
        for (SubTask subTask : subTasks) {
            if (subTask.getTask() != null && subTask.getTask().getTaskName() != null) {
                String originalTaskName = subTask.getTask().getTaskName();

                // 检查任务名称是否已经包含ID号，避免重复添加
                String idPattern = "（ID:" + subTask.getTaskId() + "）";
                if (!originalTaskName.contains(idPattern)) {
                    // 同时检查是否已经包含其他格式的ID号，如果有则先移除
                    String cleanTaskName = originalTaskName.replaceAll("（ID:\\d+）", "");
                    String taskNameWithId = cleanTaskName + "（ID:" + subTask.getTaskId() + "）";
                    subTask.getTask().setTaskName(taskNameWithId);
                }
            }
        }
    }
}