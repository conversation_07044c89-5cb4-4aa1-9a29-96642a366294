<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('待审批任务')}">
    <meta charset="UTF-8">
    <title>待审批任务</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">待审批任务</h1>
            <div>
                <a th:href="@{/workflow/my}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
            </div>
        </div>

        <!-- 待办任务列表 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">待审批任务列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 成功/错误消息 -->
                        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <span th:text="${message}">操作成功</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                        <!-- 待审批任务表格 - 替换为my-workflow.html中的对应代码 -->
                        <div class="table-responsive" th:if="${!todoPage.empty}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>流程标题</th>
                                        <th>流程模板</th>
                                        <th>业务ID</th>
                                        <th>发起人</th>
                                        <th>流程进度</th>
                                        <th>提交时间</th>
                                        <th>待审批人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${todoPage.content}">
                                        <td th:text="${task.title}">流程标题</td>
                                        <td th:text="${task.template.templateName}">流程模板</td>
                                        <td>
                                            <!-- 如果业务类型为任务，则显示为任务链接 -->
                                            <a th:if="${task.businessId != null && task.businessType == '任务'}" 
                                               th:href="@{/tasks/{id}(id=${task.businessId})}"
                                               th:text="${task.businessId}">123</a>
                                            <!-- 如果业务类型不是任务或为空，则显示普通文本 -->
                                            <span th:if="${task.businessId != null && task.businessType != '任务'}" 
                                                  th:text="${task.businessId}">123</span>
                                            <span th:unless="${task.businessId != null}">-</span>
                                        </td>
                                        <td th:text="${task.initiator}">发起人</td>
                                        <td>
                                            <!-- 流程进度显示 -->
                                            <span class="badge bg-info" th:text="${(instanceSteps != null && instanceSteps[task.instanceId] != null ? instanceSteps[task.instanceId] : 0) + 
                                            '/' + (task.stepCount != null ? task.stepCount : 0) + ' 步'}">0/0 步</span>
                                        </td>
                                        <td th:text="${task.submittedDateTime != null ? #temporals.format(task.submittedDateTime, 'yyyy-MM-dd HH:mm') : '-'}">提交时间</td>
                                        <td>
                                            <span th:if="${task.currentApprover == null}">-</span>
                                            <span th:if="${task.currentApprover != null}">
                                                <!-- 如果是用户名，直接显示 -->
                                                <span th:if="${!task.currentApprover.contains(';') && !task.currentApprover.contains(',')}" 
                                                      th:text="${task.currentApprover}"></span>
                                                <!-- 如果是多个用户（以分号或逗号分隔），显示"多人审批" -->
                                                <span th:if="${task.currentApprover.contains(';') || task.currentApprover.contains(',')}" 
                                                      title="多人审批" th:attr="data-approvers=${task.currentApprover}">多人审批</span>
                                            </span>
                                        </td>                                        <td>
                                            <a th:href="@{/workflow/approval/{id}(id=${task.instanceId})}" class="btn btn-sm btn-primary" target="_blank">
                                                <i class="bi bi-check-lg"></i> 审批
                                            </a>
                                            <a th:href="@{/workflow/instances/{id}(id=${task.instanceId})}" class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-eye"></i> 查看
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${todoPage.empty}">
                            <p class="text-muted mb-0">暂无待审批任务</p>
                        </div>
                    </div>
                    <!-- 分页控件 -->
                    <div class="card-footer" th:if="${todoPage.totalPages > 0}">
                        <div th:replace="~{fragments/pagination :: pagination(${todoPage}, @{/workflow/instances/todo})}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
