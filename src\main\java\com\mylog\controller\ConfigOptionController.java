package com.mylog.controller;

import com.mylog.model.config.ConfigOption;
import com.mylog.service.ConfigOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/config-options")
public class ConfigOptionController {
    
    @Autowired
    private ConfigOptionService configOptionService;
    
    /**
     * 获取指定类别的配置选项
     */
    @GetMapping("/category/{category}")
    public ResponseEntity<List<ConfigOption>> getConfigOptionsByCategory(@PathVariable String category) {
        List<ConfigOption> options = configOptionService.getConfigOptionsByCategory(category);
        return ResponseEntity.ok(options);
    }
    
    /**
     * 获取指定类别和值的配置选项
     */
    @GetMapping("/category/{category}/value/{value}")
    public ResponseEntity<ConfigOption> getConfigOption(@PathVariable String category, @PathVariable String value) {
        Optional<ConfigOption> option = configOptionService.getConfigOption(category, value);
        return option.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建新的配置选项
     */
    @PostMapping
    public ResponseEntity<ConfigOption> createConfigOption(@RequestBody ConfigOption configOption) {
        ConfigOption saved = configOptionService.saveConfigOption(configOption);
        return ResponseEntity.ok(saved);
    }
    
    /**
     * 添加配置选项（带ratio和remark）
     */
    @PostMapping("/add")
    public ResponseEntity<ConfigOption> addConfigOption(
            @RequestParam String category,
            @RequestParam String value,
            @RequestParam(required = false) Double ratio,
            @RequestParam(required = false) String remark) {
        ConfigOption saved = configOptionService.addConfigOption(category, value, ratio, remark);
        return ResponseEntity.ok(saved);
    }
    
    /**
     * 更新配置选项
     */
    @PutMapping
    public ResponseEntity<ConfigOption> updateConfigOption(@RequestBody ConfigOption configOption) {
        ConfigOption updated = configOptionService.saveConfigOption(configOption);
        return ResponseEntity.ok(updated);
    }
    
    /**
     * 删除配置选项
     */
    @DeleteMapping("/category/{category}/value/{value}")
    public ResponseEntity<Void> deleteConfigOption(@PathVariable String category, @PathVariable String value) {
        configOptionService.deleteConfigOption(category, value);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 删除指定类别的所有配置选项
     */
    @DeleteMapping("/category/{category}")
    public ResponseEntity<Void> deleteConfigOptionsByCategory(@PathVariable String category) {
        configOptionService.deleteConfigOptionsByCategory(category);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取所有类别
     */
    @GetMapping("/categories")
    public ResponseEntity<List<String>> getAllCategories() {
        List<String> categories = configOptionService.getAllCategories();
        return ResponseEntity.ok(categories);
    }
}
