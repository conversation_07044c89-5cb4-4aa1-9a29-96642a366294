-- 修复日历相关表的时间字段，从英国时间（UTC）转换为本地时间（UTC+8）
-- 执行此脚本前请先备份数据库

-- 开始事务
BEGIN TRANSACTION;

-- 1. 修复 calendars 表的时间字段
UPDATE calendars 
SET created_time = datetime(created_time, '+8 hours'),
    updated_time = datetime(updated_time, '+8 hours')
WHERE created_time IS NOT NULL;

-- 2. 修复 calendar_events 表的时间字段
UPDATE calendar_events 
SET start_time = datetime(start_time, '+8 hours'),
    end_time = CASE 
        WHEN end_time IS NOT NULL THEN datetime(end_time, '+8 hours')
        ELSE NULL 
    END,
    created_time = datetime(created_time, '+8 hours'),
    updated_time = CASE 
        WHEN updated_time IS NOT NULL THEN datetime(updated_time, '+8 hours')
        ELSE NULL 
    END,
    recurrence_end_date = CASE 
        WHEN recurrence_end_date IS NOT NULL THEN datetime(recurrence_end_date, '+8 hours')
        ELSE NULL 
    END
WHERE start_time IS NOT NULL;

-- 3. 修复 event_reminders 表的时间字段
UPDATE event_reminders 
SET reminder_time = datetime(reminder_time, '+8 hours'),
    created_time = datetime(created_time, '+8 hours')
WHERE reminder_time IS NOT NULL;

-- 提交事务
COMMIT;

-- 验证修复结果
SELECT '=== 修复后的时间数据示例 ===';

SELECT '--- calendars 表 ---';
SELECT id, name, created_time, updated_time 
FROM calendars 
ORDER BY id 
LIMIT 5;

SELECT '--- calendar_events 表 ---';
SELECT id, title, start_time, end_time, created_time, updated_time 
FROM calendar_events 
ORDER BY id 
LIMIT 5;

SELECT '--- event_reminders 表 ---';
SELECT id, reminder_time, created_time 
FROM event_reminders 
ORDER BY id 
LIMIT 5;

SELECT '=== 修复完成 ===';
