package com.mylog.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mylog.model.CalendarEvent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 日历事件DTO
 */
public class CalendarEventDTO {
    
    private Long id;
    
    @NotBlank(message = "事件标题不能为空")
    private String title;
    
    private String description;
      @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    private Boolean isAllDay = false;
    
    private String location;
    
    private CalendarEvent.EventType eventType = CalendarEvent.EventType.MEETING;
    
    private CalendarEvent.Priority priority = CalendarEvent.Priority.NORMAL;
    
    private Boolean isRecurring = false;
    
    private CalendarEvent.RecurrenceType recurrenceType;
    
    private Integer recurrenceInterval;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recurrenceEndDate;
    
    @NotNull(message = "日历ID不能为空")
    private Long calendarId;
    
    private String calendarName;
    
    private String calendarColor;
    
    private Long creatorId;
      @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;    private List<EventReminderDTO> reminders = new ArrayList<>();
    
    // 构造函数
    public CalendarEventDTO() {}
      public CalendarEventDTO(CalendarEvent event) {
        this.id = event.getId();
        this.title = event.getTitle();
        this.description = event.getDescription();
        this.startTime = event.getStartTime();
        this.endTime = event.getEndTime();
        this.isAllDay = event.getIsAllDay();
        this.location = event.getLocation();
        this.eventType = event.getEventType();
        this.priority = event.getPriority();
        this.isRecurring = event.getIsRecurring();
        this.recurrenceType = event.getRecurrenceType();
        this.recurrenceInterval = event.getRecurrenceInterval();
        this.recurrenceEndDate = event.getRecurrenceEndDate();
        this.calendarId = event.getCalendar().getId();
        this.calendarName = event.getCalendar().getName();
        this.calendarColor = event.getCalendar().getColor();
        this.creatorId = event.getCreatorId();
        this.createdTime = event.getCreatedTime();
        this.updatedTime = event.getUpdatedTime();
        
        // 加载提醒信息
        if (event.getReminders() != null && !event.getReminders().isEmpty()) {
            this.reminders = event.getReminders().stream()
                .map(EventReminderDTO::new)
                .collect(java.util.stream.Collectors.toList());
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Boolean getIsAllDay() {
        return isAllDay;
    }
    
    public void setIsAllDay(Boolean isAllDay) {
        this.isAllDay = isAllDay;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public CalendarEvent.EventType getEventType() {
        return eventType;
    }
    
    public void setEventType(CalendarEvent.EventType eventType) {
        this.eventType = eventType;
    }
    
    public CalendarEvent.Priority getPriority() {
        return priority;
    }
    
    public void setPriority(CalendarEvent.Priority priority) {
        this.priority = priority;
    }
    
    public Boolean getIsRecurring() {
        return isRecurring;
    }
    
    public void setIsRecurring(Boolean isRecurring) {
        this.isRecurring = isRecurring;
    }
    
    public CalendarEvent.RecurrenceType getRecurrenceType() {
        return recurrenceType;
    }
    
    public void setRecurrenceType(CalendarEvent.RecurrenceType recurrenceType) {
        this.recurrenceType = recurrenceType;
    }
    
    public Integer getRecurrenceInterval() {
        return recurrenceInterval;
    }
    
    public void setRecurrenceInterval(Integer recurrenceInterval) {
        this.recurrenceInterval = recurrenceInterval;
    }
    
    public LocalDateTime getRecurrenceEndDate() {
        return recurrenceEndDate;
    }
    
    public void setRecurrenceEndDate(LocalDateTime recurrenceEndDate) {
        this.recurrenceEndDate = recurrenceEndDate;
    }
    
    public Long getCalendarId() {
        return calendarId;
    }
    
    public void setCalendarId(Long calendarId) {
        this.calendarId = calendarId;
    }
    
    public String getCalendarName() {
        return calendarName;
    }
    
    public void setCalendarName(String calendarName) {
        this.calendarName = calendarName;
    }
    
    public String getCalendarColor() {
        return calendarColor;
    }
    
    public void setCalendarColor(String calendarColor) {
        this.calendarColor = calendarColor;
    }
    
    public Long getCreatorId() {
        return creatorId;
    }
    
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public List<EventReminderDTO> getReminders() {
        return reminders;
    }
      public void setReminders(List<EventReminderDTO> reminders) {
        this.reminders = reminders;
    }
}
