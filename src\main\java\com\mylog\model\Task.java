package com.mylog.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

// 如果这个类只是用来添加新列，不需要定义为实体
// 去掉@Entity注解
// @Entity
// @Table(name = "Tasks")
public class Task {

    @Column(name = "last_comment_date")
    private LocalDateTime lastCommentDate;

    public LocalDateTime getLastCommentDate() {
        return lastCommentDate;
    }

    public void setLastCommentDate(LocalDateTime lastCommentDate) {
        this.lastCommentDate = lastCommentDate;
    }
} 