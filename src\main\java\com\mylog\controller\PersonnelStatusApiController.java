package com.mylog.controller;

import com.mylog.model.PersonnelStatus;
import com.mylog.service.PersonnelStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/personnel-status")
public class PersonnelStatusApiController {

    private static final Logger logger = LoggerFactory.getLogger(PersonnelStatusApiController.class);
    
    @Autowired
    private PersonnelStatusService personnelStatusService;
    
    /**
     * 获取所有人员状态
     */
    @GetMapping
    public ResponseEntity<List<PersonnelStatus>> getAllPersonnelStatus() {
        List<PersonnelStatus> statusList = personnelStatusService.getAllPersonnelStatus();
        return ResponseEntity.ok(statusList);
    }
    
    /**
     * 获取所有状态选项
     */
    @GetMapping("/status-options")
    public ResponseEntity<List<String>> getStatusOptions() {
        List<String> statusOptions = personnelStatusService.getAllDistinctStatuses();
        return ResponseEntity.ok(statusOptions);
    }
    
    /**
     * 获取所有地点选项
     */
    @GetMapping("/location-options")
    public ResponseEntity<List<String>> getLocationOptions() {
        List<String> locationOptions = personnelStatusService.getAllDistinctLocations();
        return ResponseEntity.ok(locationOptions);
    }
    
    /**
     * 获取人员状态统计
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatusStatistics() {
        List<PersonnelStatus> allStatus = personnelStatusService.getAllPersonnelStatus();
        
        // 按状态分组统计
        Map<String, Long> statusCounts = allStatus.stream()
                .collect(Collectors.groupingBy(
                        PersonnelStatus::getStatus,
                        Collectors.counting()
                ));
        
        // 按地点分组统计
        Map<String, Long> locationCounts = allStatus.stream()
                .collect(Collectors.groupingBy(
                        PersonnelStatus::getLocation,
                        Collectors.counting()
                ));
        
        Map<String, Object> result = new HashMap<>();
        result.put("total", allStatus.size());
        result.put("statusCounts", statusCounts);
        result.put("locationCounts", locationCounts);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据ID获取人员状态
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getPersonnelStatus(@PathVariable Long id) {
        return personnelStatusService.getPersonnelStatusById(id)
                .map(status -> ResponseEntity.ok(status))
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据名称获取人员状态
     */
    @GetMapping("/by-name/{name}")
    public ResponseEntity<?> getPersonnelStatusByName(@PathVariable String name) {
        return personnelStatusService.getPersonnelStatusByName(name)
                .map(status -> ResponseEntity.ok(status))
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 更新人员状态
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updatePersonnelStatus(
            @PathVariable Long id,
            @RequestParam String status,
            @RequestParam String location) {
        
        try {
            PersonnelStatus updated = personnelStatusService.updatePersonnelStatus(id, status, location);
            if (updated != null) {
                return ResponseEntity.ok(updated);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("更新人员状态时出错: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("更新人员状态失败: " + e.getMessage());
        }
    }
}
