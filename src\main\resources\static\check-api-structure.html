<!DOCTYPE html>
<html>
<head>
    <title>API数据结构检查</title>
</head>
<body>
    <h2>API数据结构检查</h2>
    <div id="output"></div>

    <script>
        async function checkAPIStructure() {
            try {
                const response = await fetch('/api/check-ins/user/admin/pending');
                const data = await response.json();
                
                const output = document.getElementById('output');
                
                if (data.success && data.data && data.data.length > 0) {
                    const firstEvent = data.data[0];
                    output.innerHTML = `
                        <h3>API返回的第一个事件数据结构：</h3>
                        <pre>${JSON.stringify(firstEvent, null, 2)}</pre>
                        
                        <h3>重要字段检查：</h3>
                        <p>事件标题: ${firstEvent.eventTitle}</p>
                        <p>事件ID: ${firstEvent.eventId}</p>
                        <p>提醒信息: ${JSON.stringify(firstEvent.eventReminder, null, 2)}</p>
                        <p>提醒时间: ${firstEvent.eventReminder?.reminderTime}</p>
                        <p>提醒ID: ${firstEvent.eventReminder?.id}</p>
                        <p>需要签到: ${firstEvent.eventReminder?.requiresCheckIn}</p>
                    `;
                } else {
                    output.innerHTML = `
                        <p>没有数据或API调用失败</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                document.getElementById('output').innerHTML = `
                    <p>错误: ${error.message}</p>
                `;
            }
        }
        
        checkAPIStructure();
    </script>
</body>
</html>
