package com.mylog.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.mylog.service.UserService;

/**
 * 签到页面控制器
 */
@Controller
@RequestMapping("/check-in")
public class CheckInController {
    
    private static final Logger logger = LoggerFactory.getLogger(CheckInController.class);
    
    @Autowired
    private UserService userService;
    
    /**
     * 签到主页面
     */
    @GetMapping
    public String checkInPage(Model model) {
        logger.info("访问签到页面");
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            logger.info("当前用户: {}", currentUsername);
              // 获取用户信息并设置主题
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
                model.addAttribute("userTheme", "theme-" + theme);
                // 添加当前用户信息供前端使用
                model.addAttribute("currentUser", user.getUsername());
                model.addAttribute("currentUserId", user.getUserId());
                logger.info("用户主题: {}, 用户ID: {}", theme, user.getUserId());
            });
            
            // 设置活动菜单
            model.addAttribute("activeMenu", "checkin");
            
            logger.info("成功加载签到页面");
            return "checkin/index";
            
        } catch (Exception e) {
            logger.error("加载签到页面时出错: {}", e.getMessage(), e);
            model.addAttribute("error", "加载签到页面失败: " + e.getMessage());
            model.addAttribute("activeMenu", "checkin");
            return "error/general";
        }
    }
}
