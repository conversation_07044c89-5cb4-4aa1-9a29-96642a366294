package com.mylog.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mylog.service.OptionsService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试用的API控制器，用于检查人员数据
 */
@RestController
@RequestMapping("/api/debug")
public class DebugController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebugController.class);
    
    @Autowired
    private OptionsService optionsService;
    
    /**
     * 调试人员数据加载
     */
    @GetMapping("/personnel")
    public Map<String, Object> getPersonnelDebug() {
        logger.info("调试API：获取人员数据");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<String> personnel = optionsService.getPersonnel();
            
            result.put("success", true);
            result.put("personnel", personnel);
            result.put("count", personnel.size());
            
            logger.info("人员数据获取成功，数量: {}", personnel.size());
            if (!personnel.isEmpty()) {
                logger.info("前几个人员: {}", personnel.subList(0, Math.min(5, personnel.size())));
            }
            
        } catch (Exception e) {
            logger.error("获取人员数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("personnel", java.util.Collections.emptyList());
            result.put("count", 0);
        }
        
        return result;
    }
    
    /**
     * 调试所有选项数据
     */
    @GetMapping("/all-options")
    public Map<String, Object> getAllOptionsDebug() {
        logger.info("调试API：获取所有选项数据");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, List<String>> options = new HashMap<>();
            options.put("personnel", optionsService.getPersonnel());
            options.put("salesPersonnel", optionsService.getSalesPersonnel());
            options.put("mechanicalPersonnel", optionsService.getMechanicalPersonnel());
            options.put("electricalPersonnel", optionsService.getElectricalPersonnel());
            options.put("visionTypes", optionsService.getVisionTypes());
            
            result.put("success", true);
            result.put("options", options);
            
            // 记录每个选项的数量
            options.forEach((key, value) -> {
                logger.info("选项 {} 数量: {}", key, value.size());
            });
            
        } catch (Exception e) {
            logger.error("获取选项数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
