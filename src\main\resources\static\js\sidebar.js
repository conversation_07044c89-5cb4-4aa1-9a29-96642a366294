/**
 * 侧边栏响应式控制
 * 处理移动端侧边栏的显示/隐藏，以及遮罩层的控制
 * 增加侧边栏收缩功能
 * 增加点击主菜单项时收起/展开子菜单的功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Bootstrap的Tooltip组件
    initBootstrapTooltips();

    const sidebarToggle = document.getElementById('sidebarToggle');
    const closeSidebar = document.getElementById('closeSidebar');
    const sidebar = document.getElementById('sidebar');
    const sidebarCollapseBtn = document.getElementById('sidebarCollapseBtn');
    const mainContent = document.querySelector('main');

    // 从 localStorage 中获取侧边栏状态
    const sidebarState = localStorage.getItem('sidebarCollapsed');

    if (sidebar) {
        // 如果有保存的状态，应用它
        if (sidebarState === 'true') {
            sidebar.classList.add('collapsed');
            // 对所有页面布局都进行处理
            applyMainContentCollapsedState(true);
            // 移除初始化类，因为现在已经应用了真正的状态
            document.documentElement.classList.remove('sidebar-init-collapsed');
        }

        // 点击切换按钮打开侧边栏
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                sidebar.classList.toggle('show');
                document.body.classList.toggle('sidebar-open');
            });
        }

        // 侧边栏收缩按钮点击事件
        if (sidebarCollapseBtn) {
            sidebarCollapseBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 在切换前先移除初始化类，避免样式冲突
                document.documentElement.classList.remove('sidebar-init-collapsed');

                // 切换侧边栏状态
                sidebar.classList.toggle('collapsed');
                const isCollapsed = sidebar.classList.contains('collapsed');

                // 应用主内容区域的收缩状态
                applyMainContentCollapsedState(isCollapsed);

                // 保存状态到 localStorage
                localStorage.setItem('sidebarCollapsed', isCollapsed);

                // 当侧边栏收缩时，添加工具提示
                updateTooltips();

                // 处理子菜单的显示状态
                handleSubmenuVisibility(isCollapsed);

                // 重新初始化工具提示
                initBootstrapTooltips();
            });
        }

        // 更新工具提示函数
        function updateTooltips() {
            const navLinks = sidebar.querySelectorAll('.nav-link');
            const isCollapsed = sidebar.classList.contains('collapsed');

            navLinks.forEach(link => {
                const spanText = link.querySelector('span');
                if (spanText) {
                    if (isCollapsed) {
                        // 当侧边栏收缩时，添加工具提示
                        link.setAttribute('title', spanText.textContent);
                        link.setAttribute('data-bs-toggle', 'tooltip');
                        link.setAttribute('data-bs-placement', 'right');
                    } else {
                        // 当侧边栏展开时，移除工具提示
                        link.removeAttribute('title');
                        link.removeAttribute('data-bs-toggle');
                        link.removeAttribute('data-bs-placement');
                    }
                }
            });

            // 初始化工具提示
            if (typeof bootstrap !== 'undefined' && isCollapsed) {
                const tooltipTriggerList = [].slice.call(sidebar.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        }

        // 初始化时调用一次更新工具提示
        updateTooltips();

        // 初始化工具提示
        initBootstrapTooltips();

        // 初始化箭头图标状态
        initArrowIcons();

        // 初始化子菜单显示状态
        const isCollapsed = sidebar.classList.contains('collapsed');
        handleSubmenuVisibility(isCollapsed);

        // 使用jQuery处理菜单点击事件
        if (typeof $ !== 'undefined') {
            // 首先移除现有的点击事件处理程序
            $('.toggle-submenu').off('click');

            // 添加新的点击事件处理程序
            $('.toggle-submenu').on('click', function(e) {
                // 获取目标子菜单ID
                const targetId = $(this).data('bs-target');
                const targetSubmenu = $(targetId);
                const arrow = $(this).find('.bi-chevron-up, .bi-chevron-down');

                // 阻止默认的Bootstrap切换行为
                e.preventDefault();
                e.stopPropagation();

                // 切换子菜单的显示状态
                targetSubmenu.toggleClass('show');

                // 更新箭头图标
                if (targetSubmenu.hasClass('show')) {
                    arrow.removeClass('bi-chevron-down').addClass('bi-chevron-up');
                } else {
                    arrow.removeClass('bi-chevron-up').addClass('bi-chevron-down');
                }
            });
        } else {
            // 如果jQuery不可用，则使用原生 JavaScript
            const toggleMenuItems = sidebar.querySelectorAll('.toggle-submenu');
            toggleMenuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 获取目标子菜单ID
                    const targetId = this.getAttribute('data-bs-target');
                    const targetSubmenu = document.querySelector(targetId);

                    // 如果子菜单已经展开，则先收起它
                    if (targetSubmenu && targetSubmenu.classList.contains('show')) {
                        // 阻止默认的Bootstrap切换行为
                        e.preventDefault();
                        e.stopPropagation();

                        // 手动关闭子菜单
                        targetSubmenu.classList.remove('show');

                        // 更新箭头图标
                        const arrow = this.querySelector('.bi-chevron-up');
                        if (arrow) {
                            arrow.classList.remove('bi-chevron-up');
                            arrow.classList.add('bi-chevron-down');
                        }
                    }
                });
            });
        }

        // 点击关闭按钮关闭侧边栏
        if (closeSidebar) {
            closeSidebar.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            });
        }

        // 点击内容区域或遮罩层关闭侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth < 768 &&
                sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                e.target !== sidebarToggle) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        });

        // 添加移动端手势支持
        let touchStartX = 0;
        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });

        document.addEventListener('touchend', function(e) {
            const touchEndX = e.changedTouches[0].screenX;
            const diff = touchStartX - touchEndX;

            // 左滑关闭侧边栏
            if (diff > 50 && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }

            // 从屏幕左边缘右滑打开侧边栏
            if (touchStartX < 30 && diff < -50) {
                sidebar.classList.add('show');
                document.body.classList.add('sidebar-open');
            }
        }, { passive: true });

        // 窗口大小改变时处理
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        });
    }
});

/**
 * 初始化Bootstrap的Tooltip组件
 */
function initBootstrapTooltips() {
    if (typeof bootstrap !== 'undefined') {
        // 销毁现有的工具提示
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(el => {
            const tooltip = bootstrap.Tooltip.getInstance(el);
            if (tooltip) {
                tooltip.dispose();
            }
        });

        // 初始化新的工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * 应用主内容区域的收缩状态
 * @param {boolean} isCollapsed 是否收缩
 */
function applyMainContentCollapsedState(isCollapsed) {
    // 处理所有可能的主内容区域
    const mainContents = document.querySelectorAll('main');
    mainContents.forEach(main => {
        if (isCollapsed) {
            main.classList.add('sidebar-collapsed');
        } else {
            main.classList.remove('sidebar-collapsed');
        }
    });
}

/**
 * 处理侧边栏收缩时子菜单的显示状态
 * @param {boolean} isCollapsed 是否收缩
 */
function handleSubmenuVisibility(isCollapsed) {
    // 获取所有子菜单
    const submenus = document.querySelectorAll('.collapse');

    // 确保项目管理和任务管理页面中的搜索和数据列表卡片保持展开状态
    const keepExpandedSelectors = [
        '#searchCollapse',  // 搜索卡片
        '#projectListCollapse',  // 项目列表卡片
        '#taskListCollapse'  // 任务列表卡片
    ];

    if (typeof $ !== 'undefined') {
        // 使用jQuery
        if (isCollapsed) {
            // 当侧边栏收缩时，隐藏所有子菜单，但保持特定卡片展开
            $('.collapse').not(keepExpandedSelectors.join(',')).removeClass('show');

            // 为主菜单项添加悬停事件，当悬停时显示子菜单
            $('.toggle-submenu').each(function() {
                const $this = $(this);
                const targetId = $this.data('bs-target');

                // 移除现有的悬停事件
                $this.off('mouseenter mouseleave');

                // 添加新的悬停事件
                $this.hover(
                    function() {
                        // 鼠标进入时，显示子菜单
                        $(targetId).addClass('submenu-hover').css({
                            'position': 'absolute',
                            'left': '100%',
                            'top': $(this).position().top,
                            'z-index': 1000,
                            'min-width': '200px',
                            'background-color': '#343a40',
                            'border-radius': '0 4px 4px 0',
                            'box-shadow': '0 0 10px rgba(0,0,0,0.2)',
                            'display': 'block'
                        });
                    },
                    function() {
                        // 鼠标离开时，隐藏子菜单
                        $(targetId).removeClass('submenu-hover').css('display', 'none');
                    }
                );

                // 为子菜单添加悬停事件，防止鼠标移到子菜单上时子菜单消失
                $(targetId).hover(
                    function() {
                        // 鼠标进入子菜单时，保持显示
                        $(this).css('display', 'block');
                    },
                    function() {
                        // 鼠标离开子菜单时，隐藏子菜单
                        $(this).css('display', 'none');
                    }
                );
            });
        } else {
            // 当侧边栏展开时，移除悬停事件和样式
            $('.toggle-submenu').off('mouseenter mouseleave');
            $('.collapse').off('mouseenter mouseleave').css({
                'position': '',
                'left': '',
                'top': '',
                'z-index': '',
                'min-width': '',
                'background-color': '',
                'border-radius': '',
                'box-shadow': '',
                'display': ''
            });
        }
    } else {
        // 使用原生 JavaScript
        if (isCollapsed) {
            // 当侧边栏收缩时，隐藏所有子菜单，但保持特定卡片展开
            submenus.forEach(submenu => {
                // 检查是否是需要保持展开的卡片
                const shouldKeepExpanded = keepExpandedSelectors.some(selector =>
                    submenu.matches(selector));

                if (!shouldKeepExpanded) {
                    submenu.classList.remove('show');
                }
            });

            // 为主菜单项添加悬停事件
            const toggleMenuItems = document.querySelectorAll('.toggle-submenu');
            toggleMenuItems.forEach(item => {
                const targetId = item.getAttribute('data-bs-target');
                const targetSubmenu = document.querySelector(targetId);

                if (!targetSubmenu) return;

                // 添加鼠标进入事件
                item.addEventListener('mouseenter', function() {
                    targetSubmenu.style.position = 'absolute';
                    targetSubmenu.style.left = '100%';
                    targetSubmenu.style.top = this.getBoundingClientRect().top + 'px';
                    targetSubmenu.style.zIndex = '1000';
                    targetSubmenu.style.minWidth = '200px';
                    targetSubmenu.style.backgroundColor = '#343a40';
                    targetSubmenu.style.borderRadius = '0 4px 4px 0';
                    targetSubmenu.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
                    targetSubmenu.style.display = 'block';
                    targetSubmenu.classList.add('submenu-hover');
                });

                // 添加鼠标离开事件
                item.addEventListener('mouseleave', function() {
                    targetSubmenu.style.display = 'none';
                    targetSubmenu.classList.remove('submenu-hover');
                });

                // 为子菜单添加悬停事件
                targetSubmenu.addEventListener('mouseenter', function() {
                    this.style.display = 'block';
                });

                targetSubmenu.addEventListener('mouseleave', function() {
                    this.style.display = 'none';
                });
            });
        } else {
            // 当侧边栏展开时，移除样式
            submenus.forEach(submenu => {
                submenu.style.position = '';
                submenu.style.left = '';
                submenu.style.top = '';
                submenu.style.zIndex = '';
                submenu.style.minWidth = '';
                submenu.style.backgroundColor = '';
                submenu.style.borderRadius = '';
                submenu.style.boxShadow = '';
                submenu.style.display = '';
            });
        }
    }
}

/**
 * 初始化箭头图标状态
 * 确保箭头图标的状态与子菜单的显示状态一致
 */
function initArrowIcons() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    // 使用jQuery或原生 JavaScript初始化箭头图标
    if (typeof $ !== 'undefined') {
        // 使用jQuery
        $('.toggle-submenu').each(function() {
            const targetId = $(this).data('bs-target');
            const targetSubmenu = $(targetId);

            if (targetSubmenu.length) {
                // 检查子菜单是否显示
                const isShown = targetSubmenu.hasClass('show');

                // 获取箭头图标
                const arrow = $(this).find('.bi-chevron-up, .bi-chevron-down');

                if (arrow.length) {
                    // 根据子菜单状态设置箭头图标
                    if (isShown) {
                        arrow.removeClass('bi-chevron-down').addClass('bi-chevron-up');
                    } else {
                        arrow.removeClass('bi-chevron-up').addClass('bi-chevron-down');
                    }
                }
            }
        });
    } else {
        // 使用原生 JavaScript
        const toggleMenuItems = sidebar.querySelectorAll('.toggle-submenu');

        toggleMenuItems.forEach(item => {
            // 获取目标子菜单ID
            const targetId = item.getAttribute('data-bs-target');
            if (!targetId) return;

            const targetSubmenu = document.querySelector(targetId);
            if (!targetSubmenu) return;

            // 检查子菜单是否显示
            const isShown = targetSubmenu.classList.contains('show');

            // 获取箭头图标
            const arrow = item.querySelector('.bi-chevron-up, .bi-chevron-down');
            if (!arrow) return;

            // 根据子菜单状态设置箭头图标
            if (isShown) {
                arrow.classList.remove('bi-chevron-down');
                arrow.classList.add('bi-chevron-up');
            } else {
                arrow.classList.remove('bi-chevron-up');
                arrow.classList.add('bi-chevron-down');
            }
        });
    }
}