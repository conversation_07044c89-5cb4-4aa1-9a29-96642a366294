﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <!-- 导航栏片段 -->
    <nav th:fragment="navbar" class="col-md-2 d-none d-md-block sidebar p-0">
        <div class="position-sticky">
            <div class="p-3 text-center">
                <h5 class="text-white">MyLog</h5>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link text-white" th:href="@{/}">
                        <i class="bi bi-house me-2"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white" th:href="@{/projects}">
                        <i class="bi bi-kanban me-2"></i>项目
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white" th:href="@{/messages}">
                        <i class="bi bi-envelope me-2"></i>消息
                        <span th:if="${unreadMessageCount > 0}" class="badge bg-danger rounded-pill ms-1" th:text="${unreadMessageCount}">0</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white" th:href="@{/settings}">
                        <i class="bi bi-gear me-2"></i>设置
                    </a>
                </li>
                <li class="nav-item">
                    <form th:action="@{/logout}" method="post">
                        <button type="submit" class="nav-link text-white border-0 bg-transparent">
                            <i class="bi bi-box-arrow-right me-2"></i>退出登录
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>
</body>
</html> 