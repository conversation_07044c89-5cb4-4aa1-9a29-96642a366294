/* 搜索条件样式优化 */
@media (min-width: 768px) {
    /* 调整字段选择下拉框宽度 */
    .search-condition .col-md-3 {
        width: 20% !important; /* 从25%减少到20% */
        flex: 0 0 20%;
        max-width: 20%;
    }

    /* 调整值容器宽度 */
    .search-condition .col-md-7 {
        width: 70% !important; /* 从58.33%增加到70% */
        flex: 0 0 70%;
        max-width: 70%;
    }

    /* 调整删除按钮容器宽度 */
    .search-condition .col-md-2 {
        width: 10% !important; /* 从16.67%减少到10% */
        flex: 0 0 10%;
        max-width: 10%;
    }
}

/* 确保删除按钮不会换行 */
.search-condition .btn-outline-danger {
    white-space: nowrap;
    padding: 0.25rem;
    min-width: auto;
    width: 36px !important;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 删除按钮图标样式 */
.search-condition .btn-outline-danger i.bi-trash {
    font-size: 1rem;
    margin: 0;
}

/* 移动端特殊处理 */
@media (max-width: 767.98px) {
    .search-condition {
        flex-wrap: wrap;
    }

    .search-condition .col-12.col-sm-6.col-md-3 {
        width: 100% !important;
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }

    .search-condition .col-10.col-sm-5.col-md-7 {
        width: calc(100% - 50px) !important;
        flex: 0 0 calc(100% - 50px);
        max-width: calc(100% - 50px);
    }
}
