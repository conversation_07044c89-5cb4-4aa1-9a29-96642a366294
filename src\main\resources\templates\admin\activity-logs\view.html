<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('日志详情')}">
    <meta charset="UTF-8">
    <title>日志详情</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">日志详情</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a th:href="@{/admin/activity-logs}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回日志列表
                        </a>
                    </div>
                </div>

                <!-- 日志详情卡片 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title m-0">基本信息</h5>                        <span th:class="${
                            log.activityType.name() == 'LOGIN' ? 'badge bg-success' :
                            log.activityType.name() == 'LOGOUT' ? 'badge bg-secondary' :
                            log.activityType.name() == 'CREATE' ? 'badge bg-primary' :
                            log.activityType.name() == 'UPDATE' ? 'badge bg-info' :
                            log.activityType.name() == 'DELETE' ? 'badge bg-danger' :
                            log.activityType.name() == 'VIEW' ? 'badge bg-light text-dark' :
                            log.activityType.name() == 'DOWNLOAD' ? 'badge bg-warning' :
                            log.activityType.name() == 'EXPORT' ? 'badge bg-secondary' :
                            log.activityType.name() == 'SETTINGS_CHANGE' ? 'badge bg-warning text-dark' :
                            'badge bg-info'}"
                              th:text="${log.activityType}">活动类型</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <th style="width: 30%">日志ID：</th>
                                        <td th:text="${log.id}">1</td>
                                    </tr>
                                    <tr>
                                        <th>用户ID：</th>
                                        <td th:text="${log.userId}">1</td>
                                    </tr>
                                    <tr>
                                        <th>用户名：</th>
                                        <td th:text="${log.username}">管理员</td>
                                    </tr>                                    <tr>
                                        <th>活动类型：</th>                                        <td>
                                            <span th:class="${
                                                log.activityType.name() == 'LOGIN' ? 'badge bg-success' :
                                                log.activityType.name() == 'LOGOUT' ? 'badge bg-secondary' :
                                                log.activityType.name() == 'CREATE' ? 'badge bg-primary' :
                                                log.activityType.name() == 'UPDATE' ? 'badge bg-info' :
                                                log.activityType.name() == 'DELETE' ? 'badge bg-danger' :
                                                log.activityType.name() == 'VIEW' ? 'badge bg-light text-dark' :
                                                log.activityType.name() == 'DOWNLOAD' ? 'badge bg-warning' :
                                                log.activityType.name() == 'EXPORT' ? 'badge bg-secondary' :
                                                log.activityType.name() == 'SETTINGS_CHANGE' ? 'badge bg-warning text-dark' :
                                                'badge bg-info'}"
                                                  th:text="${log.activityType}">活动类型</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <th style="width: 30%">IP地址：</th>
                                        <td th:text="${log.ipAddress}">127.0.0.1</td>
                                    </tr>
                                    <tr>
                                        <th>实体类型：</th>
                                        <td th:text="${log.entityType ?: '-'}">Project</td>
                                    </tr>
                                    <tr>
                                        <th>实体ID：</th>
                                        <td th:text="${log.entityId ?: '-'}">1</td>
                                    </tr>
                                    <tr>
                                        <th>时间：</th>
                                        <td th:text="${log.createdDate}">2023-01-01 12:00:00</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作描述 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title m-0">操作描述</h5>
                    </div>
                    <div class="card-body">
                        <p th:text="${log.description}">描述内容</p>
                    </div>
                </div>

                <!-- 相关用户信息 -->
                <div class="card mb-4" th:if="${user != null}">
                    <div class="card-header">
                        <h5 class="card-title m-0">相关用户信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <th style="width: 30%">用户ID：</th>
                                        <td th:text="${user.userId}">1</td>
                                    </tr>
                                    <tr>
                                        <th>用户名：</th>
                                        <td th:text="${user.username}">管理员</td>
                                    </tr>
                                    <tr>
                                        <th>角色：</th>
                                        <td>
                                            <span th:class="${
                                                user.role.name() == 'ADMIN' ? 'badge bg-danger' :
                                                user.role.name() == 'MANAGER' ? 'badge bg-warning text-dark' :
                                                'badge bg-info'}"
                                                  th:text="${user.role}">角色</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <th style="width: 30%">创建时间：</th>
                                        <td th:text="${#temporals.format(user.createdDate, 'yyyy-MM-dd HH:mm:ss')}">2023-01-01 12:00:00</td>
                                    </tr>
                                    <tr>
                                        <th>主题：</th>
                                        <td th:text="${user.themeStyle}">DEFAULT</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a th:href="@{/admin/users/{id}/edit(id=${user.userId})}" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i> 编辑用户
                            </a>
                            <a th:href="@{/admin/activity-logs/search(userId=${user.userId})}" class="btn btn-sm btn-info">
                                <i class="bi bi-list-ul"></i> 查看此用户的所有日志
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 返回和导航按钮 -->
                <div class="d-flex justify-content-between mb-4">
                    <a th:href="@{/admin/activity-logs}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回日志列表
                    </a>
                </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
</body>
</html>