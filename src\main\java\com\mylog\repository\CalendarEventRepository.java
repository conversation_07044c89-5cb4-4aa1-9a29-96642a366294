package com.mylog.repository;

import com.mylog.model.CalendarEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 日历事件Repository接口
 */
@Repository
public interface CalendarEventRepository extends JpaRepository<CalendarEvent, Long> {
    
    /**
     * 根据日历ID查找事件列表
     */
    List<CalendarEvent> findByCalendarIdOrderByStartTimeAsc(Long calendarId);
    
    /**
     * 根据创建者ID查找事件列表
     */
    List<CalendarEvent> findByCreatorIdOrderByStartTimeAsc(Long creatorId);
    
    /**
     * 根据时间范围查找事件
     */
    @Query("SELECT e FROM CalendarEvent e WHERE e.startTime >= :startTime AND e.startTime <= :endTime ORDER BY e.startTime ASC")
    List<CalendarEvent> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据日历ID和时间范围查找事件
     */
    @Query("SELECT e FROM CalendarEvent e WHERE e.calendar.id = :calendarId AND e.startTime >= :startTime AND e.startTime <= :endTime ORDER BY e.startTime ASC")
    List<CalendarEvent> findByCalendarIdAndTimeRange(@Param("calendarId") Long calendarId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据用户ID和时间范围查找事件
     */
    @Query("SELECT e FROM CalendarEvent e WHERE e.creatorId = :userId AND e.startTime >= :startTime AND e.startTime <= :endTime ORDER BY e.startTime ASC")
    List<CalendarEvent> findByUserIdAndTimeRange(@Param("userId") Long userId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据多个日历ID和时间范围查找事件
     */
    @Query("SELECT e FROM CalendarEvent e WHERE e.calendar.id IN :calendarIds AND e.startTime >= :startTime AND e.startTime <= :endTime ORDER BY e.startTime ASC")
    List<CalendarEvent> findByCalendarIdsAndTimeRange(@Param("calendarIds") List<Long> calendarIds,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据事件类型查找事件
     */
    List<CalendarEvent> findByEventTypeOrderByStartTimeAsc(CalendarEvent.EventType eventType);
    
    /**
     * 根据优先级查找事件
     */
    List<CalendarEvent> findByPriorityOrderByStartTimeAsc(CalendarEvent.Priority priority);
    
    /**
     * 查找重复事件
     */
    List<CalendarEvent> findByIsRecurringTrueOrderByStartTimeAsc();
    
    /**
     * 搜索事件标题或描述
     */
    @Query("SELECT e FROM CalendarEvent e WHERE (LOWER(e.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(e.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) ORDER BY e.startTime ASC")
    List<CalendarEvent> searchByKeyword(@Param("keyword") String keyword);
    
    /**
     * 根据用户ID搜索事件
     */
    @Query("SELECT e FROM CalendarEvent e WHERE e.creatorId = :userId AND (LOWER(e.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(e.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) ORDER BY e.startTime ASC")
    List<CalendarEvent> searchByUserIdAndKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);
    
    /**
     * 统计日历的事件数量
     */
    long countByCalendarId(Long calendarId);
    
    /**
     * 统计用户的事件数量
     */
    long countByCreatorId(Long creatorId);
    
    /**
     * 使用原生SQL根据时间范围查找事件（解决时间格式问题）
     */
    @Query(value = "SELECT * FROM calendar_events WHERE start_time >= ?1 AND start_time <= ?2 ORDER BY start_time ASC", nativeQuery = true)
    List<CalendarEvent> findByTimeRangeNative(String startTime, String endTime);
}
