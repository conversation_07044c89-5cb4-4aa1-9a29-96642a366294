<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证 - 6月7日以后的事件显示</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #e7f3ff; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>🐛➡️✅ 修复验证：6月7日以后的事件显示</h1>
    
    <div class="result info">
        <strong>问题描述:</strong> 6月7号以后的日历不显示事件<br>
        <strong>修复内容:</strong> 修复了 getViewEndDate() 方法中的日期范围计算错误<br>
        <strong>当前时间:</strong> <span id="currentTime"></span>
    </div>
    
    <button class="btn btn-primary" onclick="testDateRange()">测试日期范围修复</button>
    <button class="btn btn-primary" onclick="testEventAPI()">测试事件API</button>
    <button class="btn btn-success" onclick="runFullTest()">完整验证测试</button>
    
    <div id="output"></div>
    
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div class="result ${className}"><strong>[${timestamp}]</strong> ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        function formatStandardDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        // 修复后的日期范围计算
        function getViewStartDate(currentDate) {
            const date = new Date(currentDate);
            date.setDate(1);
            date.setDate(date.getDate() - date.getDay());
            return date;
        }
        
        function getViewEndDate(currentDate) {
            const date = new Date(currentDate);
            // 获取当前月的最后一天
            date.setMonth(date.getMonth() + 1, 0);
            // 计算月视图中显示的最后一天（可能在下个月）
            const lastDayOfMonth = new Date(date);
            lastDayOfMonth.setDate(lastDayOfMonth.getDate() + (6 - lastDayOfMonth.getDay()));
            return lastDayOfMonth;
        }
        
        function testDateRange() {
            log('🔍 <strong>测试修复后的日期范围计算</strong>');
            
            const june7 = new Date('2025-06-07');
            const startDate = getViewStartDate(june7);
            const endDate = getViewEndDate(june7);
            
            log(`📅 测试日期: 2025年6月7日`);
            log(`📊 月视图日期范围:`);
            log(`&nbsp;&nbsp;开始: ${formatStandardDateTime(startDate)} (${startDate.toLocaleDateString('zh-CN')})`);
            log(`&nbsp;&nbsp;结束: ${formatStandardDateTime(endDate)} (${endDate.toLocaleDateString('zh-CN')})`);
            
            // 验证关键日期
            const criticalDates = [
                { date: new Date('2025-06-07'), name: '6月7日' },
                { date: new Date('2025-06-10'), name: '6月10日' },
                { date: new Date('2025-06-11'), name: '6月11日' }
            ];
            
            let allInRange = true;
            criticalDates.forEach(item => {
                const inRange = item.date >= startDate && item.date <= endDate;
                allInRange = allInRange && inRange;
                log(`&nbsp;&nbsp;${item.name}: ${inRange ? '✅ 在范围内' : '❌ 超出范围'}`, 
                    inRange ? 'success' : 'error');
            });
            
            log(`<br><strong>日期范围修复结果: ${allInRange ? '✅ 成功' : '❌ 失败'}</strong>`, 
                allInRange ? 'success' : 'error');
        }
        
        async function testEventAPI() {
            log('🌐 <strong>测试事件API调用</strong>');
            
            try {
                // 模拟修复后的日期范围
                const june7 = new Date('2025-06-07');
                const startDate = getViewStartDate(june7);
                const endDate = getViewEndDate(june7);
                
                const startTimeFormatted = formatStandardDateTime(startDate);
                const endTimeFormatted = formatStandardDateTime(endDate);
                
                log(`📡 API请求参数:`);
                log(`&nbsp;&nbsp;开始时间: ${startTimeFormatted}`);
                log(`&nbsp;&nbsp;结束时间: ${endTimeFormatted}`);
                
                const apiUrl = `/api/events/range?startTime=${encodeURIComponent(startTimeFormatted)}&endTime=${encodeURIComponent(endTimeFormatted)}`;
                
                const token = document.querySelector("meta[name='_csrf']")?.getAttribute("content");
                const header = document.querySelector("meta[name='_csrf_header']")?.getAttribute("content");
                
                const response = await fetch(apiUrl, {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        ...(token && header ? { [header]: token } : {})
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const events = result.data || result;
                    
                    log(`✅ API调用成功，返回 ${events.length} 个事件`, 'success');
                    
                    // 统计6月7日及以后的事件
                    const afterJune7 = events.filter(e => e.startTime >= '2025-06-07');
                    log(`📊 6月7日及以后的事件: ${afterJune7.length} 个`, 
                        afterJune7.length > 0 ? 'success' : 'warning');
                    
                    afterJune7.forEach(event => {
                        log(`&nbsp;&nbsp;📅 "${event.title}" - ${event.startTime}`, 'info');
                    });
                    
                } else {
                    log(`❌ API调用失败: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`❌ API测试异常: ${error.message}`, 'error');
            }
        }
        
        async function runFullTest() {
            log('🚀 <strong>开始完整验证测试</strong>');
            log('');
            
            // 1. 测试日期范围
            await new Promise(resolve => {
                testDateRange();
                setTimeout(resolve, 1000);
            });
            
            log('');
            
            // 2. 测试API
            await testEventAPI();
            
            log('');
            log('🎯 <strong>验证完成！</strong>');
            log('请检查上述测试结果，确认6月7日以后的事件现在能够正确显示。', 'success');
            log('如果测试通过，可以访问主日历页面查看修复效果: <a href="/calendar" target="_blank">点击这里</a>', 'info');
        }
        
        // 页面加载时显示当前时间
        window.addEventListener('load', () => {
            const now = new Date();
            document.getElementById('currentTime').textContent = 
                `${now.toLocaleString('zh-CN')} (${formatStandardDateTime(now)})`;
        });
    </script>
</body>
</html>
