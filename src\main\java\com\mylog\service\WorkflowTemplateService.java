package com.mylog.service;

import com.mylog.model.workflow.WorkflowTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 流程模板服务接口
 */
public interface WorkflowTemplateService {

    /**
     * 查找所有流程模板
     */
    List<WorkflowTemplate> findAllTemplates();

    /**
     * 分页查找所有流程模板
     */
    Page<WorkflowTemplate> findAllTemplates(Pageable pageable);

    /**
     * 根据ID查找流程模板
     */
    Optional<WorkflowTemplate> findTemplateById(Long id);

    /**
     * 根据模板编码查找流程模板
     */
    Optional<WorkflowTemplate> findTemplateByCode(String templateCode);

    /**
     * 根据模板标题查找流程模板
     */
    Optional<WorkflowTemplate> findTemplateByTitle(String templateTitle);

    /**
     * 根据适用范围查找流程模板
     */
    List<WorkflowTemplate> findTemplatesByScope(String applicableScope);

    /**
     * 根据适用范围查找启用的流程模板
     */
    List<WorkflowTemplate> findEnabledTemplatesByScope(String applicableScope);
    
    /**
     * 根据启用状态查找流程模板
     */
    List<WorkflowTemplate> findByEnabled(Boolean enabled);

    /**
     * 保存流程模板
     */
    WorkflowTemplate saveTemplate(WorkflowTemplate template);

    /**
     * 删除流程模板
     */
    void deleteTemplate(Long id);

    /**
     * 启用/禁用流程模板
     */
    WorkflowTemplate toggleTemplateStatus(Long id, boolean enabled);

    /**
     * 综合搜索流程模板
     */
    Page<WorkflowTemplate> searchTemplates(String keyword, String applicableScope, Boolean enabled, Pageable pageable);

    /**
     * 使用原生SQL查询所有流程模板
     */
    List<WorkflowTemplate> findAllTemplatesByNativeQuery();
    
}
