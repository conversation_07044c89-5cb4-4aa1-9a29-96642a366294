package com.mylog.dto;

import com.mylog.model.Calendar;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 日历DTO
 */
public class CalendarDTO {
    
    private Long id;
    
    @NotBlank(message = "日历名称不能为空")
    private String name;
    
    private String description;
      private String color = "#007bff";
    
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    private Boolean isDefault = false;
    
    private Boolean isShared = false;
    
    private LocalDateTime createdTime;
    
    private LocalDateTime updatedTime;
    
    private List<CalendarEventDTO> events = new ArrayList<>();
    
    // 构造函数
    public CalendarDTO() {}
    
    public CalendarDTO(Calendar calendar) {
        this.id = calendar.getId();
        this.name = calendar.getName();
        this.description = calendar.getDescription();
        this.color = calendar.getColor();
        this.userId = calendar.getUserId();
        this.isDefault = calendar.getIsDefault();
        this.isShared = calendar.getIsShared();
        this.createdTime = calendar.getCreatedTime();
        this.updatedTime = calendar.getUpdatedTime();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Boolean getIsDefault() {
        return isDefault;
    }
    
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    public Boolean getIsShared() {
        return isShared;
    }
    
    public void setIsShared(Boolean isShared) {
        this.isShared = isShared;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public List<CalendarEventDTO> getEvents() {
        return events;
    }
    
    public void setEvents(List<CalendarEventDTO> events) {
        this.events = events;
    }
    
    @Override
    public String toString() {
        return "CalendarDTO{" +
               "id=" + id +
               ", name='" + name + '\'' +
               ", description='" + description + '\'' +
               ", color='" + color + '\'' +
               ", userId=" + userId +
               ", isDefault=" + isDefault +
               ", isShared=" + isShared +
               ", createdTime=" + createdTime +
               ", updatedTime=" + updatedTime +
               '}';
    }
}
