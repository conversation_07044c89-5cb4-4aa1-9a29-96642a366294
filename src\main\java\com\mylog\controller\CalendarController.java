package com.mylog.controller;

import com.mylog.dto.CalendarDTO;
import com.mylog.service.CalendarService;
import com.mylog.service.OptionsService;
import com.mylog.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 日历控制器
 */
@Controller
@Slf4j
public class CalendarController {
    
    @Autowired
    private CalendarService calendarService;
    
    @Autowired
    private OptionsService optionsService;    /**
     * 日历主页面
     */
    @GetMapping("/calendar")
    public String calendarPage(Model model) {
        // 传递人员数据到前端，用于提醒接收人选择
        List<String> personnel = optionsService.getPersonnel();
        log.info("📊 Calendar页面 - 获取到的人员数据数量: {}", personnel.size());
        if (!personnel.isEmpty()) {
            log.info("📊 Calendar页面 - 前几个人员: {}", personnel.subList(0, Math.min(5, personnel.size())));
        }
        
        model.addAttribute("personnel", personnel);
        return "calendar/index";
    }/**
     * 日历API - 获取当前用户的日历列表
     */
    @GetMapping(value = {"/api/calendars", "/api/calendars/"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getCurrentUserCalendars() {
        Map<String, Object> response = new HashMap<>();
        try {
            // 从SecurityContext获取当前用户ID
            Long currentUserId = SecurityUtils.getCurrentUserId();
            if (currentUserId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }
            
            List<CalendarDTO> calendars = calendarService.getCalendarsByUserId(currentUserId);
            response.put("success", true);
            response.put("data", calendars);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }    /**
     * 日历API - 创建日历
     */    
    @PostMapping(value = {"/api/calendars", "/api/calendars/"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> createCalendar(@RequestBody CalendarDTO calendarDTO) {
        System.out.println("=== DEBUG: CalendarController.createCalendar() 开始执行 ===");
        System.out.println("DEBUG: 接收到的日历数据: " + calendarDTO);
        log.info("CalendarController.createCalendar() 被调用，数据: {}", calendarDTO);
        Map<String, Object> response = new HashMap<>();
        try {
            // 从SecurityContext获取当前用户ID
            Long currentUserId = SecurityUtils.getCurrentUserId();
            if (currentUserId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }
            
            // 始终设置用户ID，确保不会为null
            calendarDTO.setUserId(currentUserId);
            System.out.println("DEBUG: 设置当前用户ID(" + currentUserId + ")后的日历数据: " + calendarDTO);
            log.info("设置当前用户ID({})后准备调用服务层", currentUserId);
            
            System.out.println("DEBUG: 准备调用服务层的createCalendar方法");
            CalendarDTO createdCalendar = calendarService.createCalendar(calendarDTO);
            System.out.println("DEBUG: 服务层返回的创建结果: " + createdCalendar);
            log.info("日历创建成功，返回数据: {}", createdCalendar);
            
            response.put("success", true);
            response.put("message", "日历创建成功");
            response.put("data", createdCalendar);
            System.out.println("DEBUG: 返回成功响应: " + response);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.out.println("ERROR: 创建日历时发生异常: " + e.getMessage());
            e.printStackTrace();
            log.error("创建日历失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }    /**
     * 日历API - 更新日历
     */
    @PutMapping(value = "/api/calendars/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> updateCalendar(@PathVariable Long id, 
                                                            @Valid @RequestBody CalendarDTO calendarDTO) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 从SecurityContext获取当前用户ID
            Long currentUserId = SecurityUtils.getCurrentUserId();
            if (currentUserId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }
            
            // 设置用户ID，确保权限检查
            calendarDTO.setUserId(currentUserId);
            
            CalendarDTO updatedCalendar = calendarService.updateCalendar(id, calendarDTO);
            response.put("success", true);
            response.put("message", "日历更新成功");
            response.put("data", updatedCalendar);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }      /**
     * 日历API - 删除日历
     */
    @DeleteMapping(value = "/api/calendars/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteCalendar(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            calendarService.deleteCalendar(id);
            response.put("success", true);
            response.put("message", "日历删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }      /**
     * 日历API - 获取日历详情
     */
    @GetMapping(value = "/api/calendars/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getCalendar(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            CalendarDTO calendar = calendarService.getCalendarById(id)
                .orElseThrow(() -> new IllegalArgumentException("日历不存在"));
            response.put("success", true);
            response.put("data", calendar);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 获取用户的日历列表
     */
    @GetMapping("/api/calendars/user/{userId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getCalendarsByUserId(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarDTO> calendars = calendarService.getCalendarsByUserId(userId);
            response.put("success", true);
            response.put("data", calendars);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 获取用户的默认日历
     */
    @GetMapping("/api/calendars/user/{userId}/default")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDefaultCalendar(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            CalendarDTO defaultCalendar = calendarService.getDefaultCalendar(userId)
                .orElseThrow(() -> new IllegalArgumentException("未找到默认日历"));
            response.put("success", true);
            response.put("data", defaultCalendar);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 设置默认日历
     */
    @PutMapping("/api/calendars/user/{userId}/default/{calendarId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> setDefaultCalendar(@PathVariable Long userId, 
                                                                @PathVariable Long calendarId) {
        Map<String, Object> response = new HashMap<>();
        try {
            CalendarDTO defaultCalendar = calendarService.setDefaultCalendar(userId, calendarId);
            response.put("success", true);
            response.put("message", "默认日历设置成功");
            response.put("data", defaultCalendar);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 获取共享日历列表
     */
    @GetMapping("/api/calendars/shared")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getSharedCalendars() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarDTO> sharedCalendars = calendarService.getSharedCalendars();
            response.put("success", true);
            response.put("data", sharedCalendars);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 获取用户可访问的所有日历
     */
    @GetMapping("/api/calendars/user/{userId}/accessible")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAccessibleCalendars(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarDTO> calendars = calendarService.getAccessibleCalendars(userId);
            response.put("success", true);
            response.put("data", calendars);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 创建用户默认日历
     */
    @PostMapping("/api/calendars/user/{userId}/create-default")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> createDefaultCalendar(@PathVariable Long userId, 
                                                                   @RequestParam String userName) {
        Map<String, Object> response = new HashMap<>();
        try {
            CalendarDTO defaultCalendar = calendarService.createDefaultCalendar(userId, userName);
            response.put("success", true);
            response.put("message", "默认日历创建成功");
            response.put("data", defaultCalendar);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
      /**
     * 日历API - 检查日历名称是否存在
     */
    @GetMapping("/api/calendars/user/{userId}/check-name")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> checkCalendarName(@PathVariable Long userId, 
                                                               @RequestParam String name) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean exists = calendarService.isCalendarNameExists(userId, name);
            response.put("success", true);
            response.put("data", exists);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
