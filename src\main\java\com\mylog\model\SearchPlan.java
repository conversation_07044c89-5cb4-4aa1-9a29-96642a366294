package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Entity
@Table(name = "SearchPlans")
@Data
public class SearchPlan {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long planId;
    
    @Column(nullable = false)
    private String name;
    
    @Column(length = 1000)
    private String description;
    
    @Column(nullable = false, length = 5000)
    private String conditions;
    
    @Column(nullable = false)
    private String username;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String createdDate;
    
    /**
     * 设置创建时间
     */
    public void setCreatedDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            throw new IllegalArgumentException("创建时间不能为空");
        }
        this.createdDate = dateTime.format(DATE_FORMATTER);
    }
    
    /**
     * 获取创建时间作为LocalDateTime对象
     */
    public LocalDateTime getCreatedDateTime() {
        if (this.createdDate == null) {
            return null;
        }
        return LocalDateTime.parse(this.createdDate, DATE_FORMATTER);
    }
} 