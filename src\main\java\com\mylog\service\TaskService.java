package com.mylog.service;

import com.mylog.model.ProjectTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface TaskService {

    @Transactional(readOnly = true)
    List<ProjectTask> findAllTasks();

    /**
     * 获取所有任务用于更新（不分页）
     * @return 所有任务列表
     */
    @Transactional(readOnly = true)
    List<ProjectTask> findAllTasksForUpdate();

    Page<ProjectTask> findAllTasks(Pageable pageable);

    Optional<ProjectTask> findTaskById(Long id);

    List<ProjectTask> findTasksByProjectId(Long projectId);

    Page<ProjectTask> findTasksByProjectId(Long projectId, Pageable pageable);
    
    /**
     * 获取指定项目的所有任务（不分页）
     * @param projectId 项目ID
     * @return 项目下的所有任务列表
     */
    List<ProjectTask> findAllTasksByProjectId(Long projectId);

    /**
     * 查找所有非归档项目的任务
     * @param pageable 分页参数
     * @return 分页非归档项目任务列表
     */
    Page<ProjectTask> findTasksFromNonArchivedProjects(Pageable pageable);

    @Transactional
    ProjectTask saveTask(ProjectTask task);

    void deleteTask(Long id);    /**
     * 将任务标记为完成
     * @param id 任务ID
     * @return 更新后的任务
     */
    ProjectTask completeTask(Long id);    /**
     * 将任务标记为暂停
     * @param id 任务ID
     * @return 更新后的任务
     */
    ProjectTask pauseTask(Long id);

    /**
     * 将任务标记为暂停，支持重启模式
     * @param id 任务ID
     * @param isRestart 是否为重启模式，如果为true则执行重启相关代码
     * @return 更新后的任务
     */
    ProjectTask pauseTask(Long id, boolean isRestart);

    List<ProjectTask> findTasksByResponsible(String responsible);

    Page<ProjectTask> findTasksByResponsible(String responsible, Pageable pageable);

    /**
     * 统计特定负责人和状态的任务数量
     * @param responsible 负责人
     * @param status 状态
     * @return 任务数量
     */
    Long countTasksByResponsibleAndStatus(String responsible, String status);

    /**
     * 统计特定负责人在指定日期之后创建的任务数量
     * @param responsible 负责人
     * @param createdAfter 创建日期（格式：yyyy-MM-dd HH:mm:ss）
     * @return 任务数量
     */
    Long countTasksByResponsibleAndCreatedDateAfter(String responsible, String createdAfter);
    
    /**
     * 统计特定负责人在指定日期范围内已完成的任务数量
     * @param responsible 负责人
     * @param completedAfter 完成日期下限（格式：yyyy-MM-dd HH:mm:ss）
     * @return 任务数量
     */
    Long countCompletedTasksByResponsibleAndEndDateAfter(String responsible, String completedAfter);

    List<ProjectTask> findTasksByStatus(String status);

    Page<ProjectTask> findTasksByStatus(String status, Pageable pageable);

    List<ProjectTask> findTasksByRisk(String risk);

    Page<ProjectTask> findTasksByRisk(String risk, Pageable pageable);

    List<ProjectTask> searchTasks(String keyword);

    Page<ProjectTask> searchTasks(String keyword, Pageable pageable);

    List<ProjectTask> findTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    Page<ProjectTask> findTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * 统计指定用户被分配的任务数量
     * @param assignee 被分配人用户名
     * @return 任务数量
     */
    Long countTasksByAssignee(String assignee);

    /**
     * 动态搜索任务
     * @param searchCriteria 搜索条件映射
     * @return 符合条件的任务列表
     */
    List<ProjectTask> dynamicSearchTasks(Map<String, Object> searchCriteria);

    /**
     * 动态搜索任务（分页版本）
     * @param searchCriteria 搜索条件映射
     * @param pageable 分页对象
     * @return 符合条件的任务分页
     */
    Page<ProjectTask> dynamicSearchTasks(Map<String, Object> searchCriteria, Pageable pageable);

    Page<ProjectTask> findDifficultTasksByResponsible(String responsible, Pageable pageable);

    Page<ProjectTask> findSpecialTasksByResponsible(String responsible, Pageable pageable);

    /**
     * 统计未归档项目中进行中的任务数量
     * @return 任务数量
     */
    Long countNonArchivedInProgressTasks();

    /**
     * 获取最近的未归档项目中的任务
     * @param limit 限制数量
     * @return 最近的任务列表
     */
    List<ProjectTask> findRecentTasksFromNonArchivedProjects(int limit);

    /**
     * 统计指定用户负责的难点任务中状态为"进行中"的数量
     * @param responsible 负责人
     * @return 进行中的难点任务数量
     */
    Long countInProgressDifficultTasksByResponsible(String responsible);

    /**
     * 统计指定用户负责的专项任务中状态为"进行中"的数量
     * @param responsible 负责人
     * @return 进行中的专项任务数量
     */
    Long countInProgressSpecialTasksByResponsible(String responsible);

    /**
     * 检查项目是否存在未完成的任务
     * @param projectId 项目ID
     * @return 如果项目存在任何未完成（非"已完成"状态）的任务，则返回true；否则返回false
     */
    boolean hasUncompletedTasks(Long projectId);

    /**
     * 删除所有任务
     */
    void deleteAllTasks();

    /**
     * 批量保存任务
     * @param tasks 任务列表
     * @return 保存后的任务列表
     */
    List<ProjectTask> saveAllTasks(List<ProjectTask> tasks);

    /**
     * 获取用户的分管任务列表
     * 条件：
     * 1. 任务名称包含"客户现场调试"
     * 2. 与该任务同一个项目下必须有一个类型为"分管"的任务且这个任务的责任人为当前用户
     *
     * @param currentUsername 当前用户名
     * @return 符合条件的任务列表
     */
    List<ProjectTask> findMyDelegatedTasks(String currentUsername);

    /**
     * 获取用户的分管任务列表（分页版本）
     *
     * @param currentUsername 当前用户名
     * @param pageable 分页参数
     * @return 符合条件的任务分页
     */
    Page<ProjectTask> findMyDelegatedTasks(String currentUsername, Pageable pageable);

    /**
     * 统计用户的进行中分管任务数量
     *
     * @param currentUsername 当前用户名
     * @return 进行中的分管任务数量
     */
    Long countInProgressDelegatedTasks(String currentUsername);

    /**
     * 获取用户的教育培训任务列表
     * @param currentUsername 当前用户名
     * @param pageable 分页参数
     * @return 教育培训任务分页
     */
    Page<ProjectTask> findMyTrainingTasks(String currentUsername, Pageable pageable);

    /**
     * 统计用户的进行中教育培训任务数量
     * @param currentUsername 当前用户名
     * @return 进行中的教育培训任务数量
     */
    Long countInProgressTrainingTasks(String currentUsername);

    /**
     * 获取用户负责的订单类型任务列表
     * @param currentUsername 当前用户名
     * @param pageable 分页参数
     * @return 订单任务分页
     */
    Page<ProjectTask> findMyOrderTasks(String currentUsername, Pageable pageable);

    /**
     * 统计用户的进行中订单任务数量
     * @param currentUsername 当前用户名
     * @return 进行中的订单任务数量
     */
    Long countInProgressOrderTasks(String currentUsername);

    /**
     * 获取所有订单类型任务列表
     * @param pageable 分页参数
     * @return 订单任务分页
     */
    Page<ProjectTask> findAllOrderTasks(Pageable pageable);

    /**
     * 统计所有进行中订单任务数量
     * @return 进行中的订单任务数量
     */
    Long countAllInProgressOrderTasks();

    /**
     * 获取所有难点任务列表
     * @param pageable 分页参数
     * @return 难点任务分页
     */
    Page<ProjectTask> findAllDifficultTasks(Pageable pageable);

    /**
     * 获取所有专项任务列表
     * @param pageable 分页参数
     * @return 专项任务分页
     */
    Page<ProjectTask> findAllSpecialTasks(Pageable pageable);

    /**
     * 统计所有进行中的难点任务数量
     * @return 进行中的难点任务数量
     */
    Long countAllInProgressDifficultTasks();

    /**
     * 统计所有进行中的专项任务数量
     * @return 进行中的专项任务数量
     */
    Long countAllInProgressSpecialTasks();

    /**
     * 获取用户负责的订单类型任务列表
     * @param responsible 负责人
     * @param pageable 分页参数
     * @return 订单任务分页
     */
    Page<ProjectTask> findOrderTasksByResponsible(String responsible, Pageable pageable);

    /**
     * 统计用户的订单任务数量（按状态）
     * @param responsible 负责人
     * @param status 状态
     * @return 订单任务数量
     */
    Long countOrderTasksByResponsibleAndStatus(String responsible, String status);

    /**
     * 统计特定状态的任务数量
     * @param status 状态
     * @return 任务数量
     */
    Long countTasksByStatus(String status);

    /**
     * 获取所有任务（不分页）
     */
    List<ProjectTask> findAllTasksNoPage();

    /**
     * 计算任务的评论天数并保存
     * @param tasks 任务列表
     */
    void calculateCommentDays(List<ProjectTask> tasks);

    /**
     * 查找特定项目中特定状态的任务
     * @param projectId 项目ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<ProjectTask> findTasksByProjectIdAndStatus(Long projectId, String status);

    /**
     * 获取最大任务ID
     * @return 最大任务ID
     */
    Long findMaxTaskId();

    /**
     * 检查任务是否可编辑
     * 如果任务正在审批中，则不可编辑
     * @param taskId 任务ID
     * @return 如果可以编辑返回true，否则返回false
     */
    boolean isTaskEditable(Long taskId);

    /**
     * 更新任务的审批状态
     * @param taskId 任务ID
     * @param approvalStatus 审批状态
     * @param approvalInstanceId 审批流程实例ID
     * @param approverName 审批人姓名
     * @return 更新后的任务
     */
    ProjectTask updateTaskApprovalStatus(Long taskId, Integer approvalStatus, Long approvalInstanceId, String approverName);
    
    /**
     * 使用自定义ID创建任务
     * @param task 包含自定义ID的任务实体
     * @return 创建后的任务
     */
    ProjectTask createTaskWithCustomId(ProjectTask task);

    /**
     * 根据项目客户名称统计任务数量
     * @param customerName 客户名称
     * @return 任务数量
     */
    Long countTasksByProjectCustomerName(String customerName);

    /**
     * 更新所有进行中任务的实际工期
     * 此方法会：
     * 1. 找到所有状态为"进行中"的任务
     * 2. 调用pauseTask方法更新每个任务的工期
     * 3. 将任务状态改回"进行中"
     * 4. 更新实际开始时间为当前时间
     * @return 更新结果信息
     */
    String updateAllInProgressTasksDuration();
}