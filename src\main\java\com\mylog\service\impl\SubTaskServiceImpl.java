package com.mylog.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.SubTask;
import com.mylog.repository.SubTaskRepository;
import com.mylog.service.SubTaskService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Service
@Transactional
public class SubTaskServiceImpl implements SubTaskService {

    @Autowired
    private SubTaskRepository subTaskRepository;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    public List<SubTask> findAllSubTasks() {
        return subTaskRepository.findAll();
    }

    @Override
    public Optional<SubTask> findSubTaskById(Long id) {
        return subTaskRepository.findById(id);
    }

    @Override
    public List<SubTask> findSubTasksByTaskId(Long taskId) {
        return subTaskRepository.querySubTasksByTaskId(taskId);
    }

    @Override
    public Page<SubTask> findSubTasksByTaskId(Long taskId, Pageable pageable) {
        return subTaskRepository.querySubTasksByTaskIdPaged(taskId, pageable);
    }

    @Override
    public SubTask saveSubTask(SubTask subTask) {
        if (subTask.getSubTaskId() == null) {
            // 如果是新建子任务，设置序号
            subTask.setSequenceNumber(getNextSequenceNumber(subTask.getTaskId()));
        }
        
        // 保存子任务
        SubTask savedSubTask = subTaskRepository.save(subTask);
        
        // 发布事件通知父任务更新最后评论日期
        try {
            eventPublisher.publishEvent(new TaskUpdateEvent(subTask.getTaskId()));
        } catch (Exception e) {
            // 记录错误但不影响子任务的保存
            System.err.println("发布任务更新事件时出错: " + e.getMessage());
        }
        
        return savedSubTask;
    }

    @Override
    public void deleteSubTask(Long id) {
        subTaskRepository.deleteById(id);
    }

    @Override
    public Integer getNextSequenceNumber(Long taskId) {
        Integer maxSequence = subTaskRepository.findMaxSequenceNumberByTaskId(taskId);
        return (maxSequence == null) ? 1 : maxSequence + 1;
    }

    @Override
    public List<SubTask> searchSubTasks(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAllSubTasks();
        }
        return subTaskRepository.findByLogContentContaining(keyword);
    }

    @Override
    public List<SubTask> findSubTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return subTaskRepository.findByCreatedDateBetween(
            startDate.format(formatter),
            endDate.format(formatter)
        );
    }

    @Override
    public Optional<SubTask> findLatestSubTaskByTaskId(Long taskId) {
        return subTaskRepository.queryLatestSubTaskByTaskId(taskId);
    }

    @Override
    public List<SubTask> findLatestSubTasksByTaskIds(List<Long> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return new ArrayList<>();
        }
        return subTaskRepository.findLatestSubTasksByTaskIds(taskIds);
    }

    @Override
    public Page<SubTask> findAllSubTasksPaged(Pageable pageable) {
        return subTaskRepository.findAllSubTasksPaged(pageable);
    }

    @Override
    public Page<SubTask> dynamicSearchSubTasks(Map<String, String> searchCriteria, Pageable pageable) {
        // 如果没有搜索条件，返回所有评论
        if (searchCriteria == null || searchCriteria.isEmpty()) {
            return findAllSubTasksPaged(pageable);
        }

        // 提取搜索参数，如果没有则传null
        String taskName = searchCriteria.get("taskName");
        String logContent = searchCriteria.get("logContent");
        String createdBy = searchCriteria.get("createdBy");
        String startDate = null;
        String endDate = null;

        // 处理日期范围
        if (searchCriteria.containsKey("createdDate_start")) {
            try {
                LocalDateTime start = LocalDateTime.parse(searchCriteria.get("createdDate_start"));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                startDate = start.format(formatter);
            } catch (Exception e) {
                // 日期解析失败，忽略此条件
            }
        }

        if (searchCriteria.containsKey("createdDate_end")) {
            try {
                LocalDateTime end = LocalDateTime.parse(searchCriteria.get("createdDate_end"));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                endDate = end.format(formatter);
            } catch (Exception e) {
                // 日期解析失败，忽略此条件
            }
        }

        // 使用多条件查询方法
        return subTaskRepository.findByMultipleConditionsPaged(
            taskName, logContent, createdBy, startDate, endDate, pageable);
    }
}