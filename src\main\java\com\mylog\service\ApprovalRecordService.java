package com.mylog.service;

import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.ApprovalRecord.ApprovalAction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 审批记录服务接口
 */
public interface ApprovalRecordService {
    
    /**
     * 查找所有审批记录
     */
    List<ApprovalRecord> findAllRecords();
    
    /**
     * 分页查找所有审批记录
     */
    Page<ApprovalRecord> findAllRecords(Pageable pageable);
    
    /**
     * 根据ID查找审批记录
     */
    Optional<ApprovalRecord> findRecordById(Long id);
    
    /**
     * 根据实例ID查找审批记录列表，按创建时间排序
     */
    List<ApprovalRecord> findRecordsByInstanceId(Long instanceId);
    
    /**
     * 根据实例ID查找审批记录列表（分页版本）
     */
    Page<ApprovalRecord> findRecordsByInstanceId(Long instanceId, Pageable pageable);
    
    /**
     * 根据步骤ID查找审批记录列表
     */
    List<ApprovalRecord> findRecordsByStepId(Long stepId);
    
    /**
     * 根据审批人查找审批记录列表
     */
    List<ApprovalRecord> findRecordsByApprover(String approver);
    
    /**
     * 根据审批操作类型查找审批记录列表
     */
    List<ApprovalRecord> findRecordsByAction(ApprovalAction action);
    
    /**
     * 保存审批记录
     */
    ApprovalRecord saveRecord(ApprovalRecord record);
    
    /**
     * 查找实例的最新审批记录
     */
    Optional<ApprovalRecord> findLatestRecord(Long instanceId);
    
    /**
     * 删除审批记录
     */
    void deleteRecord(Long recordId);
    
    /**
     * 查找用户的审批历史记录
     */
    Page<ApprovalRecord> findUserApprovalHistory(String username, Pageable pageable);
    
    /**
     * 根据附件路径查找审批记录
     */
    List<ApprovalRecord> findRecordsByAttachmentPath(String attachmentPath);
}
