<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程详情')}">
    <meta charset="UTF-8">
    <title>流程详情</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                流程详情
                <small class="text-muted" th:text="'- ' + ${instance.title}"></small>
            </h1>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <a th:if="${instance.status.name() == 'DRAFT'}" th:href="@{/workflow/approval/{id}(id=${instance.instanceId})}" class="btn btn-primary">
                    <i class="bi bi-send"></i> 提交
                </a>
            </div>
        </div>

        <!-- 成功/错误消息 -->
        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            <span th:text="${message}">操作成功</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span th:text="${error}">操作失败</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>        <!-- 流程信息卡片 -->
        <div class="row mb-4">
            <div class="col-md-12">                <!-- 使用共享的信息卡片片段 -->
                <div th:replace="~{workflow/fragments/workflow-info-card :: info-card(${instance}, ${currentStep}, ${totalSteps}, ${stepLabels}, ${stepComments}, ${stepTimes})}"></div>
            </div>
        </div>

        <!-- 审批记录卡片 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">审批记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" th:if="${!records.isEmpty()}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>操作类型</th>
                                        <th>审批人类型</th>
                                        <th>操作人</th>
                                        <th>操作时间</th>
                                        <th>审批意见</th>
                                        <th>附件</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="record : ${records}">
                                        <td>
                                            <span th:if="${record.action.name() == 'SUBMIT'}" class="badge bg-info">提交</span>
                                            <span th:if="${record.action.name() == 'APPROVE'}" class="badge bg-success">同意</span>
                                            <span th:if="${record.action.name() == 'REJECT'}" class="badge bg-danger">拒绝</span>
                                            <span th:if="${record.action.name() == 'TRANSFER'}" class="badge bg-warning">转交</span>
                                            <span th:if="${record.action.name() == 'WITHDRAW'}" class="badge bg-secondary">撤回</span>
                                            <span th:if="${record.action.name() == 'TERMINATE'}" class="badge bg-dark">终止</span>
                                            <span th:if="${record.action.name() == 'COMMENT'}" class="badge bg-light text-dark">评论</span>
                                        </td>                                        <td>
                                            <span th:if="${record.step != null && record.step.approverType != null}">
                                                <span th:if="${record.step.approverType.name() == 'FIXED_USER'}" class="badge bg-secondary">固定用户</span>
                                                <span th:if="${record.step.approverType.name() == 'ROLE'}" class="badge bg-info">角色</span>
                                                <span th:if="${record.step.approverType.name() == 'DEPARTMENT_HEAD'}" class="badge bg-primary">部门主管</span>
                                                <span th:if="${record.step.approverType.name() == 'DYNAMIC'}" class="badge bg-warning">动态指定</span>
                                            </span>
                                            <!-- 当step信息不可用时的fallback逻辑 -->
                                            <span th:unless="${record.step != null && record.step.approverType != null}">
                                                <!-- 提交和撤回操作固定为发起人操作 -->
                                                <span th:if="${record.action.name() == 'SUBMIT' || record.action.name() == 'WITHDRAW'}" class="badge bg-secondary">固定用户</span>
                                                <!-- 终止操作固定为管理员操作 -->
                                                <span th:if="${record.action.name() == 'TERMINATE'}" class="badge bg-dark">固定用户</span>
                                                <!-- 转交操作属于动态指定 -->
                                                <span th:if="${record.action.name() == 'TRANSFER'}" class="badge bg-warning">动态指定</span>
                                                <!-- 评论操作固定为当前审批人 -->
                                                <span th:if="${record.action.name() == 'COMMENT'}" class="badge bg-secondary">固定用户</span>
                                                <!-- 审批和拒绝操作：当步骤信息不可用时，显示动态指定 -->
                                                <span th:if="${record.action.name() == 'APPROVE' || record.action.name() == 'REJECT'}" class="badge bg-warning">动态指定</span>
                                            </span>
                                        </td>
                                        <td th:text="${record.approver}">操作人</td>
                                        <td th:text="${record.createdDateTime != null ? #temporals.format(record.createdDateTime, 'yyyy-MM-dd HH:mm:ss') : '-'}">操作时间</td>
                                        <td th:text="${record.comment ?: '-'}">审批意见</td>
                                        <td>
                                            <div th:if="${record.attachments != null && !#strings.isEmpty(record.attachments)}"
                                                 class="d-flex flex-column gap-1">
                                                <div th:each="attachment, status : ${#strings.arraySplit(record.attachments, ';')}"
                                                     th:if="${!#strings.isEmpty(attachment)}">
                                                    <button th:data-attachment-path="${attachment}"
                                                       class="btn btn-sm btn-outline-primary d-flex align-items-center attachment-download-btn"
                                                       onclick="downloadWorkflowAttachmentWithDirectorySelection(this.dataset.attachmentPath)">
                                                        <i class="bi bi-download me-1"></i>
                                                        <span th:text="'附件' + ${status.count}">附件</span>
                                                    </button>
                                                </div>
                                            </div>
                                            <span th:unless="${record.attachments != null && !#strings.isEmpty(record.attachments)}">-</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${records.isEmpty()}">
                            <p class="text-muted mb-0">暂无审批记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/workflow/instance-form.js}"></script>
    <script>
        // 下载工作流附件并选择目录的函数
        async function downloadWorkflowAttachmentWithDirectorySelection(attachmentPath) {
            try {
                // 先检查文件是否存在和权限
                const checkResponse = await fetch('/workflow/instances/check/attachment?path=' + encodeURIComponent(attachmentPath));

                if (!checkResponse.ok) {
                    const errorMessage = await checkResponse.text();
                    alert(errorMessage);
                    return;
                }

                // 权限检查通过，继续选择下载目录
                // 检查浏览器是否支持文件系统访问API
                if ('showDirectoryPicker' in window) {
                    // 使用现代浏览器的目录选择API
                    showDirectoryPicker()
                        .then(directoryHandle => {
                            // 获取文件信息并下载
                            downloadWorkflowAttachmentToDirectory(attachmentPath, directoryHandle);
                        })
                        .catch(error => {
                            if (error.name !== 'AbortError') {
                                console.error('目录选择失败:', error);
                                alert('目录选择失败，将使用默认下载方式');
                                // 回退到默认下载方式
                                window.location.href = '/workflow/instances/download/attachment?path=' + encodeURIComponent(attachmentPath);
                            }
                        });
                } else {
                    // 对于不支持的浏览器，显示提示并使用默认下载
                    alert('您的浏览器不支持目录选择功能，将使用默认下载方式');
                    window.location.href = '/workflow/instances/download/attachment?path=' + encodeURIComponent(attachmentPath);
                }
            } catch (error) {
                console.error('权限检查失败:', error);
                alert('权限检查失败: ' + error.message);
            }
        }

        // 下载工作流附件到指定目录
        async function downloadWorkflowAttachmentToDirectory(attachmentPath, directoryHandle) {
            try {
                // 获取文件数据
                const response = await fetch('/workflow/instances/download/attachment?path=' + encodeURIComponent(attachmentPath));

                if (!response.ok) {
                    throw new Error('下载失败: ' + response.statusText);
                }

                // 检查响应的Content-Type，判断是否为错误消息
                const contentType = response.headers.get('Content-Type');
                if (contentType && contentType.includes('text/plain')) {
                    // 这是一个错误消息，不是文件
                    const errorMessage = await response.text();
                    alert(errorMessage);
                    return;
                }

                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'workflow_attachment';

                // 检查Content-Disposition是否为inline（错误消息）
                if (contentDisposition && contentDisposition.includes('inline')) {
                    const errorMessage = await response.text();
                    alert(errorMessage);
                    return;
                }

                if (contentDisposition) {
                    // 优先尝试解析RFC 5987格式的filename*
                    const filenameStarMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
                    if (filenameStarMatch && filenameStarMatch[1]) {
                        try {
                            filename = decodeURIComponent(filenameStarMatch[1]);
                        } catch (e) {
                            console.warn('RFC 5987文件名解码失败，尝试标准格式');
                        }
                    }

                    // 如果RFC 5987格式解析失败，回退到标准格式（也是编码后的）
                    if (filename === 'workflow_attachment') {
                        const filenameMatch = contentDisposition.match(/filename="([^"]+)"/);
                        if (filenameMatch && filenameMatch[1]) {
                            try {
                                filename = decodeURIComponent(filenameMatch[1]);
                            } catch (e) {
                                filename = filenameMatch[1]; // 如果解码失败，使用原始值
                            }
                        }
                    }
                } else {
                    // 如果没有Content-Disposition头，尝试从路径中提取文件名
                    const pathParts = attachmentPath.split('/');
                    if (pathParts.length > 0) {
                        filename = pathParts[pathParts.length - 1];
                    }
                }

                // 获取文件数据
                const blob = await response.blob();

                // 创建文件句柄
                const fileHandle = await directoryHandle.getFileHandle(filename, { create: true });
                const writable = await fileHandle.createWritable();

                // 写入文件
                await writable.write(blob);
                await writable.close();

                alert('文件已成功下载到选择的目录: ' + filename);
            } catch (error) {
                console.error('下载工作流附件时出错:', error);
                alert('下载失败: ' + error.message + '\n\n将使用默认下载方式');
                // 回退到默认下载方式
                window.location.href = '/workflow/instances/download/attachment?path=' + encodeURIComponent(attachmentPath);
            }
        }
    </script>
</body>
</html>
