<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程实例管理')}">
    <meta charset="UTF-8">
    <title>流程实例管理</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">流程实例管理</h1>
            <div>
                <a th:href="@{/workflow}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                <a th:href="@{/workflow/instances/create}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i> 发起流程
                </a>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">搜索条件</h5>
                        <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="collapse show" id="searchCollapse">
                        <div class="card-body">
                            <form th:action="@{/workflow/instances}" method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="keyword" class="form-label">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" th:value="${keyword}" placeholder="流程标题">
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">全部</option>
                                        <option th:each="statusOption : ${statuses}"
                                                th:value="${statusOption}"
                                                th:text="${statusOption.name() == 'DRAFT' ? '草稿' :
                                                         (statusOption.name() == 'PROCESSING' ? '处理中' :
                                                         (statusOption.name() == 'APPROVED' ? '已批准' :
                                                         (statusOption.name() == 'REJECTED' ? '已拒绝' :
                                                         (statusOption.name() == 'CANCELED' ? '已取消' : '已终止'))))}"
                                                th:selected="${status == statusOption}">
                                            状态
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="initiator" class="form-label">发起人</label>
                                    <select class="form-select" id="initiator" name="initiator">
                                        <option value="">全部</option>
                                        <option th:each="person : ${personnelList}"
                                                th:value="${person}"
                                                th:text="${person}"
                                                th:selected="${initiator == person}">
                                            发起人
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="templateId" class="form-label">流程模板</label>
                                    <select class="form-select" id="templateId" name="templateId">
                                        <option value="">全部</option>
                                        <th:block th:each="template : ${templates}">
                                            <option th:if="${template != null}"
                                                    th:value="${template != null ? template.templateId : ''}"
                                                    th:text="${template != null ? template.templateName : '未知模板'}"
                                                    th:selected="${templateId != null && template != null && #strings.toString(template.templateId) == templateId}">
                                                流程模板名称
                                            </option>
                                        </th:block>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="businessType" class="form-label">业务类型</label>
                                    <select class="form-select" id="businessType" name="businessType">
                                        <option value="">全部</option>
                                        <option value="项目" th:selected="${businessType == '项目'}">项目</option>
                                        <option value="任务" th:selected="${businessType == '任务'}">任务</option>
                                        <option value="通用" th:selected="${businessType == '通用'}">通用</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="businessId" class="form-label">业务ID</label>
                                    <input type="text" class="form-control" id="businessId" name="businessId" th:value="${businessId}">
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                    <a th:href="@{/workflow/instances}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i> 清除
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流程实例列表 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">流程实例列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 成功/错误消息 -->
                        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <span th:text="${message}">操作成功</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                        <!-- 实例列表表格 -->
                        <div class="table-responsive" th:if="${!instancePage.empty}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>流程标题</th>
                                        <th>流程模板</th>
                                        <th>状态</th>
                                        <th>流程进度</th>
                                        <th>发起人</th>
                                        <th>待审批人</th>
                                        <th>创建时间</th>
                                        <th>业务类型</th>
                                        <th>业务ID</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="instance : ${instancePage.content}">
                                        <td th:text="${instance.title}">流程标题</td>
                                        <td>
                                            <span th:if="${instance.template != null}" th:text="${instance.template.templateName}">流程模板</span>
                                            <span th:unless="${instance.template != null}" class="text-muted">未知模板</span>
                                        </td>
                                        <td>
                                            <span th:if="${instance.status != null && instance.status.name() == 'DRAFT'}" class="badge bg-secondary">草稿</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'PROCESSING'}" class="badge bg-primary">处理中</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'APPROVED'}" class="badge bg-success">已批准</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'REJECTED'}" class="badge bg-danger">已拒绝</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'CANCELED'}" class="badge bg-warning">已取消</span>
                                            <span th:if="${instance.status != null && instance.status.name() == 'TERMINATED'}" class="badge bg-dark">已终止</span>
                                            <span th:if="${instance.status == null}" class="badge bg-secondary">未知状态</span>
                                        </td>
                                        <td>
                                            <!-- 流程进度显示 -->
                                            <span class="badge bg-info" th:text="${(instanceSteps != null && instanceSteps[instance.instanceId] != null ? instanceSteps[instance.instanceId] : 0) + 
                                            '/' + (instance.stepCount != null ? instance.stepCount : 0) + ' 步'}">0/0 步</span>
                                        </td>
                                        <td th:text="${instance.initiator}">发起人</td>
                                        <td th:text="${instance.currentApprover != null ? instance.currentApprover : '-'}">待审批人</td>
                                        <td th:text="${instance.createdDateTime != null ? #temporals.format(instance.createdDateTime, 'yyyy-MM-dd HH:mm') : '-'}">创建时间</td>
                                        <td th:text="${instance.businessType != null ? instance.businessType : '-'}">业务类型</td>
                                        <td>
                                            <!-- 当业务类型为"任务"时，显示链接 -->
                                            <a th:if="${instance.businessType == '任务' && instance.businessId != null}"
                                               th:href="@{/tasks/{id}(id=${instance.businessId})}"
                                               th:text="${instance.businessId}"
                                               title="点击查看任务详情"
                                               target="_blank">业务ID</a>
                                            <!-- 其他情况显示普通文本 -->
                                            <span th:unless="${instance.businessType == '任务' && instance.businessId != null}"
                                                  th:text="${instance.businessId != null ? instance.businessId : '-'}">业务ID</span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:href="@{/workflow/instances/{id}(id=${instance.instanceId})}" class="btn btn-sm btn-outline-primary" title="查看">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a th:if="${instance.status != null && instance.status.name() == 'DRAFT'}" th:href="@{/workflow/approval/{id}(id=${instance.instanceId})}" class="btn btn-sm btn-outline-success" title="提交">
                                                    <i class="bi bi-send"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" title="删除" 
                                                        data-bs-toggle="modal" th:data-bs-target="'#deleteModal-' + ${instance.instanceId}">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                                <!-- 删除确认模态框 -->
                                                <div class="modal fade" th:id="'deleteModal-' + ${instance.instanceId}" tabindex="-1" th:aria-labelledby="'deleteModalLabel-' + ${instance.instanceId}" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" th:id="'deleteModalLabel-' + ${instance.instanceId}">确认删除</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                确定要删除流程实例 "<span th:text="${instance.title}"></span>" 吗？此操作不可撤销。
                                                                <div class="alert alert-warning mt-2">
                                                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                                    如果此流程与任务相关联，删除后任务的审批状态将被重置。
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                                <form th:action="@{/workflow/instances/{id}/delete(id=${instance.instanceId})}" method="post">
                                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${instancePage.empty}">
                            <p class="text-muted mb-0">暂无流程实例</p>
                        </div>
                    </div>
                    <!-- 分页控件 -->
                    <div class="card-footer" th:if="${instancePage.totalPages > 0}">
                        <div th:replace="~{fragments/pagination :: pagination(${instancePage}, @{/workflow/instances})}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
