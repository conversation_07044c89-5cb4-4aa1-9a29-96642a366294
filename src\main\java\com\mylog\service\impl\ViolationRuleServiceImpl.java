package com.mylog.service.impl;

import com.mylog.model.ViolationRule;
import com.mylog.repository.ViolationRuleRepository;
import com.mylog.service.ViolationRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 违规规则服务实现类
 */
@Service
public class ViolationRuleServiceImpl implements ViolationRuleService {

    private static final Logger logger = LoggerFactory.getLogger(ViolationRuleServiceImpl.class);

    private final ViolationRuleRepository violationRuleRepository;

    @Autowired
    public ViolationRuleServiceImpl(ViolationRuleRepository violationRuleRepository) {
        this.violationRuleRepository = violationRuleRepository;
    }

    @Override
    public List<ViolationRule> getAllRules() {
        return violationRuleRepository.findAll();
    }

    @Override
    public List<ViolationRule> getAllEnabledRules() {
        return violationRuleRepository.findByEnabledTrueOrderByPriorityAsc();
    }

    @Override
    public Optional<ViolationRule> getRuleById(Long ruleId) {
        return violationRuleRepository.findById(ruleId);
    }

    @Override
    public ViolationRule saveRule(ViolationRule rule) {
        if (rule.getCreatedDateTime() == null) {
            rule.setCreatedDateTime(LocalDateTime.now());
        }
        rule.setLastModifiedDateTime(LocalDateTime.now());
        return violationRuleRepository.save(rule);
    }

    @Override
    public void deleteRule(Long ruleId) {
        violationRuleRepository.deleteById(ruleId);
    }

    @Override
    public ViolationRule enableRule(Long ruleId) {
        Optional<ViolationRule> ruleOpt = violationRuleRepository.findById(ruleId);
        if (ruleOpt.isPresent()) {
            ViolationRule rule = ruleOpt.get();
            rule.setEnabled(true);
            rule.setLastModifiedDateTime(LocalDateTime.now());
            return violationRuleRepository.save(rule);
        }
        return null;
    }

    @Override
    public ViolationRule disableRule(Long ruleId) {
        Optional<ViolationRule> ruleOpt = violationRuleRepository.findById(ruleId);
        if (ruleOpt.isPresent()) {
            ViolationRule rule = ruleOpt.get();
            rule.setEnabled(false);
            rule.setLastModifiedDateTime(LocalDateTime.now());
            return violationRuleRepository.save(rule);
        }
        return null;
    }

    @Override
    public void initDefaultRules() {
        // 检查是否已存在规则
        if (violationRuleRepository.count() > 0) {
            logger.info("已存在违规规则，跳过初始化默认规则");
            return;
        }

        logger.info("开始初始化默认违规规则");

        // 创建默认规则
        ViolationRule defaultRule = new ViolationRule();
        defaultRule.setRuleName("默认违规规则");
        defaultRule.setDescription("默认的违规任务筛选规则：进行中任务，排除包含'待调试'和'人'的任务，评论天数>7（特殊任务>15）");
        defaultRule.setTaskStatus("进行中");
        defaultRule.setTaskNameExcludes("待调试,人");
        defaultRule.setCommentDaysThreshold(7.0);
        defaultRule.setSpecialTaskCommentDaysThreshold(15.0);
        defaultRule.setSpecialTaskIdentifier("培训");
        defaultRule.setEnabled(true);
        defaultRule.setPriority(0);
        defaultRule.setScore(100); // 默认违规扣10分
        defaultRule.setCreatedBy("系统");

        violationRuleRepository.save(defaultRule);

        logger.info("默认违规规则初始化完成");
    }
}
