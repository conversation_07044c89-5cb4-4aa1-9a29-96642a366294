<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('任务评论表单')}">
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>任务评论表单</title>
    <script>
        // 页面加载前执行
        window.onpageshow = function(event) {
            // 如果是从bfcache中加载的页面，则立即跳转到来源页面
            if (event.persisted) {
                returnToReferer();
            }
        };

        // 防止在浏览器历史中记录该页面
        history.replaceState(null, document.title, window.location.href);

        // 强制禁用浏览器的后退功能，直接返回到引用页面
        window.addEventListener('popstate', function() {
            returnToReferer();
        });

        // 返回到引用页面的函数
        function returnToReferer() {
            var referer = sessionStorage.getItem('subTaskReferer');
            if (referer) {
                window.location.replace(referer);
            } else {
                // 如果没有保存的引用页面，尝试从表单获取
                var refererInput = document.querySelector('input[name="referer"]');
                if (refererInput && refererInput.value) {
                    window.location.replace(refererInput.value);
                } else {
                    // 如果都没有，返回到任务列表
                    window.location.replace('/tasks/order-tasks');
                }
            }
        }
    </script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">添加任务评论</h1>
        </div>

        <div class="row">
            <div class="col-md-12">
                <form th:action="@{/subtasks/save}" method="post" th:object="${subTask}" class="needs-validation" novalidate>
                    <input type="hidden" th:field="*{subTaskId}" />
                    <input type="hidden" th:field="*{taskId}" />
                    <input type="hidden" th:field="*{sequenceNumber}" />
                    <input type="hidden" name="referer" th:value="${referer}" id="refererInput" />

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">任务评论信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="logContent" class="form-label">内容<span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="logContent" th:field="*{logContent}" rows="3" required></textarea>
                                    <div class="invalid-feedback">请输入任务评论内容</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">保存</button>
                                    <a href="#" onclick="returnToReferer(); return false;" class="btn btn-secondary">取消</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 保存引用页面到会话存储
            var refererInput = document.getElementById('refererInput');
            if (refererInput && refererInput.value) {
                sessionStorage.setItem('subTaskReferer', refererInput.value);
            }

            // 禁用浏览器缓存
            window.addEventListener('beforeunload', function() {
                // 不显示提示，但可以确保页面在卸载后不会被缓存
            });

            // 表单验证
            var forms = document.querySelectorAll('.needs-validation');
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        });

        // 检测历史状态变化
        (function() {
            // 标记当前页面，使其不会被添加到历史记录
            sessionStorage.setItem('noHistoryPage', window.location.href);

            // 创建一个检测函数，确保用户不能通过后退按钮返回到此页面
            function checkBrowserNavigation() {
                if (performance && performance.navigation) {
                    // 如果是通过后退按钮访问的
                    if (performance.navigation.type === 2) {
                        returnToReferer();
                    }
                }
            }

            // 每隔一段时间检查一次导航类型
            setInterval(checkBrowserNavigation, 200);
        })();
    </script>
</body>
</html>
