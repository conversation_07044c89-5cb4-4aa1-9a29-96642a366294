package com.mylog.config;

import org.hibernate.jpa.boot.spi.IntegratorProvider;
import org.hibernate.integrator.spi.Integrator;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.service.spi.SessionFactoryServiceRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Collections;

/**
 * Hibernate日志禁用配置类
 * 通过自定义Integrator和拦截器来禁用Hibernate的SQL日志输出
 */
@Configuration
public class HibernateLogSuppressorConfig {

    @Autowired
    private HibernateSqlLogInterceptor sqlLogInterceptor;

    /**
     * 创建一个Hibernate属性自定义器，用于完全禁用Hibernate的SQL日志记录
     */
    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
        return hibernateProperties -> {
            // 禁用所有Hibernate SQL日志相关选项
            hibernateProperties.put("hibernate.show_sql", "false");
            hibernateProperties.put("hibernate.format_sql", "false");
            hibernateProperties.put("hibernate.use_sql_comments", "false");
            hibernateProperties.put("hibernate.generate_statistics", "false");
            
            // 设置日志级别为OFF
            Map<String, String> loggerLevels = new HashMap<>();
            loggerLevels.put("org.hibernate", "OFF");
            loggerLevels.put("org.hibernate.SQL", "OFF");
            loggerLevels.put("org.hibernate.type", "OFF");
            loggerLevels.put("org.hibernate.engine", "OFF");
            loggerLevels.put("org.hibernate.jdbc", "OFF");
            loggerLevels.put("org.hibernate.orm.jdbc", "OFF");
            hibernateProperties.put("hibernate.logger", loggerLevels);
            
            // 注册SQL语句拦截器，可以阻止SQL被记录
            hibernateProperties.put("hibernate.session_factory.statement_inspector", sqlLogInterceptor);
            
            // 注册自定义Integrator以禁用SQL日志
            hibernateProperties.put("hibernate.integrator_provider", 
                (IntegratorProvider) () -> Collections.singletonList(new SQLLogSuppressorIntegrator()));
        };
    }
    
    /**
     * 定义一个自定义的Hibernate Integrator，用于禁用SQL日志
     */
    public static class SQLLogSuppressorIntegrator implements Integrator {
        @Override
        public void integrate(org.hibernate.boot.Metadata metadata, SessionFactoryImplementor sessionFactory, SessionFactoryServiceRegistry serviceRegistry) {
            // 通过EventListenerRegistry禁用SQL日志事件
            final EventListenerRegistry eventListenerRegistry = serviceRegistry.getService(EventListenerRegistry.class);
            
            // 禁用所有可能产生SQL日志的事件监听器
            // 这里我们不需要添加任何监听器，只是确保系统知道我们的集成器被加载
        }
        
        @Override
        public void disintegrate(SessionFactoryImplementor sessionFactory, SessionFactoryServiceRegistry serviceRegistry) {
            // 无需特殊清理
        }
    }
}