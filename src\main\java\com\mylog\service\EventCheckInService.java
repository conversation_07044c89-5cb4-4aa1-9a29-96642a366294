package com.mylog.service;

import com.mylog.dto.EventCheckInDTO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 事件签到服务接口
 */
public interface EventCheckInService {
      /**
     * 用户签到
     * @param reminderId 提醒ID
     * @param userId 用户ID
     * @return 签到记录
     */
    EventCheckInDTO checkIn(Long reminderId, String userId);
    
    /**
     * 用户签到（支持备注和位置信息）
     * @param reminderId 提醒ID
     * @param userId 用户ID
     * @param notes 签到备注
     * @param latitude 纬度
     * @param longitude 经度
     * @return 签到记录
     */
    EventCheckInDTO checkIn(Long reminderId, String userId, String notes, Double latitude, Double longitude);
    
    /**
     * 检查用户是否可以签到
     * @param reminderId 提醒ID
     * @param userId 用户ID
     * @return 是否可以签到
     */
    boolean canCheckIn(Long reminderId, String userId);
    
    /**
     * 获取用户的签到记录
     * @param reminderId 提醒ID
     * @param userId 用户ID
     * @return 签到记录
     */
    Optional<EventCheckInDTO> getCheckInRecord(Long reminderId, String userId);
    
    /**
     * 获取事件的所有签到记录
     * @param eventId 事件ID
     * @return 签到记录列表
     */
    List<EventCheckInDTO> getEventCheckIns(Long eventId);
    
    /**
     * 获取用户的签到历史
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 签到记录列表
     */
    List<EventCheckInDTO> getUserCheckInHistory(String userId, LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * 检查签到是否在有效时间窗口内
     * @param reminderId 提醒ID
     * @return 是否在有效时间窗口内
     */
    boolean isWithinCheckInWindow(Long reminderId);
    
    /**
     * 获取签到统计信息
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 签到统计
     */
    CheckInStatistics getCheckInStatistics(String userId, LocalDateTime startDate, LocalDateTime endDate);
      /**
     * 签到统计信息内部类
     */
    class CheckInStatistics {
        private long totalReminders;
        private long checkedInCount;
        private long missedCount;
        private double checkInRate;
        
        public CheckInStatistics(long totalReminders, long checkedInCount, long missedCount, double checkInRate) {
            this.totalReminders = totalReminders;
            this.checkedInCount = checkedInCount;
            this.missedCount = missedCount;
            this.checkInRate = checkInRate;
        }
        
        // Getters
        public long getTotalReminders() { return totalReminders; }
        public long getCheckedInCount() { return checkedInCount; }
        public long getMissedCount() { return missedCount; }
        public double getCheckInRate() { return checkInRate; }
        
        // Setters
        public void setTotalReminders(long totalReminders) { this.totalReminders = totalReminders; }
        public void setCheckedInCount(long checkedInCount) { this.checkedInCount = checkedInCount; }
        public void setMissedCount(long missedCount) { this.missedCount = missedCount; }
        public void setCheckInRate(double checkInRate) { this.checkInRate = checkInRate; }
    }

    // ========== 为签到页面添加的新方法 ==========

    /**
     * 获取用户待签到事件列表
     * @param userId 用户ID
     * @return 待签到事件列表
     */
    List<PendingCheckInDTO> getPendingCheckIns(String userId);

    /**
     * 获取简化版签到统计信息
     * @param userId 用户ID
     * @return 签到统计
     */
    SimpleStatistics getSimpleCheckInStatistics(String userId);

    /**
     * 获取分页签到历史
     * @param userId 用户ID
     * @param filter 过滤条件
     * @param page 页码
     * @param size 页大小
     * @return 分页签到历史
     */
    PagedCheckInHistory getPagedCheckInHistory(String userId, String filter, int page, int size);

    /**
     * 通过事件ID进行签到
     * @param eventId 事件ID
     * @param userId 用户ID
     * @param notes 签到备注
     * @param latitude 纬度
     * @param longitude 经度
     * @return 签到记录
     */
    EventCheckInDTO checkInByEvent(Long eventId, String userId, String notes, Double latitude, Double longitude);

    /**
     * 待签到事件DTO
     */
    class PendingCheckInDTO {
        private Long eventId;
        private String eventTitle;
        private EventReminderDTO eventReminder;
        
        public PendingCheckInDTO() {}
        
        public PendingCheckInDTO(Long eventId, String eventTitle, EventReminderDTO eventReminder) {
            this.eventId = eventId;
            this.eventTitle = eventTitle;
            this.eventReminder = eventReminder;
        }
        
        // Getters and Setters
        public Long getEventId() { return eventId; }
        public void setEventId(Long eventId) { this.eventId = eventId; }
        
        public String getEventTitle() { return eventTitle; }
        public void setEventTitle(String eventTitle) { this.eventTitle = eventTitle; }
        
        public EventReminderDTO getEventReminder() { return eventReminder; }
        public void setEventReminder(EventReminderDTO eventReminder) { this.eventReminder = eventReminder; }
    }

    /**
     * 事件提醒DTO
     */    class EventReminderDTO {
        private Long id;
        private String title;
        private String message;
        private LocalDateTime reminderTime;
        private boolean requiresCheckIn;
        private Integer checkInWindowMinutes;
        
        public EventReminderDTO() {}
          public EventReminderDTO(Long id, String title, String message, LocalDateTime reminderTime, 
                               boolean requiresCheckIn, Integer checkInWindowMinutes) {
            this.id = id;
            this.title = title;
            this.message = message;
            this.reminderTime = reminderTime;
            this.requiresCheckIn = requiresCheckIn;
            this.checkInWindowMinutes = checkInWindowMinutes;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
          public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public LocalDateTime getReminderTime() { return reminderTime; }
        public void setReminderTime(LocalDateTime reminderTime) { this.reminderTime = reminderTime; }
        
        public boolean isRequiresCheckIn() { return requiresCheckIn; }
        public void setRequiresCheckIn(boolean requiresCheckIn) { this.requiresCheckIn = requiresCheckIn; }
        
        public Integer getCheckInWindowMinutes() { return checkInWindowMinutes; }
        public void setCheckInWindowMinutes(Integer checkInWindowMinutes) { this.checkInWindowMinutes = checkInWindowMinutes; }
    }

    /**
     * 简化版统计信息
     */
    class SimpleStatistics {
        private long todayCount;
        private long weekCount;
        private long monthCount;
        private long totalCount;
        
        public SimpleStatistics() {}
        
        public SimpleStatistics(long todayCount, long weekCount, long monthCount, long totalCount) {
            this.todayCount = todayCount;
            this.weekCount = weekCount;
            this.monthCount = monthCount;
            this.totalCount = totalCount;
        }
        
        // Getters and Setters
        public long getTodayCount() { return todayCount; }
        public void setTodayCount(long todayCount) { this.todayCount = todayCount; }
        
        public long getWeekCount() { return weekCount; }
        public void setWeekCount(long weekCount) { this.weekCount = weekCount; }
        
        public long getMonthCount() { return monthCount; }
        public void setMonthCount(long monthCount) { this.monthCount = monthCount; }
        
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }
    }

    /**
     * 分页签到历史
     */
    class PagedCheckInHistory {
        private List<EventCheckInDTO> content;
        private int totalPages;
        private long totalElements;
        
        public PagedCheckInHistory() {}
        
        public PagedCheckInHistory(List<EventCheckInDTO> content, int totalPages, long totalElements) {
            this.content = content;
            this.totalPages = totalPages;
            this.totalElements = totalElements;
        }
        
        // Getters and Setters
        public List<EventCheckInDTO> getContent() { return content; }
        public void setContent(List<EventCheckInDTO> content) { this.content = content; }
        
        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
        
        public long getTotalElements() { return totalElements; }
        public void setTotalElements(long totalElements) { this.totalElements = totalElements; }
    }
}
