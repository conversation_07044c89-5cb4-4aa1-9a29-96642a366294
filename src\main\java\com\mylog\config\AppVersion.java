package com.mylog.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 应用程序版本信息管理类 - 编译时版本信息
 */
@Component
public class AppVersion {
    
    private static final Logger logger = LoggerFactory.getLogger(AppVersion.class);
    
    @Value("${app.name:MyLog Web}")
    private String appName;
    
    @Value("${app.version:0.0.1-SNAPSHOT}")
    private String appVersion;
    
    @Value("${app.description:Web版本的MyLog管理系统}")
    private String appDescription;
    
    @Value("${app.build.version:1.250529.0000}")
    private String buildVersion;
    
    @Value("${app.build.timestamp:未知时间}")
    private String buildTimestamp;
    
    private String versionString;
    
    /**
     * 初始化版本信息 - 使用编译时生成的版本号
     */
    @PostConstruct
    public void init() {
        try {
            // 使用编译时生成的版本号
            versionString = buildVersion;
            
            // 解析版本号中的时间信息用于说明
            String timeExplanation = "";
            if (buildVersion.matches("1\\.\\d{6}\\.\\d{4}")) {
                String[] parts = buildVersion.split("\\.");
                if (parts.length == 3) {
                    String datePart = parts[1]; // YYMMDD
                    String timePart = parts[2]; // HHMM
                    
                    String year = "20" + datePart.substring(0, 2);
                    String month = datePart.substring(2, 4);
                    String day = datePart.substring(4, 6);
                    String hour = timePart.substring(0, 2);
                    String minute = timePart.substring(2, 4);
                    
                    timeExplanation = String.format(" = 主版本1 + %s年%s月%s日 + %s时%s分", 
                        year, month, day, hour, minute);
                }
            }
            
            // 格式化构建时间戳显示
            String displayBuildTime = buildTimestamp;
            if (buildTimestamp != null && !buildTimestamp.equals("未知时间") && !buildTimestamp.startsWith("${")) {
                try {
                    // 如果是ISO格式，转换为易读格式
                    if (buildTimestamp.contains("T")) {
                        displayBuildTime = buildTimestamp.replace("T", " ").replace("Z", "");
                    }
                } catch (Exception e) {
                    // 保持原格式
                }
            }
            
            logger.info("=== 应用程序信息 (编译时版本) ===");
            logger.info("名称: {}", appName);
            logger.info("版本: {} (编译时生成)", versionString);
            logger.info("Maven版本: {}", appVersion);
            logger.info("描述: {}", appDescription);
            logger.info("构建时间: {}", displayBuildTime);
            if (!timeExplanation.isEmpty()) {
                logger.info("版本说明: {}{}", versionString, timeExplanation);
            }
            logger.info("版本特性: 编译时固定，同一份代码版本号不变");
            logger.info("================================");
            
        } catch (Exception e) {
            logger.error("初始化版本信息失败", e);
            versionString = "1.250529.0000";
        }
    }
    
    /**
     * 获取当前应用程序版本
     * @return 版本字符串
     */
    public String getVersion() {
        return versionString;
    }
    
    /**
     * 获取应用程序名称
     * @return 应用程序名称
     */
    public String getAppName() {
        return appName;
    }
    
    /**
     * 获取应用程序描述
     * @return 应用程序描述
     */
    public String getAppDescription() {
        return appDescription;
    }
    
    /**
     * 获取构建时间
     * @return 构建时间字符串
     */
    public String getBuildTimestamp() {
        return buildTimestamp;
    }
}
