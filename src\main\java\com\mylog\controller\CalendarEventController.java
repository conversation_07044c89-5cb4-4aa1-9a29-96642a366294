package com.mylog.controller;

import com.mylog.dto.CalendarEventDTO;
import com.mylog.model.CalendarEvent;
import com.mylog.service.CalendarEventService;
import com.mylog.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletRequest;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 日历事件控制器
 */
@RestController
@RequestMapping(value = "/api/events", produces = MediaType.APPLICATION_JSON_VALUE)
@CrossOrigin(origins = "*")
public class CalendarEventController {
    
    @Autowired
    private CalendarEventService eventService;    /**
     * 创建事件
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createEvent(@Valid @RequestBody CalendarEventDTO eventDTO, 
                                                          HttpServletRequest request) {
        // 获取请求ID用于追踪
        String requestId = request.getHeader("X-Request-ID");
        if (requestId == null) {
            requestId = "BACKEND_" + System.currentTimeMillis();
        }
        
        System.out.println("🔄 [" + requestId + "] ============= 控制器收到创建事件请求 =============");
        System.out.println("📊 [" + requestId + "] 请求时间: " + LocalDateTime.now());
        System.out.println("📝 [" + requestId + "] 事件标题: " + eventDTO.getTitle());
        System.out.println("📂 [" + requestId + "] 日历ID: " + eventDTO.getCalendarId());
        System.out.println("👤 [" + requestId + "] 创建者ID: " + eventDTO.getCreatorId());
        System.out.println("⏰ [" + requestId + "] 开始时间: " + eventDTO.getStartTime());
        System.out.println("⏰ [" + requestId + "] 结束时间: " + eventDTO.getEndTime());
        System.out.println("🔔 [" + requestId + "] 提醒数量: " + (eventDTO.getReminders() != null ? eventDTO.getReminders().size() : 0));
        System.out.println("🌐 [" + requestId + "] 客户端IP: " + request.getRemoteAddr());
        System.out.println("🔗 [" + requestId + "] User-Agent: " + request.getHeader("User-Agent"));
        
        Map<String, Object> response = new HashMap<>();
        try {
            System.out.println("🚀 [" + requestId + "] 开始调用服务层创建事件...");
            long serviceStart = System.currentTimeMillis();
            
            CalendarEventDTO createdEvent = eventService.createEvent(eventDTO);
            
            long serviceEnd = System.currentTimeMillis();
            System.out.println("⏱️ [" + requestId + "] 服务层耗时: " + (serviceEnd - serviceStart) + "ms");
            
            response.put("success", true);
            response.put("message", "事件创建成功");
            response.put("data", createdEvent);
            response.put("requestId", requestId); // 返回请求ID
            
            System.out.println("✅ [" + requestId + "] 事件创建成功，ID: " + createdEvent.getId());
            System.out.println("📊 [" + requestId + "] 响应时间: " + LocalDateTime.now());
            System.out.println("🎉 [" + requestId + "] ============= 请求处理完成 =============");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("💥 [" + requestId + "] ============= 事件创建失败 =============");
            System.err.println("❌ [" + requestId + "] 错误信息: " + e.getMessage());
            System.err.println("🔍 [" + requestId + "] 错误类型: " + e.getClass().getSimpleName());
            System.err.println("📊 [" + requestId + "] 失败时间: " + LocalDateTime.now());
            e.printStackTrace();
            
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("requestId", requestId);
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新事件
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateEvent(@PathVariable Long id, 
                                                         @Valid @RequestBody CalendarEventDTO eventDTO) {
        Map<String, Object> response = new HashMap<>();
        try {
            CalendarEventDTO updatedEvent = eventService.updateEvent(id, eventDTO);
            response.put("success", true);
            response.put("message", "事件更新成功");
            response.put("data", updatedEvent);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除事件
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteEvent(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            eventService.deleteEvent(id);
            response.put("success", true);
            response.put("message", "事件删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取事件详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getEvent(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            CalendarEventDTO event = eventService.getEventById(id)
                .orElseThrow(() -> new IllegalArgumentException("事件不存在"));
            response.put("success", true);
            response.put("data", event);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取日历的事件列表
     */
    @GetMapping("/calendar/{calendarId}")
    public ResponseEntity<Map<String, Object>> getEventsByCalendar(@PathVariable Long calendarId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByCalendarId(calendarId);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取用户的事件列表
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getEventsByUser(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByUserId(userId);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }    /**
     * 根据时间范围获取当前用户可访问的事件（包括共享日历事件）
     */
    @GetMapping("/range")
    public ResponseEntity<Map<String, Object>> getEventsByTimeRange(
            @RequestParam String startTime,
            @RequestParam String endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取当前用户ID
            Long currentUserId = SecurityUtils.getCurrentUserId();
            if (currentUserId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }
            
            // 转换为LocalDateTime - 增强解析能力，支持多种格式
            LocalDateTime startDateTime, endDateTime;
            
            try {
                // 尝试标准格式 "YYYY-MM-DD HH:MM:SS"
                startDateTime = LocalDateTime.parse(startTime.replace(" ", "T"));
                endDateTime = LocalDateTime.parse(endTime.replace(" ", "T"));
            } catch (Exception e) {
                // 打印异常信息以便诊断
                System.out.println("解析标准格式失败: " + e.getMessage());
                System.out.println("尝试其他格式解析，收到的时间字符串: " + startTime + " 和 " + endTime);
                
                // 如果标准格式解析失败，尝试其他格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                startDateTime = LocalDateTime.parse(startTime, formatter);
                endDateTime = LocalDateTime.parse(endTime, formatter);
            }
            
            // 记录日志
            System.out.println("转换后的时间范围: " + startDateTime + " 到 " + endDateTime);
            System.out.println("当前用户ID: " + currentUserId);
            
            // 使用新的方法获取用户可访问的事件（包括共享日历事件）
            List<CalendarEventDTO> events = eventService.getAccessibleEventsByTimeRange(currentUserId, startDateTime, endDateTime);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "获取事件失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据日历ID和时间范围获取事件
     */
    @GetMapping("/calendar/{calendarId}/range")
    public ResponseEntity<Map<String, Object>> getEventsByCalendarAndTimeRange(
            @PathVariable Long calendarId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByCalendarIdAndTimeRange(calendarId, startTime, endTime);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据用户ID和时间范围获取事件
     */
    @GetMapping("/user/{userId}/range")
    public ResponseEntity<Map<String, Object>> getEventsByUserAndTimeRange(
            @PathVariable Long userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByUserIdAndTimeRange(userId, startTime, endTime);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据多个日历ID和时间范围获取事件
     */
    @PostMapping("/calendars/range")
    public ResponseEntity<Map<String, Object>> getEventsByCalendarsAndTimeRange(
            @RequestBody List<Long> calendarIds,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByCalendarIdsAndTimeRange(calendarIds, startTime, endTime);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 搜索事件
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchEvents(@RequestParam String keyword) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.searchEvents(keyword);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据用户ID搜索事件
     */
    @GetMapping("/user/{userId}/search")
    public ResponseEntity<Map<String, Object>> searchEventsByUser(@PathVariable Long userId, 
                                                                @RequestParam String keyword) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.searchEventsByUserId(userId, keyword);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取今日事件
     */
    @GetMapping("/user/{userId}/today")
    public ResponseEntity<Map<String, Object>> getTodayEvents(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getTodayEvents(userId);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取本周事件
     */
    @GetMapping("/user/{userId}/week")
    public ResponseEntity<Map<String, Object>> getWeekEvents(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getWeekEvents(userId);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取本月事件
     */
    @GetMapping("/user/{userId}/month")
    public ResponseEntity<Map<String, Object>> getMonthEvents(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getMonthEvents(userId);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据事件类型获取事件
     */
    @GetMapping("/type/{eventType}")
    public ResponseEntity<Map<String, Object>> getEventsByType(@PathVariable CalendarEvent.EventType eventType) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByType(eventType);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据优先级获取事件
     */
    @GetMapping("/priority/{priority}")
    public ResponseEntity<Map<String, Object>> getEventsByPriority(@PathVariable CalendarEvent.Priority priority) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getEventsByPriority(priority);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取重复事件
     */
    @GetMapping("/recurring")
    public ResponseEntity<Map<String, Object>> getRecurringEvents() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> events = eventService.getRecurringEvents();
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 创建重复事件实例
     */
    @PostMapping("/recurring/instances")
    public ResponseEntity<Map<String, Object>> createRecurringEventInstances(
            @Valid @RequestBody CalendarEventDTO eventDTO,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<CalendarEventDTO> instances = eventService.createRecurringEventInstances(eventDTO, endDate);
            response.put("success", true);
            response.put("message", "重复事件实例创建成功");
            response.put("data", instances);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
