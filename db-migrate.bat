@echo off
REM 执行数据库迁移脚本

echo 正在应用数据库迁移...
echo 执行工作流模板代码修改脚本...

REM 获取数据库路径
SET DB_PATH=./data/logManagement.db

REM 首先检查表结构，确认是否需要迁移
echo 检查表结构...
sqlite3 %DB_PATH% ".schema workflow_templates" > schema_check.txt

findstr /C:"templateCode" schema_check.txt > nul
IF %ERRORLEVEL% EQU 0 (
    echo 需要重命名templateCode为templateTitle并删除唯一性约束...

    REM 创建临时SQL文件
    echo PRAGMA foreign_keys=off; > migration.sql
    echo BEGIN TRANSACTION; >> migration.sql
    echo. >> migration.sql
    echo -- 获取当前表的列信息以动态创建新表 >> migration.sql
    echo CREATE TABLE workflow_templates_new AS SELECT * FROM workflow_templates WHERE 0; >> migration.sql
    echo. >> migration.sql
    echo -- 修改新表结构，将templateCode重命名为templateTitle >> migration.sql
    echo ALTER TABLE workflow_templates_new RENAME COLUMN templateCode TO templateTitle; >> migration.sql
    echo. >> migration.sql
    echo -- 复制数据 >> migration.sql
    echo INSERT INTO workflow_templates_new SELECT * FROM workflow_templates; >> migration.sql
    echo. >> migration.sql
    echo -- 删除旧表并重命名新表 >> migration.sql
    echo DROP TABLE workflow_templates; >> migration.sql
    echo ALTER TABLE workflow_templates_new RENAME TO workflow_templates; >> migration.sql
    echo. >> migration.sql
    echo COMMIT; >> migration.sql
    echo PRAGMA foreign_keys=on; >> migration.sql

    REM 执行SQL文件
    sqlite3 %DB_PATH% < migration.sql

    IF %ERRORLEVEL% NEQ 0 (
        echo 数据库迁移失败，错误代码: %ERRORLEVEL%
        del migration.sql
        del schema_check.txt
        exit /b %ERRORLEVEL%
    ) ELSE (
        echo 数据库迁移成功完成！
        del migration.sql
    )
) ELSE (
    echo 检测到表已迁移，templateTitle列已存在，跳过迁移操作。
)

REM 清理临时文件
del schema_check.txt

echo 完成所有迁移操作。
