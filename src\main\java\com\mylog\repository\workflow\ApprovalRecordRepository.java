package com.mylog.repository.workflow;

import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.ApprovalRecord.ApprovalAction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 审批记录存储库接口
 */
@Repository
public interface ApprovalRecordRepository extends JpaRepository<ApprovalRecord, Long> {
    
    /**
     * 根据实例ID查找审批记录列表，按创建时间排序
     */
    List<ApprovalRecord> findByInstanceInstanceIdOrderByCreatedDateAsc(Long instanceId);
    
    /**
     * 根据实例ID查找审批记录列表（分页版本）
     */
    Page<ApprovalRecord> findByInstanceInstanceIdOrderByCreatedDateAsc(Long instanceId, Pageable pageable);
    
    /**
     * 根据步骤ID查找审批记录列表
     */
    List<ApprovalRecord> findByStepStepId(Long stepId);
    
    /**
     * 根据审批人查找审批记录列表
     */
    List<ApprovalRecord> findByApprover(String approver);
    
    /**
     * 根据审批操作类型查找审批记录列表
     */
    List<ApprovalRecord> findByAction(ApprovalAction action);
    
    /**
     * 查找实例的最新审批记录
     */
    @Query("SELECT ar FROM ApprovalRecord ar WHERE ar.instance.instanceId = :instanceId ORDER BY ar.createdDate DESC")
    List<ApprovalRecord> findLatestRecord(@Param("instanceId") Long instanceId, Pageable pageable);
    
    /**
     * 查找用户的审批历史记录
     */
    @Query("SELECT ar FROM ApprovalRecord ar WHERE ar.approver = :username ORDER BY ar.createdDate DESC")
    Page<ApprovalRecord> findUserApprovalHistory(@Param("username") String username, Pageable pageable);

    /**
     * 根据附件路径查找审批记录
     */
    List<ApprovalRecord> findByAttachmentsContaining(String attachmentPath);
}
