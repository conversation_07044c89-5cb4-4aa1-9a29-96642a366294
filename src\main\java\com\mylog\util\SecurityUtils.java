package com.mylog.util;

import com.mylog.model.user.User;
import com.mylog.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 安全工具类
 * 提供获取当前登录用户信息的便捷方法
 */
@Component
public class SecurityUtils {
    
    private static UserService userService;
    
    @Autowired
    public void setUserService(UserService userService) {
        SecurityUtils.userService = userService;
    }
    
    /**
     * 获取当前登录用户的ID
     * @return 用户ID，如果未登录则返回null
     */
    public static Long getCurrentUserId() {
        String username = getCurrentUsername();
        if (username == null) {
            return null;
        }
        
        Optional<User> userOpt = userService.findUserByUsername(username);
        return userOpt.map(User::getUserId).orElse(null);
    }
    
    /**
     * 获取当前登录用户的用户名
     * @return 用户名，如果未登录则返回null
     */
    public static String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || 
            "anonymousUser".equals(authentication.getName())) {
            return null;
        }
        return authentication.getName();
    }
    
    /**
     * 获取当前登录用户的完整信息
     * @return 用户对象，如果未登录则返回空Optional
     */
    public static Optional<User> getCurrentUser() {
        String username = getCurrentUsername();
        if (username == null) {
            return Optional.empty();
        }
        
        return userService.findUserByUsername(username);
    }
    
    /**
     * 检查当前用户是否已登录
     * @return true表示已登录，false表示未登录
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.isAuthenticated() && 
               !"anonymousUser".equals(authentication.getName());
    }
}
