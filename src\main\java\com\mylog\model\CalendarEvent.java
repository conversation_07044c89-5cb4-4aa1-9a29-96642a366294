package com.mylog.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mylog.config.LocalDateTimeAttributeConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 日历事件实体类
 */
@Entity
@Table(name = "calendar_events")
public class CalendarEvent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "事件标题不能为空")
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Column(name = "description", length = 1000)
    private String description;    @NotNull(message = "开始时间不能为空")
    @Column(name = "start_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime startTime;
      @Column(name = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime endTime;
    
    @Column(name = "is_all_day", columnDefinition = "boolean default false")
    private Boolean isAllDay = false;
    
    @Column(name = "location", length = 200)
    private String location;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", length = 20)
    private EventType eventType = EventType.MEETING;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", length = 10)
    private Priority priority = Priority.NORMAL;
    
    @Column(name = "is_recurring", columnDefinition = "boolean default false")
    private Boolean isRecurring = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "recurrence_type", length = 20)
    private RecurrenceType recurrenceType;
    
    @Column(name = "recurrence_interval")
    private Integer recurrenceInterval;      @Column(name = "recurrence_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime recurrenceEndDate;
    
    @NotNull(message = "日历ID不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "calendar_id", nullable = false)
    private Calendar calendar;
    
    @NotNull(message = "创建用户ID不能为空")
    @Column(name = "creator_id", nullable = false)
    private Long creatorId;      @Column(name = "created_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime createdTime;
      @Column(name = "updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime updatedTime;
    
    @OneToMany(mappedBy = "event", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<EventReminder> reminders = new ArrayList<>();
    
    // 枚举定义
    public enum EventType {
        MEETING, TASK, REMINDER, APPOINTMENT, HOLIDAY, OTHER
    }
    
    public enum Priority {
        LOW, NORMAL, HIGH, URGENT
    }
    
    public enum RecurrenceType {
        DAILY, WEEKLY, MONTHLY, YEARLY
    }
      // 构造函数
    public CalendarEvent() {
        this.createdTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    public CalendarEvent(String title, LocalDateTime startTime, Calendar calendar, Long creatorId) {
        this();
        this.title = title;
        this.startTime = startTime;
        this.calendar = calendar;
        this.creatorId = creatorId;
    }
      // PrePersist and PreUpdate
    @PrePersist
    protected void onCreate() {
        createdTime = com.mylog.util.DateTimeUtils.nowInChina();
        updatedTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Boolean getIsAllDay() {
        return isAllDay;
    }
    
    public void setIsAllDay(Boolean isAllDay) {
        this.isAllDay = isAllDay;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public EventType getEventType() {
        return eventType;
    }
    
    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }
    
    public Priority getPriority() {
        return priority;
    }
    
    public void setPriority(Priority priority) {
        this.priority = priority;
    }
    
    public Boolean getIsRecurring() {
        return isRecurring;
    }
    
    public void setIsRecurring(Boolean isRecurring) {
        this.isRecurring = isRecurring;
    }
    
    public RecurrenceType getRecurrenceType() {
        return recurrenceType;
    }
    
    public void setRecurrenceType(RecurrenceType recurrenceType) {
        this.recurrenceType = recurrenceType;
    }
    
    public Integer getRecurrenceInterval() {
        return recurrenceInterval;
    }
    
    public void setRecurrenceInterval(Integer recurrenceInterval) {
        this.recurrenceInterval = recurrenceInterval;
    }
    
    public LocalDateTime getRecurrenceEndDate() {
        return recurrenceEndDate;
    }
    
    public void setRecurrenceEndDate(LocalDateTime recurrenceEndDate) {
        this.recurrenceEndDate = recurrenceEndDate;
    }
    
    public Calendar getCalendar() {
        return calendar;
    }
    
    public void setCalendar(Calendar calendar) {
        this.calendar = calendar;
    }
    
    public Long getCreatorId() {
        return creatorId;
    }
    
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public List<EventReminder> getReminders() {
        return reminders;
    }
    
    public void setReminders(List<EventReminder> reminders) {
        this.reminders = reminders;
    }
}
