package com.mylog.controller;

import com.mylog.model.ProjectTask;
import com.mylog.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/delegated-tasks")
public class DelegatedTaskController {
    
    private static final Logger logger = LoggerFactory.getLogger(DelegatedTaskController.class);
    
    @Autowired
    private TaskService taskService;
    
    @GetMapping
    public String listDelegatedTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {
        
        logger.info("访问分管任务列表页面");
        
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        
        // 获取分页的分管任务
        Page<ProjectTask> delegatedTasks = taskService.findMyDelegatedTasks(
            currentUsername, 
            PageRequest.of(page, size)
        );
        
        // 计算任务的评论天数并保存
        taskService.calculateCommentDays(delegatedTasks.getContent());
        logger.info("已计算分管任务中 {} 个任务的评论天数", delegatedTasks.getContent().size());
        
        // 获取进行中的分管任务数量
        Long inProgressCount = taskService.countInProgressDelegatedTasks(currentUsername);
        
        // 添加到模型
        model.addAttribute("activeMenu", "delegatedTasks");
        model.addAttribute("delegatedTasks", delegatedTasks);
        model.addAttribute("inProgressCount", inProgressCount);
        model.addAttribute("totalCount", delegatedTasks.getTotalElements());
        
        return "delegated-tasks/index";
    }
} 