package com.mylog.service;

import com.mylog.model.config.ConfigOption;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 增强版的选项服务，支持 ratio 和 remark 字段
 */
@Service
public class EnhancedOptionsService {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedOptionsService.class);
    
    @Autowired
    private ConfigOptionService configOptionService;
    
    /**
     * 获取配置选项的详细信息（包含ratio和remark）
     */
    public List<ConfigOption> getDetailedOptionsByCategory(String category) {
        return configOptionService.getConfigOptionsByCategory(category);
    }
    
    /**
     * 获取配置选项的值和比率映射
     */
    public Map<String, Double> getOptionRatioMap(String category) {
        List<ConfigOption> options = configOptionService.getConfigOptionsByCategory(category);
        return options.stream()
                .filter(option -> option.getRatio() != null)
                .collect(Collectors.toMap(
                    ConfigOption::getValue,
                    ConfigOption::getRatio,
                    (existing, replacement) -> replacement
                ));
    }
    
    /**
     * 获取配置选项的值和备注映射
     */
    public Map<String, String> getOptionRemarkMap(String category) {
        List<ConfigOption> options = configOptionService.getConfigOptionsByCategory(category);
        return options.stream()
                .filter(option -> option.getRemark() != null && !option.getRemark().trim().isEmpty())
                .collect(Collectors.toMap(
                    ConfigOption::getValue,
                    ConfigOption::getRemark,
                    (existing, replacement) -> replacement
                ));
    }
    
    /**
     * 获取指定选项的比率
     */
    public Double getOptionRatio(String category, String value) {
        return configOptionService.getConfigOption(category, value)
                .map(ConfigOption::getRatio)
                .orElse(null);
    }
    
    /**
     * 获取指定选项的备注
     */
    public String getOptionRemark(String category, String value) {
        return configOptionService.getConfigOption(category, value)
                .map(ConfigOption::getRemark)
                .orElse(null);
    }
    
    /**
     * 更新选项的比率
     */
    public boolean updateOptionRatio(String category, String value, Double ratio) {
        return configOptionService.getConfigOption(category, value)
                .map(option -> {
                    option.setRatio(ratio);
                    configOptionService.saveConfigOption(option);
                    logger.info("更新配置选项 {}:{} 的比率为 {}", category, value, ratio);
                    return true;
                })
                .orElse(false);
    }
    
    /**
     * 更新选项的备注
     */
    public boolean updateOptionRemark(String category, String value, String remark) {
        return configOptionService.getConfigOption(category, value)
                .map(option -> {
                    option.setRemark(remark);
                    configOptionService.saveConfigOption(option);
                    logger.info("更新配置选项 {}:{} 的备注为 {}", category, value, remark);
                    return true;
                })
                .orElse(false);
    }
    
    /**
     * 同时更新选项的比率和备注
     */
    public boolean updateOptionDetails(String category, String value, Double ratio, String remark) {
        return configOptionService.getConfigOption(category, value)
                .map(option -> {
                    option.setRatio(ratio);
                    option.setRemark(remark);
                    configOptionService.saveConfigOption(option);
                    logger.info("更新配置选项 {}:{} 的比率为 {} 备注为 {}", category, value, ratio, remark);
                    return true;
                })
                .orElse(false);
    }
    
    /**
     * 获取人员相关的详细配置
     */
    public List<ConfigOption> getPersonnelDetails() {
        return getDetailedOptionsByCategory("人员");
    }
    
    /**
     * 获取视觉类型相关的详细配置
     */
    public List<ConfigOption> getVisionTypeDetails() {
        return getDetailedOptionsByCategory("视觉类型");
    }
    
    /**
     * 获取项目类型相关的详细配置
     */
    public List<ConfigOption> getProjectTypeDetails() {
        return getDetailedOptionsByCategory("项目类型");
    }
}
