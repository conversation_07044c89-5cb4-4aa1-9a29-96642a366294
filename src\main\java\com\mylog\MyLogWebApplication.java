package com.mylog;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.slf4j.LoggerFactory;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.Level;

import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.repository.ProjectRepository;
import com.mylog.repository.ProjectTaskRepository;

import java.time.LocalDateTime;

@SpringBootApplication
@EnableScheduling
@EntityScan("com.mylog")
public class MyLogWebApplication {

    public static void main(String[] args) {
        // 彻底禁用所有 Hibernate 日志记录器
        Logger hibernateLogger = (Logger) LoggerFactory.getLogger("org.hibernate");
        if (hibernateLogger != null) {
            hibernateLogger.setLevel(Level.OFF);
        }
        // 作为双重保险，也禁用特定的SQL日志记录器
        Logger hibernateSqlLogger = (Logger) LoggerFactory.getLogger("org.hibernate.SQL");
        if (hibernateSqlLogger != null) {
            hibernateSqlLogger.setLevel(Level.OFF);
        }
        Logger hibernateTypeLogger = (Logger) LoggerFactory.getLogger("org.hibernate.type");
        if (hibernateTypeLogger != null) {
            hibernateTypeLogger.setLevel(Level.OFF);
        }
        
        SpringApplication.run(MyLogWebApplication.class, args);
    }
    
    /**
     * 初始化测试数据
     */
    @Bean
    public static CommandLineRunner initData(ProjectRepository projectRepository,
                                           ProjectTaskRepository taskRepository,
                                           PasswordEncoder passwordEncoder) {
        return args -> {
            // 检查是否已经有数据
            if (projectRepository.count() > 0) {
                return; // 已有数据，不再初始化
            }
            
            // 创建测试项目
            Project project1 = new Project();
            project1.setProjectName("测试项目1");
            project1.setProjectCode("TP001");
            project1.setCustomerName("测试客户1");
            project1.setResponsible("admin");
            project1.setStatus("进行中");
            project1.setCreatedBy("system");
            projectRepository.save(project1);
            
            Project project2 = new Project();
            project2.setProjectName("测试项目2");
            project2.setProjectCode("TP002");
            project2.setCustomerName("测试客户2");
            project2.setResponsible("admin");
            project2.setStatus("进行中");
            project2.setCreatedBy("system");
            projectRepository.save(project2);
            
            // 创建测试任务
            // 项目1的任务
            ProjectTask task1 = new ProjectTask();
            task1.setTaskName("现场责任人");
            task1.setProjectId(project1.getProjectId());
            task1.setResponsible("admin");
            task1.setStatus("进行中");
            task1.setRisk("低");
            task1.setRatio(0.5);
            task1.setProgress(50);
            task1.setCreatedBy("system");
            taskRepository.save(task1);
            
            ProjectTask task2 = new ProjectTask();
            task2.setTaskName("客户现场调试 - 设备安装");
            task2.setProjectId(project1.getProjectId());
            task2.setResponsible("engineer1");
            task2.setStatus("进行中");
            task2.setRisk("中");
            task2.setRatio(0.3);
            task2.setProgress(30);
            task2.setCreatedBy("system");
            taskRepository.save(task2);
            
            ProjectTask task3 = new ProjectTask();
            task3.setTaskName("客户现场调试 - 软件配置");
            task3.setProjectId(project1.getProjectId());
            task3.setResponsible("engineer2");
            task3.setStatus("未开始");
            task3.setRisk("低");
            task3.setRatio(0.0);
            task3.setProgress(0);
            task3.setCreatedBy("system");
            taskRepository.save(task3);
            
            // 项目2的任务
            ProjectTask task4 = new ProjectTask();
            task4.setTaskName("现场责任人");
            task4.setProjectId(project2.getProjectId());
            task4.setResponsible("user1");
            task4.setStatus("进行中");
            task4.setRisk("低");
            task4.setRatio(0.7);
            task4.setProgress(70);
            task4.setCreatedBy("system");
            taskRepository.save(task4);
            
            ProjectTask task5 = new ProjectTask();
            task5.setTaskName("客户现场调试 - 系统测试");
            task5.setProjectId(project2.getProjectId());
            task5.setResponsible("engineer3");
            task5.setStatus("进行中");
            task5.setRisk("高");
            task5.setRatio(0.6);
            task5.setProgress(60);
            task5.setCreatedBy("system");
            taskRepository.save(task5);
        };
    }
}