package com.mylog.service.impl;

import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.ApprovalRecord.ApprovalAction;
import com.mylog.repository.workflow.ApprovalRecordRepository;
import com.mylog.service.ApprovalRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 审批记录服务实现类
 */
@Service
public class ApprovalRecordServiceImpl implements ApprovalRecordService {
    
    private static final Logger logger = LoggerFactory.getLogger(ApprovalRecordServiceImpl.class);
    
    @Autowired
    private ApprovalRecordRepository recordRepository;
    
    @Override
    @Transactional(readOnly = true)
    public List<ApprovalRecord> findAllRecords() {
        return recordRepository.findAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<ApprovalRecord> findAllRecords(Pageable pageable) {
        return recordRepository.findAll(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<ApprovalRecord> findRecordById(Long id) {
        return recordRepository.findById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ApprovalRecord> findRecordsByInstanceId(Long instanceId) {
        return recordRepository.findByInstanceInstanceIdOrderByCreatedDateAsc(instanceId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<ApprovalRecord> findRecordsByInstanceId(Long instanceId, Pageable pageable) {
        return recordRepository.findByInstanceInstanceIdOrderByCreatedDateAsc(instanceId, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ApprovalRecord> findRecordsByStepId(Long stepId) {
        return recordRepository.findByStepStepId(stepId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ApprovalRecord> findRecordsByApprover(String approver) {
        return recordRepository.findByApprover(approver);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ApprovalRecord> findRecordsByAction(ApprovalAction action) {
        return recordRepository.findByAction(action);
    }
    
    @Override
    @Transactional
    public ApprovalRecord saveRecord(ApprovalRecord record) {
        // 设置创建时间
        if (record.getCreatedDate() == null) {
            record.setCreatedDateTime(LocalDateTime.now());
        }
        
        return recordRepository.save(record);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<ApprovalRecord> findLatestRecord(Long instanceId) {
        List<ApprovalRecord> records = recordRepository.findLatestRecord(instanceId, PageRequest.of(0, 1));
        return records.isEmpty() ? Optional.empty() : Optional.of(records.get(0));
    }
    
    @Override
    @Transactional
    public void deleteRecord(Long recordId) {
        logger.info("删除审批记录: {}", recordId);
        recordRepository.deleteById(recordId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<ApprovalRecord> findUserApprovalHistory(String username, Pageable pageable) {
        return recordRepository.findUserApprovalHistory(username, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ApprovalRecord> findRecordsByAttachmentPath(String attachmentPath) {
        // 查找所有包含指定附件路径的审批记录
        // 由于attachments字段用分号分隔多个附件路径，使用LIKE查询
        return recordRepository.findByAttachmentsContaining(attachmentPath);
    }
}
