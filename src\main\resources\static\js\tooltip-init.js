/**
 * 全局工具提示初始化脚本
 * 确保在所有页面上都能正确初始化Bootstrap的Tooltip组件
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已加载Bootstrap
    if (typeof bootstrap !== 'undefined') {
        // 初始化所有带有data-bs-toggle="tooltip"属性的元素
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        console.log('全局工具提示已初始化');
    } else {
        console.warn('Bootstrap未加载，无法初始化工具提示');
    }
});
