package com.mylog.model;

import jakarta.persistence.*;

/**
 * 奖罚记录实体类
 * 用于记录人员的奖励和惩罚信息
 */
@Entity
@Table(name = "RewardPenaltyRecord")
public class RewardPenaltyRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;  // 姓名

    @Column(name = "type", nullable = false)
    private String type;  // 奖罚类型

    @Column(name = "reason", nullable = false)
    private String reason;  // 奖罚事由

    @Column(name = "remarks")
    private String remarks;  // 备注

    @Column(name = "create_time", nullable = false)
    private String createTime;  // 创建时间，格式：2025-05-23 04:05:03

    @Column(name = "occur_time", nullable = false)
    private String occurTime;  // 发生时间，格式：2025-05-23 04:05:03

    @Column(name = "points", nullable = false)
    private Integer points;  // 积分，正数表示奖励，负数表示惩罚（变化积分）

    @Column(name = "total_points", nullable = false)
    private Integer totalPoints;  // 存量积分（累计积分）

    /**
     * 默认构造函数
     */
    public RewardPenaltyRecord() {
    }

    /**
     * 全参数构造函数
     */
    public RewardPenaltyRecord(String name, String type, String reason, String remarks, 
                               String createTime, String occurTime, Integer points, Integer totalPoints) {
        this.name = name;
        this.type = type;
        this.reason = reason;
        this.remarks = remarks;
        this.createTime = createTime;
        this.occurTime = occurTime;
        this.points = points;
        this.totalPoints = totalPoints;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(String occurTime) {
        this.occurTime = occurTime;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getTotalPoints() {
        return totalPoints;
    }

    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }

    @Override
    public String toString() {
        return "RewardPenaltyRecord{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", reason='" + reason + '\'' +
                ", remarks='" + remarks + '\'' +
                ", createTime='" + createTime + '\'' +
                ", occurTime='" + occurTime + '\'' +
                ", points=" + points +
                ", totalPoints=" + totalPoints +
                '}';
    }
}
