-- V1.0.17__Create_Event_Check_In_Tables.sql
-- 创建事件签到相关表

-- 首先更新 event_reminders 表，添加签到相关字段
ALTER TABLE event_reminders ADD COLUMN requires_check_in BOOLEAN DEFAULT 0;
ALTER TABLE event_reminders ADD COLUMN check_in_window_minutes INTEGER DEFAULT 30;

-- 创建事件签到表
CREATE TABLE IF NOT EXISTS event_check_ins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_reminder_id INTEGER NOT NULL,         -- 关联的事件提醒ID
    user_id INTEGER NOT NULL,                   -- 执行签到的用户ID (改为字符串类型以兼容当前系统)
    check_in_time DATETIME NOT NULL,            -- 实际签到时间
    latitude REAL,                              -- 可选：签到时的纬度
    longitude REAL,                             -- 可选：签到时的经度
    notes VARCHAR(500),                         -- 可选：签到备注
    created_time DATETIME NOT NULL,             -- 记录创建时间
    FOREIGN KEY (event_reminder_id) REFERENCES event_reminders(id) ON DELETE CASCADE
);

-- 创建事件签到表索引
CREATE INDEX IF NOT EXISTS idx_check_ins_reminder_id ON event_check_ins(event_reminder_id);
CREATE INDEX IF NOT EXISTS idx_check_ins_user_id ON event_check_ins(user_id);
CREATE INDEX IF NOT EXISTS idx_check_ins_time ON event_check_ins(check_in_time);
CREATE INDEX IF NOT EXISTS idx_check_ins_created_time ON event_check_ins(created_time);

-- 为提醒表添加签到相关索引
CREATE INDEX IF NOT EXISTS idx_reminders_requires_check_in ON event_reminders(requires_check_in);
CREATE INDEX IF NOT EXISTS idx_reminders_check_in_window ON event_reminders(check_in_window_minutes);

-- 插入一些测试数据（如果提醒表有数据的话）
UPDATE event_reminders 
SET requires_check_in = 1, check_in_window_minutes = 30 
WHERE id IN (1, 2, 3);
