package com.mylog.model.workflow;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程实例实体类
 * 具体执行中的流程
 */
@Entity
@Table(name = "workflow_instances")
@Data
public class WorkflowInstance {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowInstance.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 流程状态枚举
     */
    public enum WorkflowStatus {
        DRAFT,       // 草稿
        PROCESSING,  // 处理中
        APPROVED,    // 已批准
        REJECTED,    // 已拒绝
        CANCELED,    // 已取消
        TERMINATED   // 已终止
    }
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long instanceId;
    
    /**
     * 所属流程模板
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", nullable = false)
    private WorkflowTemplate template;
    
    /**
     * 流程步骤数量
     * 注意：数据库存储的步骤数不包含提交步骤，但此getter方法返回的是包含提交步骤的总数
     */
    @Column(name = "step_count", nullable = false)
    private Integer stepCount = 0;
    
    /**
     * 流程实例标题
     */
    @Column(nullable = false, length = 200)
    private String title;
    
    /**
     * 流程状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private WorkflowStatus status = WorkflowStatus.DRAFT;
    
    /**
     * 当前步骤ID
     */
    @Column
    private Long currentStepId;
    
    /**
     * 待审批人
     * 存储指定的下一步审批人用户名
     */
    @Column(length = 50)
    private String currentApprover;
    
    /**
     * 关联业务类型
     * 如：Project, Task等
     */
    @Column(length = 50)
    private String businessType;
    
    /**
     * 关联业务ID
     */
    @Column
    private Long businessId;
    
    /**
     * 发起人
     */
    @Column(nullable = false, length = 50)
    private String initiator;
    
    /**
     * 流程说明
     */
    @Column(columnDefinition = "TEXT")
    private String description;
    
    /**
     * 创建时间
     */
    @Column(nullable = false)
    private String createdDate;
    
    /**
     * 提交时间
     */
    @Column
    private String submittedDate;
    
    /**
     * 完成时间
     */
    @Column
    private String completedDate;
    
    /**
     * 开始时间
     */
    @Column
    private String startTime;
    
    /**
     * 结束时间
     */
    @Column
    private String endTime;
    
    /**
     * 出发地
     */
    @Column(length = 100)
    private String startLocation;
    
    /**
     * 目的地
     */
    @Column(length = 100)
    private String endLocation;
    
    /**
     * 实施人员
     */
    @Column(length = 255)
    private String staff;
    
    /**
     * 备注1
     */
    @Column(length = 255)
    private String remark1;
    
    /**
     * 备注2
     */
    @Column(length = 255)
    private String remark2;
    
    /**
     * 备注3
     */
    @Column(length = 255)
    private String remark3;
    
    /**
     * 备注4
     */
    @Column(length = 255)
    private String remark4;
    
    /**
     * 审批记录列表
     */
    @OneToMany(mappedBy = "instance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("createdDate ASC")
    private List<ApprovalRecord> approvalRecords = new ArrayList<>();
    
    /**
     * 设置创建时间
     */
    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.createdDate = formatDateTime(dateTime);
    }
    
    /**
     * 获取创建时间
     */
    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.createdDate);
    }
    
    /**
     * 设置提交时间
     */
    public void setSubmittedDateTime(LocalDateTime dateTime) {
        this.submittedDate = formatDateTime(dateTime);
    }
    
    /**
     * 获取提交时间
     */
    public LocalDateTime getSubmittedDateTime() {
        return parseDateTime(this.submittedDate);
    }
    
    /**
     * 设置完成时间
     */
    public void setCompletedDateTime(LocalDateTime dateTime) {
        this.completedDate = formatDateTime(dateTime);
    }
    
    /**
     * 获取完成时间
     */
    public LocalDateTime getCompletedDateTime() {
        return parseDateTime(this.completedDate);
    }
    
    /**
     * 设置开始时间
     */
    public void setStartDateTime(LocalDateTime dateTime) {
        this.startTime = formatDateTime(dateTime);
    }
    
    /**
     * 获取开始时间
     */
    public LocalDateTime getStartDateTime() {
        return parseDateTime(this.startTime);
    }
    
    /**
     * 设置结束时间
     */
    public void setEndDateTime(LocalDateTime dateTime) {
        this.endTime = formatDateTime(dateTime);
    }
    
    /**
     * 获取结束时间
     */
    public LocalDateTime getEndDateTime() {
        return parseDateTime(this.endTime);
    }
    
    /**
     * 获取步骤总数（包含提交步骤）
     * 注意：数据库中存储的是不包含提交步骤的数量，但此方法返回的是包含提交步骤的总数
     */
    public Integer getStepCount() {
        return this.stepCount != null ? this.stepCount + 1 : 1;
    }
    
    /**
     * 设置步骤数（不包含提交步骤）
     * 注意：此处设置的是不包含提交步骤的数量，与数据库中存储的值一致
     */
    public void setStepCount(Integer stepCount) {
        this.stepCount = stepCount;
    }
    
    /**
     * 获取数据库中存储的原始步骤数（不包含提交步骤）
     * 仅供内部使用，正常获取步骤数应使用getStepCount()方法
     */
    public Integer getRawStepCount() {
        return this.stepCount;
    }
    
    /**
     * 解析日期时间字符串
     * 仅使用标准格式 "yyyy-MM-dd HH:mm:ss" 进行解析，确保时间格式统一
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 检查是否是ISO格式（包含T分隔符）
            if (dateStr.contains("T")) {
                // 将ISO格式转换为标准格式
                LocalDateTime dateTime = LocalDateTime.parse(dateStr);
                return dateTime; // 返回转换后的日期时间
            }
            
            // 尝试以标准格式解析
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            // 记录错误
            logger.error("日期时间字符串格式不正确，需要使用 'yyyy-MM-dd HH:mm:ss' 格式: {}", dateStr);
            
            try {
                // 尝试将HTML表单提交的日期时间字符串标准化
                if (dateStr.length() <= 10) { // 只有日期，没有时间部分
                    // 添加默认时间
                    return LocalDateTime.parse(dateStr + " 00:00:00", DATE_FORMATTER);
                }
            } catch (DateTimeParseException ex) {
                // 转换失败
            }
            
            return null;
        }
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }
}
