package com.mylog.controller.workflow;

import com.mylog.controller.BaseController;

import com.mylog.model.user.User;
import com.mylog.model.workflow.WorkflowStep;
import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.WorkflowStepService;
import com.mylog.service.WorkflowTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 流程步骤控制器
 */
@Controller
@RequestMapping("/workflow/steps")
@PreAuthorize("hasRole('ADMIN')")
public class WorkflowStepController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowStepController.class);

    @Autowired
    private WorkflowStepService stepService;

    @Autowired
    private WorkflowTemplateService templateService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityLogService activityLogService;

    /**
     * 显示流程步骤列表
     */
    @GetMapping("/template/{templateId}")
    public String listSteps(@PathVariable("templateId") Long templateId, Model model, RedirectAttributes redirectAttributes) {
        try {
            // 获取模板信息
            Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(templateId);
            if (!templateOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程模板不存在");
                return "redirect:/workflow/templates";
            }

            // 获取步骤列表
            List<WorkflowStep> steps = stepService.findStepsByTemplateId(templateId);

            // 记录步骤数量，用于调试
            logger.info("模板 ID: {}, 名称: {}, 找到步骤数量: {}",
                templateId, templateOpt.get().getTemplateName(), steps.size());

            // 如果步骤列表为空或只有一条记录，尝试直接使用EntityManager查询
            if (steps.size() <= 1) {
                logger.info("步骤列表为空或只有一条记录，尝试直接使用EntityManager查询");
                try {
                    // 使用原生SQL查询
                    List<WorkflowStep> directSteps = stepService.findStepsByTemplateIdDirect(templateId);
                    if (directSteps != null && directSteps.size() > steps.size()) {
                        logger.info("使用EntityManager查询找到更多步骤: {}", directSteps.size());
                        steps = directSteps;
                    }
                } catch (Exception e) {
                    logger.error("使用EntityManager查询步骤时出错: {}", e.getMessage(), e);
                    // 不要抛出异常，继续使用已获取的步骤列表
                }
            }

            // 记录每个步骤的详细信息
            if (!steps.isEmpty()) {
                for (int i = 0; i < steps.size(); i++) {
                    WorkflowStep step = steps.get(i);
                    logger.info("步骤 #{}: ID={}, 名称={}, 顺序={}, 审批人类型={}",
                        i+1, step.getStepId(), step.getStepName(), step.getStepOrder(), step.getApproverType());
                }
            }

            // 如果步骤列表为空，尝试从模板对象中获取
            if (steps.isEmpty() && templateOpt.get().getSteps() != null && !templateOpt.get().getSteps().isEmpty()) {
                steps = new ArrayList<>(templateOpt.get().getSteps());
                logger.info("从模板对象中获取步骤列表，数量: {}", steps.size());

                // 记录从模板对象中获取的步骤详细信息
                for (int i = 0; i < steps.size(); i++) {
                    WorkflowStep step = steps.get(i);
                    logger.info("模板中的步骤 #{}: ID={}, 名称={}, 顺序={}, 审批人类型={}",
                        i+1, step.getStepId(), step.getStepName(), step.getStepOrder(), step.getApproverType());
                }
            }

            // 添加调试信息
            logger.info("添加到模型前的步骤列表大小: {}", steps.size());
            for (WorkflowStep step : steps) {
                logger.info("步骤ID: {}, 名称: {}, 顺序: {}", step.getStepId(), step.getStepName(), step.getStepOrder());
            }

            // 确保步骤列表不为null
            if (steps == null) {
                steps = new ArrayList<>();
            }

            // 添加到模型
            model.addAttribute("template", templateOpt.get());
            model.addAttribute("steps", steps);
            model.addAttribute("activeMenu", "workflow");

            // 添加调试信息到模型
            model.addAttribute("stepsCount", steps.size());

            return "workflow/steps/index";
        } catch (Exception e) {
            logger.error("加载流程步骤列表时出错: {}", e.getMessage(), e);
            // 将错误信息添加到重定向属性中，而不是直接抛出异常
            redirectAttributes.addFlashAttribute("error", "加载流程步骤列表失败: " + e.getMessage());
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 显示创建流程步骤表单
     */
    @GetMapping("/create")
    public String showCreateForm(@RequestParam("templateId") Long templateId, Model model, RedirectAttributes redirectAttributes) {
        try {
            // 获取模板信息
            Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(templateId);
            if (!templateOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程模板不存在");
                return "redirect:/workflow/templates";
            }

            WorkflowStep step = new WorkflowStep();
            step.setTemplate(templateOpt.get());
            step.setStepOrder(stepService.getNextStepOrder(templateId));

            model.addAttribute("step", step);
            model.addAttribute("template", templateOpt.get());
            model.addAttribute("approverTypes", WorkflowStep.ApproverType.values());
            model.addAttribute("activeMenu", "workflow");

            return "workflow/steps/form";
        } catch (Exception e) {
            logger.error("显示创建流程步骤表单时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "显示创建流程步骤表单失败: " + e.getMessage());
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 显示编辑流程步骤表单
     */
    @GetMapping("/{id}/edit")
    public String showEditForm(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        try {
            Optional<WorkflowStep> stepOpt = stepService.findStepById(id);
            if (stepOpt.isPresent()) {
                WorkflowStep step = stepOpt.get();
                model.addAttribute("step", step);
                model.addAttribute("template", step.getTemplate());
                model.addAttribute("approverTypes", WorkflowStep.ApproverType.values());
                model.addAttribute("activeMenu", "workflow");
                return "workflow/steps/form";
            } else {
                redirectAttributes.addFlashAttribute("error", "流程步骤不存在");
                return "redirect:/workflow/templates";
            }
        } catch (Exception e) {
            logger.error("显示编辑流程步骤表单时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "显示编辑流程步骤表单失败: " + e.getMessage());
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 保存流程步骤
     */
    @PostMapping("/save")
    public String saveStep(WorkflowStep step, @RequestParam("templateId") Long templateId, RedirectAttributes redirectAttributes) {
        try {
            // 获取模板信息
            Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(templateId);
            if (!templateOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程模板不存在");
                return "redirect:/workflow/templates";
            }

            // 设置模板
            step.setTemplate(templateOpt.get());

            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 设置创建人或最后修改人
            if (step.getStepId() == null) {
                step.setCreatedBy(currentUsername);
            } else {
                step.setLastModifiedBy(currentUsername);
            }

            // 保存步骤
            WorkflowStep savedStep = stepService.saveStep(step);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String action = step.getStepId() == null ? "创建" : "更新";
                String ipAddress = getClientIpAddress();
                if (step.getStepId() == null) {
                    activityLogService.logCreate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        action + "流程步骤: " + step.getStepName(),
                        ipAddress,
                        "WorkflowStep",
                        savedStep.getStepId(),
                        getAccessType()
                    );
                } else {
                    activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        action + "流程步骤: " + step.getStepName(),
                        ipAddress,
                        "WorkflowStep",
                        savedStep.getStepId(),
                        getAccessType()
                    );
                }
            }

            redirectAttributes.addFlashAttribute("message", "流程步骤保存成功");
            return "redirect:/workflow/steps/template/" + templateId;
        } catch (Exception e) {
            logger.error("保存流程步骤时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "保存流程步骤失败: " + e.getMessage());
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 删除流程步骤
     */
    @PostMapping("/delete")
    public String deleteStep(@RequestParam("stepId") Long id, RedirectAttributes redirectAttributes) {
        try {
            // 获取要删除的步骤信息（用于日志记录和重定向）
            Optional<WorkflowStep> stepToDelete = stepService.findStepById(id);
            if (!stepToDelete.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程步骤不存在");
                return "redirect:/workflow/templates";
            }

            Long templateId = stepToDelete.get().getTemplate().getTemplateId();

            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);

            // 删除步骤
            stepService.deleteStep(id);

            // 记录活动日志
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logDelete(
                    currentUser.get().getUserId(),
                    currentUsername,
                    "删除流程步骤: " + stepToDelete.get().getStepName(),
                    ipAddress,
                    "WorkflowStep",
                    id,
                    getAccessType()
                );
            }

            redirectAttributes.addFlashAttribute("message", "流程步骤删除成功");
            return "redirect:/workflow/steps/template/" + templateId;
        } catch (Exception e) {
            logger.error("删除流程步骤时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除流程步骤失败: " + e.getMessage());
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 调整步骤顺序
     */
    @PostMapping("/reorder")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> reorderSteps(
            @RequestParam("templateId") Long templateId,
            @RequestParam("stepId") Long stepId,
            @RequestParam("newOrder") Integer newOrder) {

        Map<String, Object> response = new HashMap<>();

        try {
            stepService.reorderSteps(templateId, stepId, newOrder);

            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);

            // 记录活动日志
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                    currentUser.get().getUserId(),
                    currentUsername,
                    "调整流程步骤顺序",
                    ipAddress,
                    "WorkflowStep",
                    stepId,
                    getAccessType()
                );
            }

            response.put("success", true);
            response.put("message", "步骤顺序调整成功");
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
        } catch (Exception e) {
            logger.error("调整步骤顺序时出错: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "调整步骤顺序失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.OK)  // 使用200状态码以确保客户端能接收到错误信息
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
        }
    }
}
