package com.mylog.service.impl;

import com.mylog.dto.CalendarDTO;
import com.mylog.model.Calendar;
import com.mylog.repository.CalendarRepository;
import com.mylog.service.CalendarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 日历服务实现类
 */
@Service
@Transactional
public class CalendarServiceImpl implements CalendarService {
    
    @Autowired
    private CalendarRepository calendarRepository;
      @Override
    @Transactional(rollbackFor = Exception.class) // 确保所有异常都会触发事务回滚
    public CalendarDTO createCalendar(CalendarDTO calendarDTO) {
        System.out.println("=== DEBUG: CalendarServiceImpl.createCalendar() 开始执行 ===");
        System.out.println("DEBUG: 接收到的日历数据: " + calendarDTO);
        
        // 检查userId是否为null
        if (calendarDTO.getUserId() == null) {
            System.out.println("ERROR: 日历DTO的userId为null，无法创建日历");
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        // 检查日历名称是否已存在
        boolean nameExists = isCalendarNameExists(calendarDTO.getUserId(), calendarDTO.getName());
        System.out.println("DEBUG: 日历名称是否存在: " + nameExists);
        if (nameExists) {
            System.out.println("DEBUG: 日历名称已存在，抛出异常");
            throw new IllegalArgumentException("日历名称已存在");
        }
        
        Calendar calendar = convertToEntity(calendarDTO);
        System.out.println("DEBUG: 转换后的实体: " + calendar);
        
        // 如果是用户的第一个日历，设置为默认日历
        long calendarCount = calendarRepository.countByUserId(calendarDTO.getUserId());
        System.out.println("DEBUG: 用户ID[" + calendarDTO.getUserId() + "]的日历数量: " + calendarCount);
        if (calendarCount == 0) {
            calendar.setIsDefault(true);
            System.out.println("DEBUG: 设置为默认日历");
        }
          
        try {
            System.out.println("DEBUG: 准备保存日历实体到数据库");
            // 显式检查userId是否为null
            if (calendar.getUserId() == null) {
                System.out.println("ERROR: 日历实体的userId为null，无法保存");
                throw new IllegalArgumentException("用户ID不能为空");
            }
            
            // 显式设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            calendar.setCreatedTime(now);
            calendar.setUpdatedTime(now);
            
            Calendar savedCalendar = calendarRepository.save(calendar);
            System.out.println("DEBUG: 保存成功，返回的实体ID: " + (savedCalendar != null ? savedCalendar.getId() : "null"));
            
            // 验证日历是否真的被保存
            if (savedCalendar.getId() == null) {
                System.out.println("ERROR: 保存后的日历ID为null");
                throw new RuntimeException("日历保存失败，ID为null");
            }
            
            Long calendarId = savedCalendar.getId();
            Calendar retrievedCalendar = calendarRepository.findById(calendarId).orElse(null);
            System.out.println("DEBUG: 从数据库验证保存结果: " + (retrievedCalendar != null ? "成功" : "失败"));
            
            if (retrievedCalendar == null) {
                System.out.println("ERROR: 日历似乎已保存但在数据库中找不到ID: " + calendarId);
                throw new RuntimeException("日历保存失败，无法在数据库中找到");
            }
            
            CalendarDTO result = new CalendarDTO(savedCalendar);
            System.out.println("DEBUG: 转换回DTO完成，即将返回: " + result);
            return result;
        } catch (Exception e) {
            System.out.println("ERROR: 保存日历时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    @Override
    public CalendarDTO updateCalendar(Long id, CalendarDTO calendarDTO) {
        Calendar existingCalendar = calendarRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("日历不存在"));
        
        // 检查权限
        if (!existingCalendar.getUserId().equals(calendarDTO.getUserId())) {
            throw new IllegalArgumentException("无权限修改此日历");
        }
        
        // 检查名称是否重复（排除当前日历）
        Optional<Calendar> duplicateCalendar = calendarRepository.findByUserIdAndName(
            calendarDTO.getUserId(), calendarDTO.getName());
        if (duplicateCalendar.isPresent() && !duplicateCalendar.get().getId().equals(id)) {
            throw new IllegalArgumentException("日历名称已存在");
        }
          // 更新字段
        existingCalendar.setName(calendarDTO.getName());
        existingCalendar.setDescription(calendarDTO.getDescription());
        existingCalendar.setColor(calendarDTO.getColor());
        existingCalendar.setIsShared(calendarDTO.getIsShared());
        
        // 处理默认日历设置
        if (calendarDTO.getIsDefault() != null && calendarDTO.getIsDefault()) {
            // 如果要设置为默认日历，先取消当前用户的其他默认日历
            calendarRepository.findByUserIdAndIsDefaultTrue(calendarDTO.getUserId())
                .ifPresent(defaultCalendar -> {
                    if (!defaultCalendar.getId().equals(id)) {
                        defaultCalendar.setIsDefault(false);
                        calendarRepository.save(defaultCalendar);
                    }
                });
        }
        existingCalendar.setIsDefault(calendarDTO.getIsDefault());
        
        Calendar savedCalendar = calendarRepository.save(existingCalendar);
        return new CalendarDTO(savedCalendar);
    }
    
    @Override
    public void deleteCalendar(Long id) {
        Calendar calendar = calendarRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("日历不存在"));
        
        // 不允许删除默认日历
        if (calendar.getIsDefault()) {
            throw new IllegalArgumentException("不能删除默认日历");
        }
        
        calendarRepository.delete(calendar);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<CalendarDTO> getCalendarById(Long id) {
        return calendarRepository.findById(id)
            .map(CalendarDTO::new);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarDTO> getCalendarsByUserId(Long userId) {
        return calendarRepository.findByUserIdOrderByCreatedTimeDesc(userId)
            .stream()
            .map(CalendarDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<CalendarDTO> getDefaultCalendar(Long userId) {
        return calendarRepository.findByUserIdAndIsDefaultTrue(userId)
            .map(CalendarDTO::new);
    }
    
    @Override
    public CalendarDTO setDefaultCalendar(Long userId, Long calendarId) {
        // 检查日历是否存在且属于用户
        Calendar calendar = calendarRepository.findById(calendarId)
            .orElseThrow(() -> new IllegalArgumentException("日历不存在"));
        
        if (!calendar.getUserId().equals(userId)) {
            throw new IllegalArgumentException("无权限设置此日历为默认");
        }
        
        // 取消当前默认日历
        calendarRepository.findByUserIdAndIsDefaultTrue(userId)
            .ifPresent(defaultCalendar -> {
                defaultCalendar.setIsDefault(false);
                calendarRepository.save(defaultCalendar);
            });
        
        // 设置新的默认日历
        calendar.setIsDefault(true);
        Calendar savedCalendar = calendarRepository.save(calendar);
        return new CalendarDTO(savedCalendar);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarDTO> getSharedCalendars() {
        return calendarRepository.findByIsSharedTrueOrderByCreatedTimeDesc()
            .stream()
            .map(CalendarDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CalendarDTO> getAccessibleCalendars(Long userId) {
        return calendarRepository.findByUserIdOrShared(userId)
            .stream()
            .map(CalendarDTO::new)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isCalendarNameExists(Long userId, String name) {
        return calendarRepository.existsByUserIdAndName(userId, name);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean hasCalendarAccess(Long userId, Long calendarId) {
        return calendarRepository.findById(calendarId)
            .map(calendar -> calendar.getUserId().equals(userId) || calendar.getIsShared())
            .orElse(false);
    }
    
    @Override
    public CalendarDTO createDefaultCalendar(Long userId, String userName) {
        String defaultName = userName + "的日历";
        
        // 如果名称已存在，添加数字后缀
        String finalName = defaultName;
        int counter = 1;
        while (isCalendarNameExists(userId, finalName)) {
            finalName = defaultName + counter;
            counter++;
        }
        
        CalendarDTO calendarDTO = new CalendarDTO();
        calendarDTO.setName(finalName);
        calendarDTO.setDescription("默认日历");
        calendarDTO.setUserId(userId);
        calendarDTO.setIsDefault(true);
        
        return createCalendar(calendarDTO);
    }
      /**
     * 将DTO转换为实体
     */
    private Calendar convertToEntity(CalendarDTO dto) {
        System.out.println("DEBUG: convertToEntity - 开始转换DTO到实体");
        Calendar calendar = new Calendar();
        calendar.setName(dto.getName());
        calendar.setDescription(dto.getDescription());
        calendar.setColor(dto.getColor());
        calendar.setUserId(dto.getUserId());
        calendar.setIsDefault(dto.getIsDefault());
        calendar.setIsShared(dto.getIsShared());
        
        // 确保时间字段被设置
        if (calendar.getCreatedTime() == null) {
            calendar.setCreatedTime(LocalDateTime.now());
        }
        calendar.setUpdatedTime(LocalDateTime.now());
        
        System.out.println("DEBUG: convertToEntity - 转换完成, 结果: " + calendar);
        return calendar;
    }
}
