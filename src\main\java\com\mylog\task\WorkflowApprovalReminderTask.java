package com.mylog.task;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.Message;
import com.mylog.model.user.User;
import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import com.mylog.service.ApprovalRecordService;
import com.mylog.service.MessageService;
import com.mylog.service.UserService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.util.WeixinMessageUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 流程审批超时提醒定时任务
 * 检查处理中状态的流程实例，若审批时间超过8小时，则发送提醒通知
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkflowApprovalReminderTask {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowApprovalReminderTask.class);
    
    // 审批超时时间设置为8小时（单位：小时）
    private static final long APPROVAL_TIMEOUT_HOURS = 8;

    private final WorkflowInstanceService workflowInstanceService;
    private final ApprovalRecordService approvalRecordService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserService userService;

    @Autowired
    private WeixinMessageUtil weixinMessageUtil;
      /**
     * 构造函数执行后的初始化方法
     */    @jakarta.annotation.PostConstruct
    public void init() {
        logger.info("流程审批超时提醒定时任务已初始化");
    }   
    
    /**
     * 定时检查处理中的流程实例，对超时未审批的实例发送提醒
     * 每2小时执行一次，仅在早上8点到晚上10点之间执行
     */
    @Scheduled(cron = "0 30 8 * * ?")      // 8:30执行一次
@Scheduled(cron = "0 0 10-22/2 * * ?") // 10:00、12:00…22:00执行
    @Transactional(readOnly = true)
    public void checkWorkflowApprovalTimeout() {
        logger.info("开始执行流程审批超时检查任务，当前时间: {}", LocalDateTime.now());
        
        try {
            // 查找所有状态为"处理中"的流程实例
            List<WorkflowInstance> processingInstances = workflowInstanceService.findInstancesByStatus(WorkflowStatus.PROCESSING);
            
            if (processingInstances.isEmpty()) {
                logger.info("当前没有处理中的流程实例，跳过本次检查");
                return;
            }
            
            logger.info("找到 {} 个处理中的流程实例", processingInstances.size());
            int reminderCount = 0;
            
            // 遍历所有处理中的流程实例
            for (WorkflowInstance instance : processingInstances) {
                try {
                    // 获取当前流程实例的最新审批记录
                    Optional<ApprovalRecord> latestRecordOpt = approvalRecordService.findLatestRecord(instance.getInstanceId());
                    
                    if (!latestRecordOpt.isPresent()) {
                        logger.warn("流程实例 [{}] 没有审批记录，跳过", instance.getInstanceId());
                        continue;
                    }
                    
                    ApprovalRecord latestRecord = latestRecordOpt.get();
                    LocalDateTime recordCreatedTime = latestRecord.getCreatedDateTime();
                    
                    if (recordCreatedTime == null) {
                        logger.warn("流程实例 [{}] 的最新审批记录 [{}] 没有创建时间，跳过", 
                                instance.getInstanceId(), latestRecord.getRecordId());
                        continue;
                    }
                    
                    // 计算审批记录创建时间到现在的时间差（小时）
                    LocalDateTime now = LocalDateTime.now();
                    Duration duration = Duration.between(recordCreatedTime, now);
                    long elapsedHours = duration.toHours();
                    
                    logger.debug("流程实例 [{}] 的最新审批记录创建于 {}, 已等待 {} 小时", 
                            instance.getInstanceId(), recordCreatedTime, elapsedHours);
                    
                    // 如果等待时间超过设定的超时时间，发送提醒通知
                    if (elapsedHours >= APPROVAL_TIMEOUT_HOURS) {
                        // 获取当前审批人
                        String currentApprover = instance.getCurrentApprover();
                        
                        if (currentApprover == null || currentApprover.isEmpty()) {
                            logger.warn("流程实例 [{}] 当前审批人为空，无法发送提醒", instance.getInstanceId());
                            continue;
                        }
                        
                        // 发送提醒通知
                        sendApprovalReminderNotification(instance, elapsedHours);
                        reminderCount++;
                    }
                } catch (Exception e) {                    logger.error("处理流程实例 [{}] 时发生错误: {}", instance.getInstanceId(), e.getMessage(), e);
                }
            }
            
            logger.info("流程审批超时检查任务完成，共发送 {} 条提醒通知", reminderCount);
            
        } catch (Exception e) {
            logger.error("执行流程审批超时检查任务时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送流程审批超时提醒通知
     *
     * @param instance    流程实例
     * @param elapsedHours 已等待时间（小时）
     */
    private void sendApprovalReminderNotification(WorkflowInstance instance, long elapsedHours) {
        try {
            String approver = instance.getCurrentApprover();
            logger.info("发送审批超时提醒给用户: {}, 流程实例: {}, 已等待: {} 小时", 
                    approver, instance.getInstanceId(), elapsedHours);
            
            // 查找审批人的微信ID
            String weixinID = "";
            Optional<User> approverUser = userService.findUserByUsername(approver);
            if (approverUser.isPresent() && approverUser.get().getWeixinID() != null
                    && !approverUser.get().getWeixinID().isEmpty()) {
                weixinID = approverUser.get().getWeixinID();
                logger.debug("找到用户 {} 的微信ID: {}", approver, weixinID);
            } else {
                logger.warn("未找到用户 {} 的微信ID，使用用户名代替", approver);
                weixinID = approver;
            }
            
            // 构建微信消息内容
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("<@").append(weixinID).append("> ，您有一个待审批的流程已超过 ")
                         .append(elapsedHours).append(" 小时未处理\n\n");
            
            contentBuilder.append("**流程标题**: ").append(instance.getTitle()).append("\n");
            contentBuilder.append("**发起人**: ").append(instance.getInitiator()).append("  ");
              // 添加提交时间，如果存在的话
            LocalDateTime submittedTime = instance.getSubmittedDateTime();
            if (submittedTime != null) {
                String formattedSubmittedTime = submittedTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                contentBuilder.append("**提交时间**: ").append(formattedSubmittedTime).append("\n\n");
            } else {
                contentBuilder.append("\n");
            }
            
            
            // 发送企业微信消息
            List<String> mentionList = new ArrayList<>();
            mentionList.add(weixinID);
            weixinMessageUtil.sendWeixinMessage("流程审批超时提醒", contentBuilder.toString(), mentionList);
            
            // 同时发送系统内部消息
            Message systemMessage = new Message();
            systemMessage.setReceiver(approver);
            systemMessage.setMessageTitle("流程审批超时提醒");
            systemMessage.setMessageContent("您有一个流程 [" + instance.getTitle() + "] 已超过 " 
                    + elapsedHours + " 小时未审批，请及时处理。");
            systemMessage.setRelatedType("WorkflowInstance");
            systemMessage.setRelatedId(instance.getInstanceId());
            systemMessage.setRead(false);
            systemMessage.setCreatedDate(LocalDateTime.now());
            messageService.saveMessage(systemMessage);
            
            logger.info("已发送流程审批超时提醒给用户: {}", approver);
            
        } catch (Exception e) {
            logger.error("发送流程审批超时提醒失败: {}", e.getMessage(), e);
        }
    }
}
