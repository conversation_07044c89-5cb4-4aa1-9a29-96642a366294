-- 为submits2表添加流程实例ID字段
-- filepath: c:\mylog-web\src\main\resources\db\migration\V1.0.26__Add_Workflow_Instance_Id_To_Submits2.sql

-- 添加流程实例ID字段到submits2表
ALTER TABLE Submits2 ADD COLUMN workflow_instance_id INTEGER NULL;

-- 添加外键约束到workflow_instances表
-- 注意：SQLite不支持添加外键约束到已存在的表，因此这里只添加索引
CREATE INDEX IF NOT EXISTS idx_submits2_workflow_instance_id ON Submits2(workflow_instance_id);

-- 添加注释说明（SQLite不支持列注释，这里作为文档）
-- workflow_instance_id: 关联的工作流程实例ID，用于跟踪提交记录与审批流程的关系
