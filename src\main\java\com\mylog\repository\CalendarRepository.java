package com.mylog.repository;

import com.mylog.model.Calendar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

/**
 * 日历Repository接口
 */
@Repository
public interface CalendarRepository extends JpaRepository<Calendar, Long> {
    
    /**
     * 根据用户ID查找日历列表
     */
    List<Calendar> findByUserIdOrderByCreatedTimeDesc(Long userId);
    
    /**
     * 根据用户ID查找默认日历
     */
    Optional<Calendar> findByUserIdAndIsDefaultTrue(Long userId);
    
    /**
     * 根据用户ID和日历名称查找日历
     */
    Optional<Calendar> findByUserIdAndName(Long userId, String name);
    
    /**
     * 查找共享日历
     */
    List<Calendar> findByIsSharedTrueOrderByCreatedTimeDesc();
    
    /**
     * 根据用户ID和共享状态查找日历
     */
    @Query("SELECT c FROM Calendar c WHERE c.userId = :userId OR c.isShared = true ORDER BY c.createdTime DESC")
    List<Calendar> findByUserIdOrShared(@Param("userId") Long userId);
    
    /**
     * 统计用户的日历数量
     */
    long countByUserId(Long userId);
    
    /**
     * 检查用户是否有指定名称的日历
     */
    boolean existsByUserIdAndName(Long userId, String name);
}
