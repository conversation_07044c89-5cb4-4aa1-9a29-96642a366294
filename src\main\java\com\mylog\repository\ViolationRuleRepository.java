package com.mylog.repository;

import com.mylog.model.ViolationRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 违规规则仓库接口
 */
@Repository
public interface ViolationRuleRepository extends JpaRepository<ViolationRule, Long> {
    
    /**
     * 查找所有启用的规则，按优先级排序
     * @return 规则列表
     */
    List<ViolationRule> findByEnabledTrueOrderByPriorityAsc();
    
    /**
     * 根据规则名称查找规则
     * @param ruleName 规则名称
     * @return 规则列表
     */
    List<ViolationRule> findByRuleName(String ruleName);
    
    /**
     * 根据任务状态查找规则
     * @param taskStatus 任务状态
     * @return 规则列表
     */
    List<ViolationRule> findByTaskStatusAndEnabledTrue(String taskStatus);
}
