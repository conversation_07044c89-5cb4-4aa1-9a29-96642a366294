package com.mylog.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mylog.service.OptionsService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 闁活潿鍔嬬花顒€霉鐎ｎ厾妲搁梺顐㈩樀閵嗗秹寮悧鍫濈ウ闁汇劌瀚敮鍫曞礆鐠虹儤鐝?
 */
@RestController
@RequestMapping("/api/options")
public class OptionsController {
    
    private static final Logger logger = LoggerFactory.getLogger(OptionsController.class);
    
    @Autowired
    private OptionsService optionsService;
    
    /**
     * 闁兼儳鍢茶ぐ鍥箥閳ь剟寮垫径鎰ㄥ亾婢舵劑鈧秹寮悧鍫濈ウ
     * @return 闁告牕鎳庨幆鍫ュ箥閳ь剟寮垫径鎰ㄥ亾婢舵劑鈧秹寮悧鍫濈ウ闁汇劌鍤渁p
     */
    @GetMapping
    public Map<String, List<String>> getAllOptions() {
        logger.info("闁兼儳鍢茶ぐ鍥箥閳ь剟寮垫径鎰ㄥ亾婢舵劑鈧秹寮悧鍫濈ウ");
        
        Map<String, List<String>> allOptions = new HashMap<>();
        allOptions.put("personnel", optionsService.getPersonnel());
        allOptions.put("salesPersonnel", optionsService.getSalesPersonnel());
        allOptions.put("mechanicalPersonnel", optionsService.getMechanicalPersonnel());
        allOptions.put("electricalPersonnel", optionsService.getElectricalPersonnel());
        allOptions.put("visionTypes", optionsService.getVisionTypes());
        
        return allOptions;
    }
} 