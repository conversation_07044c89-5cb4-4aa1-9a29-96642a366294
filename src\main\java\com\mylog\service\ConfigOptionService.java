package com.mylog.service;

import com.mylog.model.config.ConfigOption;
import com.mylog.repository.user.ConfigOptionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional("userTransactionManager")
public class ConfigOptionService {
    
    @Autowired
    private ConfigOptionRepository configOptionRepository;
    
    /**
     * 根据类别获取配置选项列表
     */
    public List<ConfigOption> getConfigOptionsByCategory(String category) {
        return configOptionRepository.findByCategoryOrderBySortOrder(category);
    }
    
    /**
     * 根据类别和值获取配置选项
     */
    public Optional<ConfigOption> getConfigOption(String category, String value) {
        ConfigOption option = configOptionRepository.findByCategoryAndValue(category, value);
        return Optional.ofNullable(option);
    }
    
    /**
     * 保存或更新配置选项
     */
    public ConfigOption saveConfigOption(ConfigOption configOption) {
        return configOptionRepository.save(configOption);
    }
    
    /**
     * 批量保存配置选项
     */
    public List<ConfigOption> saveAllConfigOptions(List<ConfigOption> configOptions) {
        return configOptionRepository.saveAll(configOptions);
    }
    
    /**
     * 删除配置选项
     */
    public void deleteConfigOption(String category, String value) {
        ConfigOption.ConfigOptionId id = new ConfigOption.ConfigOptionId(category, value);
        configOptionRepository.deleteById(id);
    }
    
    /**
     * 根据类别删除所有配置选项
     */
    public void deleteConfigOptionsByCategory(String category) {
        configOptionRepository.deleteByCategory(category);
    }
    
    /**
     * 获取所有类别
     */
    public List<String> getAllCategories() {
        return configOptionRepository.findAllCategories();
    }
    
    /**
     * 获取某个类别下的最大排序号
     */
    public Integer getMaxSortOrderByCategory(String category) {
        Integer maxOrder = configOptionRepository.findMaxSortOrderByCategory(category);
        return maxOrder != null ? maxOrder : 0;
    }
    
    /**
     * 为某个类别添加新的配置选项
     */
    public ConfigOption addConfigOption(String category, String value, Double ratio, String remark) {
        Integer nextSortOrder = getMaxSortOrderByCategory(category) + 1;
        ConfigOption configOption = new ConfigOption(category, value, nextSortOrder, ratio, remark);
        return saveConfigOption(configOption);
    }
}
