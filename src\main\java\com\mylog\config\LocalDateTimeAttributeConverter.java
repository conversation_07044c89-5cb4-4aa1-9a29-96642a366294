package com.mylog.config;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * LocalDateTime到字符串的JPA转换器
 * 确保数据库中存储的时间格式为 yyyy-MM-dd HH:mm:ss
 */
@Converter
public class LocalDateTimeAttributeConverter implements AttributeConverter<LocalDateTime, String> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public String convertToDatabaseColumn(LocalDateTime localDateTime) {
        return localDateTime != null ? localDateTime.format(FORMATTER) : null;
    }

    @Override
    public LocalDateTime convertToEntityAttribute(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 首先尝试标准格式
            return LocalDateTime.parse(dateString, FORMATTER);
        } catch (Exception e) {
            try {
                // 如果失败，尝试ISO格式
                return LocalDateTime.parse(dateString.replace(" ", "T"));
            } catch (Exception ex) {
                // 如果都失败，可能是时间戳格式，尝试解析
                try {
                    long timestamp = Long.parseLong(dateString);
                    return java.time.Instant.ofEpochMilli(timestamp)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDateTime();
                } catch (NumberFormatException nfe) {
                    throw new IllegalArgumentException("无法解析时间格式: " + dateString, ex);
                }
            }
        }
    }
}
