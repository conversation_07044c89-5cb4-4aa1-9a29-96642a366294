-- 创建日历表
CREATE TABLE IF NOT EXISTS calendars (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    color VARCHAR(7) DEFAULT '#007bff',
    user_id INTEGER NOT NULL,
    is_default BOOLEAN DEFAULT 0,
    is_shared BOOLEAN DEFAULT 0,
    created_time DATETIME NOT NULL,
    updated_time DATETIME,
    UNIQUE(user_id, name)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_calendars_user_id ON calendars(user_id);
CREATE INDEX IF NOT EXISTS idx_calendars_is_default ON calendars(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_calendars_is_shared ON calendars(is_shared);

-- 创建日历事件表
CREATE TABLE IF NOT EXISTS calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    description VARCHAR(1000),
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    is_all_day BOOLEAN DEFAULT 0,
    location VARCHAR(200),
    event_type VARCHAR(20) DEFAULT 'MEETING',
    priority VARCHAR(10) DEFAULT 'NORMAL',
    is_recurring BOOLEAN DEFAULT 0,
    recurrence_type VARCHAR(20),
    recurrence_interval INTEGER,
    recurrence_end_date DATETIME,
    calendar_id INTEGER NOT NULL,
    creator_id INTEGER NOT NULL,
    created_time DATETIME NOT NULL,
    updated_time DATETIME,
    FOREIGN KEY (calendar_id) REFERENCES calendars(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_events_calendar_id ON calendar_events(calendar_id);
CREATE INDEX IF NOT EXISTS idx_events_creator_id ON calendar_events(creator_id);
CREATE INDEX IF NOT EXISTS idx_events_start_time ON calendar_events(start_time);
CREATE INDEX IF NOT EXISTS idx_events_time_range ON calendar_events(start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_events_event_type ON calendar_events(event_type);
CREATE INDEX IF NOT EXISTS idx_events_priority ON calendar_events(priority);
CREATE INDEX IF NOT EXISTS idx_events_is_recurring ON calendar_events(is_recurring);
CREATE INDEX IF NOT EXISTS idx_events_title ON calendar_events(title);

-- 创建事件提醒表
CREATE TABLE IF NOT EXISTS event_reminders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_id INTEGER NOT NULL,
    reminder_time DATETIME NOT NULL,    reminder_type VARCHAR(20) DEFAULT 'NOTIFICATION',
    is_sent BOOLEAN DEFAULT 0,
    message VARCHAR(500),
    requires_check_in BOOLEAN DEFAULT 0,       -- 新增：标记此提醒是否需要签到
    check_in_window_minutes INTEGER,           -- 新增：可选，签到有效窗口（分钟数），从 reminder_time 开始计算
    created_time DATETIME NOT NULL,
    FOREIGN KEY (event_id) REFERENCES calendar_events(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_reminders_event_id ON event_reminders(event_id);
CREATE INDEX IF NOT EXISTS idx_reminders_time ON event_reminders(reminder_time);
CREATE INDEX IF NOT EXISTS idx_reminders_is_sent ON event_reminders(is_sent);
CREATE INDEX IF NOT EXISTS idx_reminders_pending ON event_reminders(is_sent, reminder_time);

-- 创建事件签到表
CREATE TABLE IF NOT EXISTS event_check_ins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_reminder_id INTEGER NOT NULL,         -- 关联的事件提醒ID
    user_id INTEGER NOT NULL,                   -- 执行签到的用户ID
    check_in_time DATETIME NOT NULL,            -- 实际签到时间
    latitude REAL,                              -- 可选：签到时的纬度
    longitude REAL,                             -- 可选：签到时的经度
    notes VARCHAR(500),                         -- 可选：签到备注
    created_time DATETIME NOT NULL,             -- 记录创建时间
    FOREIGN KEY (event_reminder_id) REFERENCES event_reminders(id) ON DELETE CASCADE
);

-- 创建事件签到表索引
CREATE INDEX IF NOT EXISTS idx_check_ins_reminder_id ON event_check_ins(event_reminder_id);
CREATE INDEX IF NOT EXISTS idx_check_ins_user_id ON event_check_ins(user_id);
CREATE INDEX IF NOT EXISTS idx_check_ins_time ON event_check_ins(check_in_time);

-- 插入示例数据（可选）
-- 注意：这里的用户ID需要根据实际的用户表进行调整
INSERT OR IGNORE INTO calendars (name, description, color, user_id, is_default, is_shared, created_time, updated_time) VALUES
('我的日历', '默认个人日历', '#007bff', 1, 1, 0, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('工作日历', '工作相关事务', '#28a745', 1, 0, 0, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('共享日历', '团队共享日历', '#17a2b8', 1, 0, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00');

-- 插入示例事件
INSERT OR IGNORE INTO calendar_events (title, description, start_time, end_time, is_all_day, location, event_type, priority, calendar_id, creator_id, created_time, updated_time) VALUES
('团队会议', '每周例会讨论项目进展', '2025-05-30 09:00:00', '2025-05-30 10:00:00', 0, '会议室A', 'MEETING', 'NORMAL', 1, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('项目截止日期', '项目最终提交截止', '2025-06-01 23:59:59', NULL, 1, NULL, 'TASK', 'HIGH', 2, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00'),
('客户拜访', '拜访重要客户洽谈合作', '2025-05-31 14:00:00', '2025-05-31 16:00:00', 0, '客户公司', 'APPOINTMENT', 'HIGH', 2, 1, '2025-01-01 00:00:00', '2025-01-01 00:00:00');

-- 插入示例提醒
INSERT OR IGNORE INTO event_reminders (event_id, reminder_time, reminder_type, message, created_time) VALUES
(1, '2025-05-30 08:45:00', 'NOTIFICATION', '15分钟后有团队会议', '2025-01-01 00:00:00'),
(1, '2025-05-30 08:30:00', 'EMAIL', '30分钟后有团队会议，请准备相关材料', '2025-01-01 00:00:00'),
(2, '2025-06-01 09:00:00', 'NOTIFICATION', '今天是项目截止日期，请及时提交', '2025-01-01 00:00:00'),
(3, '2025-05-31 13:45:00', 'NOTIFICATION', '15分钟后需要出发拜访客户', '2025-01-01 00:00:00');
