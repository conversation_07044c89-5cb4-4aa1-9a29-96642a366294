package com.mylog.controller;

import com.mylog.model.PersonnelStatus;
import com.mylog.service.PersonnelStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/personnel-status")
public class PersonnelStatusController {

    private static final Logger logger = LoggerFactory.getLogger(PersonnelStatusController.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");

    @Autowired
    private PersonnelStatusService personnelStatusService;

    /**
     * 显示人员状态列表页面
     */
    @GetMapping
    public String listPersonnelStatus(Model model) {
        List<PersonnelStatus> statusList = personnelStatusService.getAllPersonnelStatus();
        model.addAttribute("statusList", statusList);
        model.addAttribute("activeMenu", "personnel-status");
        return "personnel-status/list";
    }

    /**
     * 显示编辑人员状态页面
     */
    @GetMapping("/edit/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String editPersonnelStatus(@PathVariable("id") Long id, Model model) {
        Optional<PersonnelStatus> statusOpt = personnelStatusService.getPersonnelStatusById(id);
        if (statusOpt.isPresent()) {
            model.addAttribute("personnel", statusOpt.get());
            model.addAttribute("activeMenu", "personnel-status");
            return "personnel-status/edit";
        }
        return "redirect:/personnel-status";
    }

    /**
     * 保存人员状态更新
     */
    @PostMapping("/save")
    @PreAuthorize("hasRole('ADMIN')")
    public String savePersonnelStatus(
            @RequestParam("id") Long id,
            @RequestParam("name") String name,
            @RequestParam("status") String status,
            @RequestParam("location") String location,
            RedirectAttributes redirectAttributes) {
        
        try {
            LocalDateTime now = LocalDateTime.now();
            String currentTime = now.format(FORMATTER);
            
            PersonnelStatus personnelStatus = personnelStatusService.getPersonnelStatusById(id)
                    .orElseThrow(() -> new IllegalArgumentException("人员状态记录不存在"));
            
            personnelStatus.setName(name);
            personnelStatus.setStatus(status);
            personnelStatus.setLocation(location);
            personnelStatus.setCreateTime(currentTime);
            personnelStatus.setEffectiveTime(currentTime);
            
            personnelStatusService.saveOrUpdate(personnelStatus);
            
            redirectAttributes.addFlashAttribute("message", "人员状态更新成功");
        } catch (Exception e) {
            logger.error("更新人员状态时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "更新人员状态失败: " + e.getMessage());
        }
        
        return "redirect:/personnel-status";
    }

    /**
     * 批量更新人员状态
     */
    @PostMapping("/batch-update")
    @PreAuthorize("hasRole('ADMIN')")
    public String batchUpdateStatus(
            @RequestParam("ids") List<Long> ids,
            @RequestParam("status") String status,
            @RequestParam("location") String location,
            RedirectAttributes redirectAttributes) {
        
        try {
            int updatedCount = 0;
            for (Long id : ids) {
                PersonnelStatus updated = personnelStatusService.updatePersonnelStatus(id, status, location);
                if (updated != null) {
                    updatedCount++;
                }
            }
            
            redirectAttributes.addFlashAttribute("message", String.format("已成功更新 %d 条人员状态记录", updatedCount));
        } catch (Exception e) {
            logger.error("批量更新人员状态时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "批量更新人员状态失败: " + e.getMessage());
        }
        
        return "redirect:/personnel-status";
    }
}
