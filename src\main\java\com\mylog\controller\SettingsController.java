package com.mylog.controller;

import com.mylog.config.AppVersion;
import com.mylog.model.user.User;
import com.mylog.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Optional;

@Controller
@RequestMapping("/settings")
public class SettingsController {

    private static final Logger logger = LoggerFactory.getLogger(SettingsController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private AppVersion appVersion;

    @GetMapping
    public String showSettings(Model model) {
        // 获取当前登录用户
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();

        Optional<User> userOpt = userService.findUserByUsername(username);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            model.addAttribute("user", user);
            model.addAttribute("themeStyles", User.ThemeStyle.values());
            model.addAttribute("activeMenu", "settings");

            // 获取用户主题
            String theme = user.getThemeStyle() != null ? user.getThemeStyle().toString().toLowerCase() : "light";
            model.addAttribute("userTheme", "theme-" + theme);

            // 检查用户是否为管理员，如果是则添加版本信息
            boolean isAdmin = user.getRole() == User.UserRole.ADMIN;
            if (isAdmin) {
                model.addAttribute("appVersion", appVersion.getVersion());
            }

            return "settings/index";
        } else {
            return "redirect:/dashboard";
        }
    }

    @PostMapping("/change-password")
    public String changePassword(
            @RequestParam("currentPassword") String currentPassword,
            @RequestParam("newPassword") String newPassword,
            @RequestParam("confirmPassword") String confirmPassword,
            RedirectAttributes redirectAttributes) {

        // 获取当前登录用户
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();

        Optional<User> userOpt = userService.findUserByUsername(username);
        if (!userOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "用户不存在");
            return "redirect:/settings";
        }

        User user = userOpt.get();

        // 验证新密码和确认密码是否一致
        if (!newPassword.equals(confirmPassword)) {
            redirectAttributes.addFlashAttribute("error", "新密码和确认密码不一致");
            return "redirect:/settings";
        }

        // 调用服务修改密码
        boolean success = userService.changePassword(user.getUserId(), currentPassword, newPassword);
        if (success) {
            redirectAttributes.addFlashAttribute("message", "密码修改成功");
        } else {
            redirectAttributes.addFlashAttribute("error", "当前密码不正确");
        }

        return "redirect:/settings";
    }

    @PostMapping("/change-theme")
    public String changeTheme(
            @RequestParam("themeStyle") User.ThemeStyle themeStyle,
            RedirectAttributes redirectAttributes) {

        // 获取当前登录用户
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();

        Optional<User> userOpt = userService.findUserByUsername(username);
        if (!userOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "用户不存在");
            return "redirect:/settings";
        }

        User user = userOpt.get();
        user.setThemeStyle(themeStyle);

        // 保存用户设置
        userService.saveUser(user);
        redirectAttributes.addFlashAttribute("message", "主题设置已更新");

        return "redirect:/settings";
    }
}