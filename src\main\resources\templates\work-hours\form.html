<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head(${workHours.id != null ? '编辑工期记录' : '新建工期记录'})}">
    <meta charset="UTF-8">
    <title>工期记录表单</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script#customScript})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${workHours.id == null ? '新建工期记录' : '编辑工期记录'}">工期记录表单</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a th:href="@{/work-hours}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>返回列表
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <form th:action="@{/work-hours/save}" method="post" th:object="${workHours}" class="needs-validation" novalidate>
                    <input type="hidden" th:field="*{id}" />
                    <input type="hidden" name="referer" th:value="${referer}" />

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">基本信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="businessType" class="form-label">业务类型<span class="text-danger">*</span></label>
                                    <select class="form-select" id="businessType" th:field="*{businessType}" required>
                                        <option value="">请选择业务类型</option>
                                        <option value="项目">项目</option>
                                        <option value="任务">任务</option>
                                        <option value="培训">培训</option>
                                        <option value="会议">会议</option>
                                        <option value="其他">其他</option>
                                    </select>
                                    <div class="invalid-feedback">请选择业务类型</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="businessId" class="form-label">业务ID<span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="businessId" th:field="*{businessId}" required min="1">
                                    <div class="invalid-feedback">请输入有效的业务ID</div>
                                    <small class="form-text text-muted">请输入相关项目、任务等的ID</small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="hoursChange" class="form-label">工期变化<span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="hoursChange" th:field="*{hoursChange}" 
                                               required step="0.1" min="-999.9" max="999.9" placeholder="0.0">
                                        <span class="input-group-text">小时</span>
                                    </div>
                                    <div class="invalid-feedback">请输入工期变化（-999.9到999.9）</div>
                                    <small class="form-text text-muted">正数表示增加工期，负数表示减少工期</small>
                                </div>
                                <div class="col-md-6" th:if="${workHours.id != null}">
                                    <label for="hoursInventory" class="form-label">当前累计工期</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="hoursInventory" th:field="*{hoursInventory}" readonly>
                                        <span class="input-group-text">小时</span>
                                    </div>
                                    <small class="form-text text-muted">编辑时不能直接修改存量，由系统自动计算</small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="reason" class="form-label">原因<span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="reason" th:field="*{reason}" rows="3" required 
                                              maxlength="500" placeholder="请详细描述工期变化的原因..."></textarea>
                                    <div class="invalid-feedback">请输入工期变化的原因</div>
                                    <div class="form-text">
                                        <span id="reasonCount">0</span>/500字符
                                    </div>
                                </div>
                            </div>                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="creator" class="form-label">创建人</label>
                                    <input type="text" class="form-control" id="creator" th:field="*{creator}" 
                                           maxlength="100" placeholder="请输入创建人姓名">
                                    <small class="form-text text-muted">记录工期的人员姓名</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="remark" class="form-label">备注</label>
                                    <input type="text" class="form-control" id="remark" th:field="*{remark}" 
                                           maxlength="200" placeholder="请输入备注信息">
                                    <small class="form-text text-muted">额外的说明信息</small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="bonus" class="form-label">绩效分</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="bonus" th:field="*{bonus}" 
                                               step="0.01" min="0" max="999999.99" placeholder="0.00">
                                    </div>
                                    <small class="form-text text-muted">与此工期相关的绩效分（可选）</small>
                                </div>
                            </div>

                            <!-- 预览区域（新建时显示） -->
                            <div class="row mb-3" th:if="${workHours.id == null}">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading"><i class="bi bi-info-circle me-2"></i>预计结果</h6>
                                        <p class="mb-0">
                                            <span id="previewText">请填写业务类型、业务ID和工期变化后，将显示预计的累计工期。</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-1"></i>
                                    <span th:text="${workHours.id == null ? '创建工期记录' : '更新工期记录'}">保存</span>
                                </button>
                                <a th:href="@{/work-hours}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle me-1"></i>取消
                                </a>
                                <button type="button" class="btn btn-outline-info" id="previewBtn" th:if="${workHours.id == null}">
                                    <i class="bi bi-eye me-1"></i>预览结果
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 侧边栏信息 -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>帮助信息</h6>
                    </div>
                    <div class="card-body">
                        <h6>业务类型说明：</h6>
                        <ul class="small">
                            <li><strong>项目：</strong>与具体项目相关的工期</li>
                            <li><strong>任务：</strong>与具体任务相关的工期</li>
                            <li><strong>培训：</strong>学习培训相关的工期</li>
                            <li><strong>会议：</strong>会议相关的工期</li>
                            <li><strong>其他：</strong>其他类型的工期</li>
                        </ul>
                        
                        <h6 class="mt-3">工期变化说明：</h6>
                        <ul class="small">
                            <li>正数：增加工期（如完成工作）</li>
                            <li>负数：减少工期（如修正错误）</li>
                            <li>支持小数，精确到0.1小时</li>
                        </ul>

                        <h6 class="mt-3">累计工期说明：</h6>
                        <p class="small">
                            系统会自动计算该业务的累计累计工期。新记录会在当前存量基础上进行变化。
                        </p>
                    </div>
                </div>

                <!-- 最近记录（如果是编辑现有记录） -->
                <div class="card" th:if="${recentRecords != null && !recentRecords.empty}">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>相关记录</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item px-0 py-2" th:each="record : ${recentRecords}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold small" th:text="${record.reason}">原因</div>
                                        <small class="text-muted" th:text="${record.createdTime}">时间</small>
                                    </div>
                                    <span th:class="${record.hoursChange >= 0 ? 'badge bg-success' : 'badge bg-danger'}"
                                          th:text="${record.hoursChange >= 0 ? '+' : ''} + ${record.hoursChange} + 'h'">+2.0h</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script id="customScript">
        $(document).ready(function() {
            // 字符计数
            const reasonTextarea = $('#reason');
            const reasonCount = $('#reasonCount');
            
            function updateReasonCount() {
                const count = reasonTextarea.val().length;
                reasonCount.text(count);
                reasonCount.toggleClass('text-danger', count > 500);
            }
            
            reasonTextarea.on('input', updateReasonCount);
            updateReasonCount(); // 初始化计数

            // 预览功能（仅新建时）
            $('#previewBtn').click(function() {
                const businessType = $('#businessType').val();
                const businessId = $('#businessId').val();
                const hoursChange = parseFloat($('#hoursChange').val()) || 0;
                
                if (!businessType || !businessId) {
                    alert('请先填写业务类型和业务ID');
                    return;
                }
                
                // 调用API获取预览结果
                $.get('/work-hours/api/preview', {
                    businessType: businessType,
                    businessId: businessId,
                    hoursChange: hoursChange
                })
                .done(function(data) {
                    $('#previewText').html(
                        `业务类型：<strong>${businessType}</strong>，业务ID：<strong>${businessId}</strong><br>` +
                        `当前存量：<strong>${data.currentInventory}小时</strong><br>` +
                        `工期变化：<strong>${hoursChange >= 0 ? '+' : ''}${hoursChange}小时</strong><br>` +
                        `预计存量：<strong>${data.predictedInventory}小时</strong>`
                    );
                })
                .fail(function() {
                    $('#previewText').text('预览失败，请检查输入的业务类型和ID是否正确。');
                });
            });

            // 实时预览（当输入改变时）
            $('#businessType, #businessId, #hoursChange').on('input change', function() {
                const businessType = $('#businessType').val();
                const businessId = $('#businessId').val();
                const hoursChange = parseFloat($('#hoursChange').val()) || 0;
                
                if (businessType && businessId && $('#previewBtn').length > 0) {
                    // 延迟500ms进行预览，避免频繁请求
                    clearTimeout(window.previewTimeout);
                    window.previewTimeout = setTimeout(function() {
                        $('#previewBtn').click();
                    }, 500);
                }
            });

            // 表单验证
            const form = document.querySelector('.needs-validation');
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    </script>
</body>
</html>
