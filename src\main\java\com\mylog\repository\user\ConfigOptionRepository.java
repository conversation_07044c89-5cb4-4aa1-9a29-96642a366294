package com.mylog.repository.user;

import com.mylog.model.config.ConfigOption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConfigOptionRepository extends JpaRepository<ConfigOption, ConfigOption.ConfigOptionId> {
    
    /**
     * 根据类别查找配置选项
     */
    List<ConfigOption> findByCategoryOrderBySortOrder(String category);
    
    /**
     * 根据类别和值查找配置选项
     */
    ConfigOption findByCategoryAndValue(String category, String value);
    
    /**
     * 查找所有类别
     */
    @Query("SELECT DISTINCT c.category FROM ConfigOption c")
    List<String> findAllCategories();
    
    /**
     * 根据类别删除配置选项
     */
    void deleteByCategory(String category);
    
    /**
     * 查找某个类别下的最大排序号
     */
    @Query("SELECT MAX(c.sortOrder) FROM ConfigOption c WHERE c.category = :category")
    Integer findMaxSortOrderByCategory(@Param("category") String category);
}
