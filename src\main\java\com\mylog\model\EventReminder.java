package com.mylog.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mylog.config.LocalDateTimeAttributeConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 事件提醒实体类
 */
@Entity
@Table(name = "event_reminders")
public class EventReminder {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "事件ID不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_id", nullable = false)
    private CalendarEvent event;      @NotNull(message = "提醒时间不能为空")
    @Column(name = "reminder_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime reminderTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "reminder_type", length = 20)
    private ReminderType reminderType = ReminderType.NOTIFICATION;
    
    @Column(name = "is_sent", columnDefinition = "boolean default false")
    private Boolean isSent = false;
      @Column(name = "message", length = 500)
    private String message;

    @Column(name = "requires_check_in", columnDefinition = "boolean default false")
    private Boolean requiresCheckIn = false;

    @Column(name = "check_in_window_minutes")
    private Integer checkInWindowMinutes;

    @Column(name = "recipients", length = 1000)
    private String recipients;

    @Column(name = "created_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime createdTime;
    
    // 枚举定义
    public enum ReminderType {
        NOTIFICATION, EMAIL, SMS
    }
      // 构造函数
    public EventReminder() {
        this.createdTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    public EventReminder(CalendarEvent event, LocalDateTime reminderTime) {
        this();
        this.event = event;
        this.reminderTime = reminderTime;
    }
      // PrePersist
    @PrePersist
    protected void onCreate() {
        createdTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public CalendarEvent getEvent() {
        return event;
    }
    
    public void setEvent(CalendarEvent event) {
        this.event = event;
    }
    
    public LocalDateTime getReminderTime() {
        return reminderTime;
    }
    
    public void setReminderTime(LocalDateTime reminderTime) {
        this.reminderTime = reminderTime;
    }
    
    public ReminderType getReminderType() {
        return reminderType;
    }
    
    public void setReminderType(ReminderType reminderType) {
        this.reminderType = reminderType;
    }
    
    public Boolean getIsSent() {
        return isSent;
    }
    
    public void setIsSent(Boolean isSent) {
        this.isSent = isSent;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
      public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public Boolean getRequiresCheckIn() {
        return requiresCheckIn;
    }

    public void setRequiresCheckIn(Boolean requiresCheckIn) {
        this.requiresCheckIn = requiresCheckIn;
    }

    public Integer getCheckInWindowMinutes() {
        return checkInWindowMinutes;
    }

    public void setCheckInWindowMinutes(Integer checkInWindowMinutes) {
        this.checkInWindowMinutes = checkInWindowMinutes;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }
}
