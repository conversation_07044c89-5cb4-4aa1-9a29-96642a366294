package com.mylog.repository;

import com.mylog.model.UserActivityLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Repository
public interface UserActivityLogRepository extends JpaRepository<UserActivityLog, Long> {
    
    // 根据用户ID查询活动日志
    List<UserActivityLog> findByUserId(Long userId);
    
    // 根据用户ID分页查询活动日志
    Page<UserActivityLog> findByUserId(Long userId, Pageable pageable);
    
    // 根据活动类型查询活动日志
    List<UserActivityLog> findByActivityType(UserActivityLog.ActivityType activityType);
    
    // 根据活动类型分页查询活动日志
    Page<UserActivityLog> findByActivityType(UserActivityLog.ActivityType activityType, Pageable pageable);
      // 按时间范围查询活动日志 - 使用createdDate字段
    @Query("SELECT l FROM UserActivityLog l WHERE " +
           "(:startTimeStr IS NULL OR l.createdDate >= :startTimeStr) AND " +
           "(:endTimeStr IS NULL OR l.createdDate <= :endTimeStr)")
    List<UserActivityLog> findByCreatedDateBetween(
           @Param("startTimeStr") String startTimeStr,
           @Param("endTimeStr") String endTimeStr);
    
    // 兼容性方法 - 保留原有方法名称
    default List<UserActivityLog> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime) {
        String startTimeStr = startTime != null ? startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        String endTimeStr = endTime != null ? endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        return findByCreatedDateBetween(startTimeStr, endTimeStr);
    }
      // 按时间范围分页查询活动日志 - 使用createdDate字段
    @Query("SELECT l FROM UserActivityLog l WHERE " +
           "(:startTimeStr IS NULL OR l.createdDate >= :startTimeStr) AND " +
           "(:endTimeStr IS NULL OR l.createdDate <= :endTimeStr)")
    Page<UserActivityLog> findByCreatedDateBetween(
           @Param("startTimeStr") String startTimeStr,
           @Param("endTimeStr") String endTimeStr,
           Pageable pageable);
    
    // 兼容性方法 - 保留原有方法名称
    default Page<UserActivityLog> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        String startTimeStr = startTime != null ? startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        String endTimeStr = endTime != null ? endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        return findByCreatedDateBetween(startTimeStr, endTimeStr, pageable);
    }
    
    // 组合条件查询：用户ID和活动类型
    Page<UserActivityLog> findByUserIdAndActivityType(Long userId, UserActivityLog.ActivityType activityType, Pageable pageable);
      // 组合条件查询：用户ID和时间范围 - 使用createdDate字段
    @Query("SELECT l FROM UserActivityLog l WHERE " +
           "l.userId = :userId AND " +
           "(:startTimeStr IS NULL OR l.createdDate >= :startTimeStr) AND " +
           "(:endTimeStr IS NULL OR l.createdDate <= :endTimeStr)")
    Page<UserActivityLog> findByUserIdAndCreatedDateBetween(
           @Param("userId") Long userId,
           @Param("startTimeStr") String startTimeStr,
           @Param("endTimeStr") String endTimeStr,
           Pageable pageable);
    
    // 兼容性方法 - 保留原有方法名称
    default Page<UserActivityLog> findByUserIdAndTimestampBetween(Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        String startTimeStr = startTime != null ? startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        String endTimeStr = endTime != null ? endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        return findByUserIdAndCreatedDateBetween(userId, startTimeStr, endTimeStr, pageable);
    }
      // 组合条件查询：活动类型和时间范围 - 使用createdDate字段
    @Query("SELECT l FROM UserActivityLog l WHERE " +
           "l.activityType = :activityType AND " +
           "(:startTimeStr IS NULL OR l.createdDate >= :startTimeStr) AND " +
           "(:endTimeStr IS NULL OR l.createdDate <= :endTimeStr)")
    Page<UserActivityLog> findByActivityTypeAndCreatedDateBetween(
           @Param("activityType") UserActivityLog.ActivityType activityType,
           @Param("startTimeStr") String startTimeStr,
           @Param("endTimeStr") String endTimeStr,
           Pageable pageable);
    
    // 兼容性方法 - 保留原有方法名称
    default Page<UserActivityLog> findByActivityTypeAndTimestampBetween(UserActivityLog.ActivityType activityType, 
                                                             LocalDateTime startTime, LocalDateTime endTime, 
                                                             Pageable pageable) {
        String startTimeStr = startTime != null ? startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        String endTimeStr = endTime != null ? endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        return findByActivityTypeAndCreatedDateBetween(activityType, startTimeStr, endTimeStr, pageable);
    }
    
    // 多条件组合查询 - 使用createdDate字段
    @Query("SELECT l FROM UserActivityLog l WHERE " +
           "(:userId IS NULL OR l.userId = :userId) AND " +
           "(:username IS NULL OR l.username LIKE %:username%) AND " +
           "(:activityType IS NULL OR l.activityType = :activityType) AND " +
           "(:entityType IS NULL OR l.entityType = :entityType) AND " +
           "(:entityId IS NULL OR l.entityId = :entityId) AND " +
           "(:startTimeStr IS NULL OR l.createdDate >= :startTimeStr) AND " +
           "(:endTimeStr IS NULL OR l.createdDate <= :endTimeStr)")
    Page<UserActivityLog> searchLogsByCreatedDate(
                                  @Param("userId") Long userId,
                                  @Param("username") String username,
                                  @Param("activityType") UserActivityLog.ActivityType activityType,
                                  @Param("entityType") String entityType,
                                  @Param("entityId") Long entityId,
                                  @Param("startTimeStr") String startTimeStr,
                                  @Param("endTimeStr") String endTimeStr,
                                  Pageable pageable);
                                  
    // 兼容性方法 - 保留原有方法名称和参数
    default Page<UserActivityLog> searchLogs(
                                  Long userId,
                                  String username,
                                  UserActivityLog.ActivityType activityType,
                                  String entityType,
                                  Long entityId,
                                  LocalDateTime startTime,
                                  LocalDateTime endTime,
                                  Pageable pageable) {
        String startTimeStr = startTime != null ? startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        String endTimeStr = endTime != null ? endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        return searchLogsByCreatedDate(userId, username, activityType, entityType, entityId, startTimeStr, endTimeStr, pageable);
    }
}