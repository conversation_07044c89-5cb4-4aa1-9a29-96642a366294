package com.mylog.service.impl;

import com.mylog.model.SearchPlan;
import com.mylog.repository.SearchPlanRepository;
import com.mylog.service.SearchPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class SearchPlanServiceImpl implements SearchPlanService {
    
    @Autowired
    private SearchPlanRepository searchPlanRepository;
    
    @Override
    @Transactional
    public SearchPlan saveSearchPlan(SearchPlan searchPlan) {
        // 设置创建时间
        if (searchPlan.getCreatedDate() == null) {
            searchPlan.setCreatedDateTime(LocalDateTime.now());
        }
        return searchPlanRepository.save(searchPlan);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<SearchPlan> getSearchPlansByUsername(String username) {
        return searchPlanRepository.findByUsernameOrderByCreatedDateDesc(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<SearchPlan> getSearchPlanById(Long planId) {
        return searchPlanRepository.findById(planId);
    }
    
    @Override
    @Transactional
    public void deleteSearchPlan(Long planId) {
        searchPlanRepository.deleteById(planId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isPlanNameExists(String username, String planName) {
        List<SearchPlan> plans = searchPlanRepository.findByUsernameAndName(username, planName);
        return !plans.isEmpty();
    }
} 