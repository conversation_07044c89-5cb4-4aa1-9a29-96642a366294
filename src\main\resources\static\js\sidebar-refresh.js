/**
 * 侧边栏自动刷新管理器
 * 负责在用户无操作时每5分钟自动刷新侧边栏徽章数据
 */
class SidebarRefreshManager {    constructor() {
        this.lastActivity = Date.now();
        // 临时调试模式：将刷新间隔设为30秒便于测试
        this.refreshInterval = 5 * 60 * 1000; // 30秒 (生产环境应该是 5 * 60 * 1000)
        this.activityCheckInterval = 10 * 1000; // 10秒检查一次
        this.refreshTimer = null;
        this.activityTimer = null;
        this.isRefreshing = false;
        
        this.init();
    }
    
    /**
     * 初始化侧边栏刷新管理器
     */
    init() {
        this.setupActivityListeners();
        this.startActivityMonitoring();
        console.log('侧边栏自动刷新管理器已启动');
    }
    
    /**
     * 设置用户活动监听器
     */
    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateLastActivity();
            }, true);
        });
    }
    
    /**
     * 更新最后活动时间
     */
    updateLastActivity() {
        this.lastActivity = Date.now();
        
        // 如果有刷新定时器在运行，重置它
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    /**
     * 开始活动监控
     */
    startActivityMonitoring() {
        this.activityTimer = setInterval(() => {
            this.checkForRefresh();
        }, this.activityCheckInterval);
    }
      /**
     * 检查是否需要刷新
     */
    checkForRefresh() {
        const timeSinceLastActivity = Date.now() - this.lastActivity;
        
        // 调试日志
        console.log(`检查刷新状态: 距离上次活动 ${Math.floor(timeSinceLastActivity / 1000)} 秒`);
        
        // 如果用户在5分钟内有活动，不刷新
        if (timeSinceLastActivity < this.refreshInterval) {
            return;
        }
        
        // 如果已经有刷新定时器在运行，不重复设置
        if (this.refreshTimer) {
            return;
        }
        
        console.log('准备刷新侧边栏数据...');
        
        // 设置刷新定时器
        this.refreshTimer = setTimeout(() => {
            this.refreshSidebarData();
        }, 1000); // 1秒后刷新，确保用户真的停止了活动
    }
    
    /**
     * 刷新侧边栏数据
     */
    async refreshSidebarData() {
        if (this.isRefreshing) {
            return;
        }
        
        this.isRefreshing = true;
        
        try {
            console.log('开始刷新侧边栏数据...');
              const response = await fetch('/api/sidebar/badges', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
              const data = await response.json();
            console.log('API返回的数据:', data);
            
            if (data.success) {
                this.updateSidebarBadges(data);
                console.log('侧边栏数据刷新成功');
            } else {
                console.warn('侧边栏数据刷新失败:', data.message);
            }
            
        } catch (error) {
            console.error('刷新侧边栏数据时发生错误:', error);
        } finally {
            this.isRefreshing = false;
            this.refreshTimer = null;
            // 更新最后活动时间，避免立即再次刷新
            this.updateLastActivity();
        }
    }
    
    /**
     * 更新侧边栏徽章显示
     */
    updateSidebarBadges(data) {
        // 更新各种徽章数据
        this.updateBadge('inProgressTaskCount', data.inProgressTaskCount);
        this.updateBadge('inProgressDifficultTaskCount', data.inProgressDifficultTaskCount);
        this.updateBadge('inProgressSpecialTaskCount', data.inProgressSpecialTaskCount);
        this.updateBadge('inProgressDelegatedTaskCount', data.inProgressDelegatedTaskCount);
        this.updateBadge('inProgressTrainingTaskCount', data.inProgressTrainingTaskCount);
        this.updateBadge('myInProgressProjectCount', data.myInProgressProjectCount);
        this.updateBadge('unreadMessageCount', data.unreadMessageCount);
        this.updateBadge('todoTasksCount', data.todoTasksCount);
        this.updateBadge('myProcessingWorkflowCount', data.myProcessingWorkflowCount);
        this.updateBadge('pendingCheckInCount', data.pendingCheckInCount);
        this.updateBadge('recentRewardPenaltyCount', data.recentRewardPenaltyCount);
        this.updateBadge('processingInstanceCount', data.processingInstanceCount);
    }    /**
     * 更新单个徽章
     */
    updateBadge(dataKey, value) {
        // 根据数据键找到对应的徽章元素
        const elements = this.getBadgeSelectors(dataKey);
        console.log(`更新徽章 ${dataKey}: 值=${value}, 找到${elements.length}个元素`);
        
        elements.forEach((element, index) => {
            console.log(`  元素${index}:`, element, `当前文本: "${element.textContent}"`);
            if (value && value > 0) {
                element.textContent = value;
                element.style.display = '';
                // 添加动画效果表示数据已更新
                this.addUpdateAnimation(element);
                console.log(`  已更新为: "${element.textContent}"`);
            } else {
                element.style.display = 'none';
                console.log('  已隐藏（值为0或null）');
            }
        });
    }/**
     * 获取徽章选择器
     */
    getBadgeSelectors(dataKey) {
        // 使用更精确的方式找到徽章元素
        const elements = [];
        
        switch(dataKey) {
            case 'inProgressTaskCount':
                // 订单任务徽章
                elements.push(...document.querySelectorAll('a[href="/tasks/order-tasks"] .badge'));
                break;
            case 'inProgressDifficultTaskCount':
                // 难点焦点任务徽章
                elements.push(...document.querySelectorAll('a[href="/tasks/difficult"] .badge'));
                break;
            case 'inProgressSpecialTaskCount':
                // 专项任务徽章
                elements.push(...document.querySelectorAll('a[href="/tasks/special"] .badge'));
                break;
            case 'inProgressDelegatedTaskCount':
                // 分管任务徽章
                elements.push(...document.querySelectorAll('a[href="/delegated-tasks"] .badge'));
                break;
            case 'inProgressTrainingTaskCount':
                // 教育培训任务徽章
                elements.push(...document.querySelectorAll('a[href="/tasks/training"] .badge'));
                break;
            case 'myInProgressProjectCount':
                // 我的项目徽章
                elements.push(...document.querySelectorAll('a[href="/projects/my"] .badge'));
                break;
            case 'unreadMessageCount':
                // 未读消息徽章
                elements.push(...document.querySelectorAll('a[href="/messages"] .badge'));
                break;
            case 'todoTasksCount':
                // 待审批任务徽章 (我的流程页面中的橙色徽章)
                elements.push(...document.querySelectorAll('a[href="/workflow/my"] .badge.bg-mycolor'));
                break;
            case 'myProcessingWorkflowCount':
                // 我的处理中流程徽章 (我的流程页面中的蓝色徽章)
                elements.push(...document.querySelectorAll('a[href="/workflow/my"] .badge.bg-primary'));
                break;
            case 'pendingCheckInCount':
                // 待签到徽章 (黄色警告徽章)
                elements.push(...document.querySelectorAll('a[href="/check-in"] .badge'));
                break;
            case 'recentRewardPenaltyCount':
                // 最近奖罚记录徽章 (红色危险徽章)
                elements.push(...document.querySelectorAll('a[href="/my-points"] .badge'));
                break;
            case 'processingInstanceCount':
                // 流程管理徽章
                elements.push(...document.querySelectorAll('a[href="/workflow"] .badge'));
                break;
        }
        
        return elements;
    }
      /**
     * 添加更新动画效果
     */
    addUpdateAnimation(element) {
        // 保存原始样式
        const originalTransform = element.style.transform;
        const originalTransition = element.style.transition;
        
        // 添加缩放动画效果
        element.style.transition = 'transform 0.3s ease';
        element.style.transform = 'scale(1.2)';
        
        setTimeout(() => {
            element.style.transform = originalTransform || 'scale(1)';
            
            // 恢复原始transition
            setTimeout(() => {
                element.style.transition = originalTransition;
            }, 300);
        }, 200);
    }    /**
     * 手动触发刷新 (用于测试)
     */
    manualRefresh() {
        console.log('手动触发侧边栏刷新...');
        this.refreshSidebarData();
    }
    
    /**
     * 测试所有徽章选择器 (用于调试)
     */
    testSelectors() {
        console.log('=== 测试徽章选择器 ===');
        const testCases = [
            'inProgressTaskCount',
            'inProgressDifficultTaskCount', 
            'inProgressSpecialTaskCount',
            'inProgressDelegatedTaskCount',
            'inProgressTrainingTaskCount',
            'myInProgressProjectCount',
            'unreadMessageCount',
            'todoTasksCount',
            'myProcessingWorkflowCount',
            'pendingCheckInCount',
            'recentRewardPenaltyCount',
            'processingInstanceCount'
        ];
        
        testCases.forEach(dataKey => {
            const elements = this.getBadgeSelectors(dataKey);
            console.log(`${dataKey}: 找到 ${elements.length} 个元素`, elements);
        });
        console.log('=== 选择器测试完成 ===');
    }
    
    /**
     * 销毁管理器
     */
    destroy() {
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }
        if (this.activityTimer) {
            clearInterval(this.activityTimer);
        }
        console.log('侧边栏自动刷新管理器已停止');
    }
}

// 当DOM加载完成后初始化侧边栏刷新管理器
document.addEventListener('DOMContentLoaded', function() {
    // 确保在侧边栏存在时才初始化
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        window.sidebarRefreshManager = new SidebarRefreshManager();
    }
});

// 在页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (window.sidebarRefreshManager) {
        window.sidebarRefreshManager.destroy();
    }
});
