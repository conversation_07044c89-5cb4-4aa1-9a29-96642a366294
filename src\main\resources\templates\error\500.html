<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head th:replace="~{fragments/layout :: head('500 - 服务器错误')}">
    <title>500 - 服务器错误</title>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <h1 class="display-1 text-danger mb-4">500</h1>
                            <h2 class="h4 mb-4">服务器内部错误</h2>
                            <p class="text-muted mb-4" th:text="${error ?: '抱歉，服务器出现了一些问题。'}">
                                抱歉，服务器出现了一些问题。
                            </p>
                            <div class="mb-4">
                                <a href="javascript:history.back()" class="btn btn-primary me-2">
                                    <i class="bi bi-arrow-left"></i> 返回上一页
                                </a>
                                <a th:href="@{/}" class="btn btn-secondary">
                                    <i class="bi bi-house"></i> 返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
