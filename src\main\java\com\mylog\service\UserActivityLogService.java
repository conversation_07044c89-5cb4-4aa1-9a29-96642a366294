package com.mylog.service;

import com.mylog.model.UserActivityLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface UserActivityLogService {
    
    /**
     * 保存用户活动日志
     * @param log 日志对象
     * @return 保存后的日志对象
     */
    UserActivityLog saveLog(UserActivityLog log);
    
    /**
     * 记录用户登录
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logLogin(Long userId, String username, String ipAddress, String accessType);
    
    /**
     * 记录用户登出
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logLogout(Long userId, String username, String ipAddress, String accessType);
    
    /**
     * 记录创建操作
     * @param userId 用户ID
     * @param username 用户名
     * @param description 描述
     * @param ipAddress IP地址
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logCreate(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType);
    
    /**
     * 记录更新操作
     * @param userId 用户ID
     * @param username 用户名
     * @param description 描述
     * @param ipAddress IP地址
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logUpdate(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType);
    
    /**
     * 记录删除操作
     * @param userId 用户ID
     * @param username 用户名
     * @param description 描述
     * @param ipAddress IP地址
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logDelete(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType);
      /**
     * 记录查看操作
     * @param userId 用户ID
     * @param username 用户名
     * @param description 描述
     * @param ipAddress IP地址
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logView(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType);
      /**
     * 记录下载操作
     * @param userId 用户ID
     * @param username 用户名
     * @param description 描述
     * @param ipAddress IP地址
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logDownload(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType);
    
    /**
     * 记录设置变更操作（包括状态变更）
     * @param userId 用户ID
     * @param username 用户名
     * @param description 描述
     * @param ipAddress IP地址
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param accessType 访问终端类型
     * @return 保存的日志对象
     */
    UserActivityLog logSettingsChange(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType);
    
    /**
     * 根据ID查找日志
     * @param id 日志ID
     * @return 日志对象
     */
    Optional<UserActivityLog> findLogById(Long id);
    
    /**
     * 查找所有日志
     * @return 日志列表
     */
    List<UserActivityLog> findAllLogs();
    
    /**
     * 分页查找所有日志
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<UserActivityLog> findAllLogs(Pageable pageable);
    
    /**
     * 根据用户ID查找日志
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<UserActivityLog> findLogsByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据活动类型查找日志
     * @param activityType 活动类型
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<UserActivityLog> findLogsByActivityType(UserActivityLog.ActivityType activityType, Pageable pageable);
    
    /**
     * 根据时间范围查找日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<UserActivityLog> findLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 高级搜索日志
     * @param searchCriteria 搜索条件
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<UserActivityLog> searchLogs(Map<String, Object> searchCriteria, Pageable pageable);
    
    /**
     * 删除指定时间范围之前的日志
     * @param beforeTime 时间点
     * @return 删除的日志数量
     */
    long deleteLogsBefore(LocalDateTime beforeTime);
} 