package com.mylog.repository;

import com.mylog.model.Submit2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

@Repository
public interface Submit2Repository extends JpaRepository<Submit2, Long> {
    
    Page<Submit2> findByTaskId(Long taskId, Pageable pageable);
    
    List<Submit2> findByTaskId(Long taskId);
    
    long countByTaskId(Long taskId);
} 