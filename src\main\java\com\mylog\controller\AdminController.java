package com.mylog.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import com.mylog.service.UserService;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private UserService userService;

    @GetMapping("/users")
    public String userManagement(Model model) {
        // 获取所有用户列表
        model.addAttribute("users", userService.findAllUsers());
        // 设置活动菜单
        model.addAttribute("activeMenu", "users");
        return "admin/users/index";
    }
}