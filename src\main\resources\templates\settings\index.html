<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('用户设置')}">
    <meta charset="UTF-8">
    <title>用户设置</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">用户设置</h1>
        </div>

        <!-- 提示消息 -->
        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
            <span th:text="${message}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- 版本信息，只对管理员可见 -->
        <div class="row" sec:authorize="hasRole('ADMIN')" th:if="${appVersion != null}">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>当前版本：</strong> <span class="badge bg-primary" th:text="${appVersion}">1.0.0</span></p>
                                <p class="small text-muted mb-0">版本格式：主版本.日期.时间</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 密码修改 -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">修改密码</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/settings/change-password}" method="post" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
                                <div class="invalid-feedback">请输入当前密码</div>
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="newPassword" name="newPassword" required>
                                <div class="invalid-feedback">请输入新密码</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                <div class="invalid-feedback">请确认新密码</div>
                            </div>
                            <button type="submit" class="btn btn-primary">保存密码</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 主题设置 -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">界面主题</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/settings/change-theme}" method="post">
                            <div class="mb-3">
                                <label for="themeStyle" class="form-label">选择主题</label>
                                <select class="form-select" id="themeStyle" name="themeStyle">
                                    <option th:each="theme : ${themeStyles}"
                                            th:value="${theme}"
                                            th:text="${theme.name() == 'DEFAULT' ? '默认主题' :
                                                     (theme.name() == 'DARK' ? '深色主题' :
                                                     (theme.name() == 'LIGHT' ? '浅色主题' : '蓝色主题'))}"
                                            th:selected="${user.themeStyle == theme}">
                                    </option>
                                </select>
                            </div>
                            <div class="theme-preview mb-3">
                                <div class="row">
                                    <div class="col-6 mb-2">
                                        <div class="card theme-card" id="theme-preview-card">
                                            <div class="card-header theme-preview-header">
                                                预览效果
                                            </div>
                                            <div class="card-body theme-preview-body">
                                                <p>这是主题预览效果</p>
                                                <button class="btn btn-primary btn-sm">按钮</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">保存主题</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 表单验证
        (function() {
            'use strict';

            var forms = document.querySelectorAll('.needs-validation');

            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }

                    form.classList.add('was-validated');
                }, false);
            });
        })();

        // 密码确认验证
        document.getElementById('confirmPassword').addEventListener('input', function() {
            var newPassword = document.getElementById('newPassword').value;
            var confirmPassword = this.value;

            if (newPassword !== confirmPassword) {
                this.setCustomValidity('密码不匹配');
            } else {
                this.setCustomValidity('');
            }
        });

        // 主题预览
        document.getElementById('themeStyle').addEventListener('change', function() {
            var themeCard = document.getElementById('theme-preview-card');
            var themeHeader = document.querySelector('.theme-preview-header');
            var themeBody = document.querySelector('.theme-preview-body');
            var themeButton = themeBody.querySelector('.btn-primary');

            // 重置样式
            themeCard.style.backgroundColor = '';
            themeCard.style.color = '';
            themeHeader.style.backgroundColor = '';
            themeHeader.style.color = '';
            themeBody.style.backgroundColor = '';
            themeBody.style.color = '';
            themeButton.style.backgroundColor = '';
            themeButton.style.borderColor = '';

            // 应用选中的主题样式
            switch(this.value) {
                case 'DARK':
                    themeCard.style.backgroundColor = '#343a40';
                    themeCard.style.color = '#fff';
                    themeHeader.style.backgroundColor = '#212529';
                    themeHeader.style.color = '#fff';
                    themeBody.style.backgroundColor = '#343a40';
                    themeBody.style.color = '#fff';
                    themeButton.style.backgroundColor = '#0d6efd';
                    themeButton.style.borderColor = '#0d6efd';
                    break;
                case 'LIGHT':
                    themeCard.style.backgroundColor = '#f8f9fa';
                    themeCard.style.color = '#212529';
                    themeHeader.style.backgroundColor = '#e9ecef';
                    themeHeader.style.color = '#212529';
                    themeBody.style.backgroundColor = '#f8f9fa';
                    themeBody.style.color = '#212529';
                    themeButton.style.backgroundColor = '#0d6efd';
                    themeButton.style.borderColor = '#0d6efd';
                    break;
                case 'BLUE':
                    themeCard.style.backgroundColor = '#f0f7ff';
                    themeCard.style.color = '#0a58ca';
                    themeHeader.style.backgroundColor = '#cfe2ff';
                    themeHeader.style.color = '#084298';
                    themeBody.style.backgroundColor = '#f0f7ff';
                    themeBody.style.color = '#0a58ca';
                    themeButton.style.backgroundColor = '#0a58ca';
                    themeButton.style.borderColor = '#084298';
                    break;
                default: // DEFAULT
                    // 使用默认样式
                    break;
            }
        });

        // 初始化主题预览
        document.getElementById('themeStyle').dispatchEvent(new Event('change'));
    </script>
</body>
</html>