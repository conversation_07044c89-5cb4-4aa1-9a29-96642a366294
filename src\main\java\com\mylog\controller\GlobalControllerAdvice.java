package com.mylog.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import com.mylog.config.CacheConfig;
import com.mylog.model.user.User;
import com.mylog.service.MessageService;
import com.mylog.service.ProjectService;
import com.mylog.service.TaskService;
import com.mylog.service.UserService;

@ControllerAdvice
public class GlobalControllerAdvice {

    @Autowired
    private MessageService messageService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private ProjectService projectService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取当前用户的未读消息数量
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("unreadMessageCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'unreadMessages_' + @globalControllerAdvice.getCurrentUsername()", cacheManager = "badgeCacheManager")
    public Long getUnreadMessageCount() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return messageService.countUnreadMessagesByReceiver(username);
            }
        } catch (Exception e) {
            // 如果获取未读消息数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 获取当前用户的主题设置
     */
    @ModelAttribute("userTheme")
    public String getUserTheme() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return userService.findUserByUsername(username)
                        .map(user -> "theme-" + user.getThemeStyle().name().toLowerCase())
                        .orElse("theme-default");
            }
        } catch (Exception e) {
            // 如果获取用户主题失败，使用默认主题
        }
        return "theme-default";
    }
    
    /**
     * 获取当前用户的进行中任务数量
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("inProgressTaskCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'inProgressTasks_' + @globalControllerAdvice.getCurrentUsername()", cacheManager = "badgeCacheManager")
    public Long getInProgressTaskCount() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return taskService.countTasksByResponsibleAndStatus(username, "进行中");
            }
        } catch (Exception e) {
            // 如果获取进行中任务数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 获取当前用户的进行中项目数量（我的项目）
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("myInProgressProjectCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'myInProgressProjects_' + @globalControllerAdvice.getCurrentUsername()", cacheManager = "badgeCacheManager")
    public Long getMyInProgressProjectCount() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return projectService.countProjectsByResponsibleAndStatus(username, "进行中");
            }
        } catch (Exception e) {
            // 如果获取进行中项目数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 获取所有进行中项目数量（项目管理）
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("inProgressProjectCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'inProgressProjects'", cacheManager = "badgeCacheManager")
    public Long getInProgressProjectCount() {
        try {
            return projectService.countProjectsByStatus("进行中");
        } catch (Exception e) {
            // 如果获取进行中项目数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 获取当前用户的进行中难点焦点任务数量
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("inProgressDifficultTaskCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'difficultTasks_' + @globalControllerAdvice.getCurrentUsername()", cacheManager = "badgeCacheManager")
    public Long getInProgressDifficultTaskCount() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return taskService.countInProgressDifficultTasksByResponsible(username);
            }
        } catch (Exception e) {
            // 如果获取进行中难点焦点任务数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 获取当前用户的进行中专项任务数量
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("inProgressSpecialTaskCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'specialTasks_' + @globalControllerAdvice.getCurrentUsername()", cacheManager = "badgeCacheManager")
    public Long getInProgressSpecialTaskCount() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                return taskService.countInProgressSpecialTasksByResponsible(username);
            }
        } catch (Exception e) {
            // 如果获取进行中专项任务数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 获取所有进行中任务数量（任务管理）
     * 添加5分钟缓存，减轻数据库压力
     */
    @ModelAttribute("allInProgressTaskCount")
    @Cacheable(value = CacheConfig.BADGE_CACHE, key = "'allInProgressTasks'", cacheManager = "badgeCacheManager")
    public Long getAllInProgressTaskCount() {
        try {
            return taskService.countTasksByStatus("进行中");
        } catch (Exception e) {
            // 如果获取进行中任务数量失败，不影响页面正常显示
        }
        return 0L;
    }
    
    /**
     * 辅助方法：获取当前用户名
     * 用于缓存键生成
     */
    public String getCurrentUsername() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
            return auth.getName();
        }
        return "anonymous";
    }
} 