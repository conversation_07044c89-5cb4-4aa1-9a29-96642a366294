package com.mylog.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.lang.NonNull;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.MediaType;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 标准时间格式
     */
    private static final DateTimeFormatter STANDARD_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private GlobalModelInterceptor globalModelInterceptor;

    @Autowired
    private WorkflowInterceptor workflowInterceptor;

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(globalModelInterceptor);
        registry.addInterceptor(workflowInterceptor);
    }
    
    @Override
    public void configurePathMatch(@NonNull PathMatchConfigurer configurer) {
        // 确保API路径优先匹配，避免被静态资源处理器拦截
        // configurer.setUseTrailingSlashMatch(true); // 已废弃，移除
    }    @Override
    public void configureMessageConverters(@NonNull List<HttpMessageConverter<?>> converters) {
        // 添加JSON消息转换器，确保能够处理异常情况
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        
        // 设置支持的媒体类型，包括通配符以确保在内容协商失败时也能工作
        jsonConverter.setSupportedMediaTypes(List.of(
            MediaType.APPLICATION_JSON,
            MediaType.TEXT_PLAIN,  // 添加对纯文本的支持
            new MediaType("application", "*+json"),  // 支持所有JSON相关类型
            MediaType.ALL  // 作为最后的选择
        ));
        
        converters.add(jsonConverter);
    }

    @Override
    public void addFormatters(@NonNull FormatterRegistry registry) {
        // 字符串到LocalDateTime的转换器
        registry.addConverter(new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(@NonNull String source) {
                if (source.trim().isEmpty()) {
                    return null;
                }
                try {
                    // 首先尝试标准格式
                    return LocalDateTime.parse(source, STANDARD_FORMATTER);
                } catch (Exception e) {
                    try {
                        // 如果失败，尝试ISO格式
                        return LocalDateTime.parse(source.replace(" ", "T"));
                    } catch (Exception ex) {
                        throw new IllegalArgumentException("无法解析时间格式: " + source, ex);
                    }
                }
            }
        });

        // LocalDateTime到字符串的转换器
        registry.addConverter(new Converter<LocalDateTime, String>() {
            @Override
            public String convert(@NonNull LocalDateTime source) {
                return source.format(STANDARD_FORMATTER);
            }
        });
    }
}