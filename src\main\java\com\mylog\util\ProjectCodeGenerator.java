package com.mylog.util;

import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class ProjectCodeGenerator {
    
    private static final String PREFIX = "PJ";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * 閻㈢喐鍨氭い鍦窗缂傛牜鐖?
     * @param sequence 瑜版挸銇夐惃鍕碍閸欏嚖绱欐禒?瀵偓婵绱?
     * @return 閺嶇厧绱℃稉?PJ######???閻ㄥ嫰銆嶉惄顔剧椽閻?
     */
    public String generateProjectCode(int sequence) {
        String date = LocalDateTime.now().format(DATE_FORMATTER);
        return String.format("%s%s%03d", PREFIX, date, sequence);
    }
} 
