<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head(${template != null && template.templateId == null ? '创建流程模板' : '编辑流程模板'})}">
    <meta charset="UTF-8">
    <title>流程模板表单</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${template != null && template.templateId == null ? '创建流程模板' : '编辑流程模板'}">流程模板表单</h1>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('templateForm').submit()">
                    <i class="bi bi-save"></i> 保存
                </button>
            </div>
        </div>

        <!-- 表单卡片 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">流程模板信息</h5>
                    </div>
                    <div class="card-body">
                        <form id="templateForm" th:action="@{/workflow/templates/save}" method="post" th:object="${template}" class="row g-3">
                            <!-- 隐藏字段 -->
                            <input type="hidden" th:field="*{templateId}">
                            
                            <!-- 模板名称 -->
                            <div class="col-md-6">
                                <label for="templateName" class="form-label">模板名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="templateName" th:field="*{templateName}" required>
                            </div>
                            
                            <!-- 模板标题 - 修改字段名从templateCode改为templateTitle -->
                            <div class="col-md-6">
                                <label for="templateTitle" class="form-label">模板标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="templateTitle" th:field="*{templateTitle}" required>
                                <small class="form-text text-muted" th:if="${template != null && template.templateId == null}">
                                    请输入模板标题
                                </small>
                            </div>
                            
                            <!-- 适用范围 -->
                            <div class="col-md-6">
                                <label for="applicableScope" class="form-label">适用范围</label>
                                <select class="form-select" id="applicableScope" th:field="*{applicableScope}">
                                    <option value="">请选择</option>
                                    <option value="项目">项目</option>
                                    <option value="任务">任务</option>
                                    <option value="通用">通用</option>
                                </select>
                            </div>
                            
                            <!-- 是否启用 -->
                            <div class="col-md-6">
                                <label for="enabled" class="form-label">状态</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="enabled" th:field="*{enabled}">
                                    <label class="form-check-label" for="enabled">启用</label>
                                </div>
                            </div>
                            
                            <!-- 描述 -->
                            <div class="col-md-12">
                                <label for="description" class="form-label">描述</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-end">
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('templateForm').submit()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表单验证
            const form = document.getElementById('templateForm');
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    </script>
</body>
</html>
