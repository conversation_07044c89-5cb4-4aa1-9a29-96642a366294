package com.mylog.model;

import lombok.Data;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

@Entity
@Table(name = "Projects")
@Data
public class Project {
    
    private static final Logger logger = LoggerFactory.getLogger(Project.class);
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long projectId;
    
    @Column(nullable = false)
    private String projectName;
    
    @Column(nullable = false, unique = true)
    private String projectCode;
    
    @Column(nullable = false)
    private String customerName;
    
    @Column(nullable = false)
    private String responsible;
    
    @Column(nullable = false)
    private String status;
    
    @Column(columnDefinition = "TEXT")
    private String visionType;
      @Column(columnDefinition = "varchar(10) default '正常'")
    private String risk = "正常";
    
    @Column(name = "project_type")
    private String projectType;
    
    private String salesOrderNumber;
    
    private String productPartNumber;
    
    private String salesResponsible;
    
    private String mechanicalResponsible;
    
    private String electricalResponsible;
      @Column
    private BigDecimal visionCost;
      @Column
    private BigDecimal visionCost2;
    
    @Column(name = "total_cost1")
    private BigDecimal totalCost1;
    
    @Column(name = "total_cost2")
    private BigDecimal totalCost2;    @Column
    private BigDecimal ratedDurationDays;
    
    @Column
    private BigDecimal actualDurationDays;
      
    @Column
    private BigDecimal difficultyCoefficient;
    
    @Column
    private BigDecimal cameraQuantity;
    
    @Column
    private BigDecimal durationDays;
    
    @Column
    private BigDecimal bonus;
    
    @Column(nullable = true)
    private Integer archive;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String plannedStartDate;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String plannedEndDate;
    
    @Column(columnDefinition = "TEXT")
    private String actualStartDate;
    
    @Column(columnDefinition = "TEXT")
    private String actualEndDate;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String createdDate;
    
    @Column(nullable = false)
    private String createdBy;
    
    @Column(columnDefinition = "TEXT")
    private String remarks;
    
    @Column(nullable = true)
    private Double quantity;
    
    @Column(columnDefinition = "TEXT")
    private String note;
    
    // 临时字段：用于在项目列表中传递任务额定工期总计，不存储到数据库
    @Transient
    private BigDecimal taskRatedDurationTotal;
    
    // 临时字段：用于在项目列表中传递暂停中任务名称，不存储到数据库
    @Transient
    private String pausedTaskNames;
    
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProjectTask> tasks;

    public Project() {
        LocalDateTime now = LocalDateTime.now();
        this.plannedStartDate = formatDateTime(now);
        this.plannedEndDate = formatDateTime(now.plusDays(200));
        this.createdDate = formatDateTime(now);
        this.tasks = new ArrayList<>();
    }
    
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, OUTPUT_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.error("日期格式错误: {}", e.getMessage());
            return null;
        }
    }
    
    private String formatDateTime(LocalDateTime dateTime) {
        try {
            return dateTime != null ? dateTime.format(OUTPUT_FORMATTER) : null;
        } catch (Exception e) {
            logger.error("格式化日期时间出错: {}", e.getMessage());
            return null;
        }
    }
    
    // PlannedStartDate getters and setters
    public String getPlannedStartDate() {
        return this.plannedStartDate;
    }
    
    public void setPlannedStartDate(String dateStr) {
        this.plannedStartDate = dateStr;
    }
    
    public LocalDateTime getPlannedStartDateTime() {
        return parseDateTime(this.plannedStartDate);
    }
    
    public void setPlannedStartDateTime(LocalDateTime dateTime) {
        this.plannedStartDate = formatDateTime(dateTime);
    }
    
    // PlannedEndDate getters and setters
    public String getPlannedEndDate() {
        return this.plannedEndDate;
    }
    
    public void setPlannedEndDate(String dateStr) {
        this.plannedEndDate = dateStr;
    }
    
    public LocalDateTime getPlannedEndDateTime() {
        return parseDateTime(this.plannedEndDate);
    }
    
    public void setPlannedEndDateTime(LocalDateTime dateTime) {
        this.plannedEndDate = formatDateTime(dateTime);
    }
    
    // ActualStartDate getters and setters
    public String getActualStartDate() {
        return this.actualStartDate;
    }
    
    public void setActualStartDate(String dateStr) {
        this.actualStartDate = dateStr;
    }
    
    public LocalDateTime getActualStartDateTime() {
        return parseDateTime(this.actualStartDate);
    }
    
    public void setActualStartDateTime(LocalDateTime dateTime) {
        this.actualStartDate = formatDateTime(dateTime);
    }
    
    // ActualEndDate getters and setters
    public String getActualEndDate() {
        return this.actualEndDate;
    }
    
    public void setActualEndDate(String dateStr) {
        this.actualEndDate = dateStr;
    }
    
    public LocalDateTime getActualEndDateTime() {
        return parseDateTime(this.actualEndDate);
    }
    
    public void setActualEndDateTime(LocalDateTime dateTime) {
        this.actualEndDate = formatDateTime(dateTime);
    }
    
    // CreatedDate getters and setters
    public String getCreatedDate() {
        return this.createdDate;
    }
    
    public void setCreatedDate(String dateStr) {
        this.createdDate = dateStr;
    }
    
    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.createdDate);
    }
    
    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.createdDate = formatDateTime(dateTime);
    }
    
    // Status getters and setters
    public String getStatus() {
        return this.status;
    }
    
    public void setStatus(String status) {
        if ("已完成".equals(this.status) && !"已完成".equals(status)) {
            this.actualEndDate = null;
            logger.debug("状态从已完成变更为{}，清空实际完成时间", status);
        }
        this.status = status;
    }

    @Transient
    public List<String> getVisionTypeList() {
        if (visionType == null || visionType.isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(visionType.split(","));
    }
    
    public void setVisionTypeList(List<String> visionTypeList) {
        if (visionTypeList == null || visionTypeList.isEmpty()) {
            this.visionType = "";
        } else {
            this.visionType = String.join(",", visionTypeList);
        }
    }
}