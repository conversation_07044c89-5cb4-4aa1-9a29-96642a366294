-- 创建奖罚记录表
CREATE TABLE IF NOT EXISTS RewardPenaltyRecord (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,          -- 姓名
    type TEXT NOT NULL,          -- 奖罚类型
    reason TEXT NOT NULL,        -- 奖罚事由
    remarks TEXT,                -- 备注
    create_time TEXT NOT NULL,   -- 创建时间，格式：2025-05-23 04:05:03
    occur_time TEXT NOT NULL,    -- 发生时间，格式：2025-05-23 04:05:03
    points INTEGER NOT NULL,     -- 积分，正数表示奖励，负数表示惩罚（变化积分）
    total_points INTEGER NOT NULL -- 存量积分（累计积分）
);

-- 为name字段创建索引，加速查询
CREATE INDEX IF NOT EXISTS idx_reward_penalty_name ON RewardPenaltyRecord (name);

-- 为type字段创建索引，方便按类型查询
CREATE INDEX IF NOT EXISTS idx_reward_penalty_type ON RewardPenaltyRecord (type);

-- 为create_time字段创建索引，方便按时间排序和查询
CREATE INDEX IF NOT EXISTS idx_reward_penalty_create_time ON RewardPenaltyRecord (create_time);
