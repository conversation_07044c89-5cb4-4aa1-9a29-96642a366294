package com.mylog.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public static PasswordEncoder passwordEncoder() {
        return NoOpPasswordEncoder.getInstance();
    }
    
    @Autowired
    private UserDetailsService userDetailsService;
    
    private final LoginSuccessHandler loginSuccessHandler;
    private final LogoutSuccessHandlerImpl logoutSuccessHandler;
    
    public SecurityConfig(LoginSuccessHandler loginSuccessHandler, LogoutSuccessHandlerImpl logoutSuccessHandler) {
        this.loginSuccessHandler = loginSuccessHandler;
        this.logoutSuccessHandler = logoutSuccessHandler;
    }
    
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/css/**", "/js/**", "/images/**", "/data/**").permitAll()
                .requestMatchers("/login", "/register").permitAll()
                .requestMatchers("/api/user/current").permitAll() // 允许获取当前用户信息用于前端验证
                .requestMatchers("/api/test/**").permitAll() // 允许测试API访问
                .requestMatchers("/api/files/check-directories", "/api/files/test-workflow-processing").permitAll() // 允许文件测试API访问
                .requestMatchers("/api/temp-files/**").permitAll() // 临时允许所有用户访问临时文件管理API（用于测试）
                .requestMatchers("/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .formLogin(form -> form
                .loginPage("/login")
                .loginProcessingUrl("/login")
                .usernameParameter("username")
                .passwordParameter("password")
                .successHandler(loginSuccessHandler)
                .permitAll()
            )
            .logout(logout -> logout
                .logoutSuccessHandler(logoutSuccessHandler)
                .permitAll()
            )
            .csrf(csrf -> csrf.disable());
        
        return http.build();
    }
} 