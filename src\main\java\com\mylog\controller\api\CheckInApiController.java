package com.mylog.controller.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.mylog.dto.EventCheckInDTO;
import com.mylog.service.EventCheckInService;
import com.mylog.service.UserService;
import com.mylog.util.ApiResponse;
import com.mylog.util.SecurityUtils;

import java.util.Optional;
import java.util.List;
import java.util.Map;

/**
 * 签到API控制器
 */
@RestController
@RequestMapping("/api/check-ins")
public class CheckInApiController {
    
    private static final Logger logger = LoggerFactory.getLogger(CheckInApiController.class);      @Autowired
    private EventCheckInService checkInService;
      @Autowired
    private UserService userService;
    
    /**
     * 根据用户名获取用户ID
     */
    private String getUserIdByUsername(String username) {
        return userService.findUserByUsername(username)
                .map(user -> String.valueOf(user.getUserId()))
                .orElseThrow(() -> new RuntimeException("用户不存在: " + username));
    }   
     /**
     * 获取用户待签到事件
     */
    @GetMapping("/pending")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<EventCheckInService.PendingCheckInDTO>>> getPendingCheckIns() {
        String username = SecurityUtils.getCurrentUsername();
        if (username == null) {
            return ResponseEntity.ok(ApiResponse.error("用户未登录"));
        }
        
        logger.info("获取用户 {} 的待签到事件", username);
        
        try {
            String userId = getUserIdByUsername(username);
            List<EventCheckInService.PendingCheckInDTO> pendingEvents = checkInService.getPendingCheckIns(userId);
            logger.info("找到 {} 个待签到事件", pendingEvents.size());
            return ResponseEntity.ok(ApiResponse.success(pendingEvents));
        } catch (Exception e) {
            logger.error("获取待签到事件失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取待签到事件失败: " + e.getMessage()));
        }
    }    /**
     * 获取用户签到统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<EventCheckInService.SimpleStatistics>> getCheckInStatistics() {
        String username = SecurityUtils.getCurrentUsername();
        if (username == null) {
            return ResponseEntity.ok(ApiResponse.error("用户未登录"));
        }
        
        logger.info("获取用户 {} 的签到统计信息", username);
        
        try {
            String userId = getUserIdByUsername(username);
            EventCheckInService.SimpleStatistics statistics = checkInService.getSimpleCheckInStatistics(userId);
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            logger.error("获取签到统计失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取签到统计失败: " + e.getMessage()));
        }
    }    /**
     * 获取用户签到历史
     */
    @GetMapping("/history")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<EventCheckInService.PagedCheckInHistory>> getCheckInHistory(
            @RequestParam(defaultValue = "all") String filter,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        String username = SecurityUtils.getCurrentUsername();
        if (username == null) {
            return ResponseEntity.ok(ApiResponse.error("用户未登录"));
        }
        
        logger.info("获取用户 {} 的签到历史, 过滤条件: {}, 页码: {}, 大小: {}", username, filter, page, size);
        
        try {
            String userId = getUserIdByUsername(username);
            EventCheckInService.PagedCheckInHistory history = checkInService.getPagedCheckInHistory(userId, filter, page, size);
            return ResponseEntity.ok(ApiResponse.success(history));
        } catch (Exception e) {
            logger.error("获取签到历史失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取签到历史失败: " + e.getMessage()));
        }
    }    /**
     * 执行签到（基于提醒ID）
     */
    @PostMapping("/reminder/{reminderId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<EventCheckInDTO>> performCheckIn(
            @PathVariable Long reminderId,
            @RequestBody Map<String, Object> checkInData) {
        
        String username = SecurityUtils.getCurrentUsername();
        if (username == null) {
            return ResponseEntity.ok(ApiResponse.error("用户未登录"));
        }
        
        logger.info("用户 {} 对提醒 {} 执行签到", username, reminderId);
        
        try {
            // 提取签到数据
            String notes = (String) checkInData.get("notes");
            Double latitude = null;
            Double longitude = null;
            
            if (checkInData.get("latitude") != null) {
                latitude = Double.valueOf(checkInData.get("latitude").toString());
            }
            if (checkInData.get("longitude") != null) {
                longitude = Double.valueOf(checkInData.get("longitude").toString());
            }
            
            String userId = getUserIdByUsername(username);
            
            // 使用支持备注和位置信息的签到方法
            EventCheckInDTO checkIn = checkInService.checkIn(reminderId, userId, notes, latitude, longitude);
            
            if (checkIn != null) {
                logger.info("签到成功: {}", checkIn.getId());
                return ResponseEntity.ok(ApiResponse.success(checkIn, "签到成功"));
            } else {
                return ResponseEntity.ok(ApiResponse.error("签到失败"));
            }
        } catch (Exception e) {
            logger.error("签到失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("签到失败: " + e.getMessage()));
        }
    }
      /**
     * 检查是否可以签到
     */
    @GetMapping("/reminder/{reminderId}/can-check-in")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Boolean>> canCheckIn(@PathVariable Long reminderId) {
        
        String username = SecurityUtils.getCurrentUsername();
        if (username == null) {
            return ResponseEntity.ok(ApiResponse.error("用户未登录"));
        }
        
        logger.info("检查用户 {} 是否可以对提醒 {} 签到", username, reminderId);
        
        try {
            String userId = getUserIdByUsername(username);
            boolean canCheckIn = checkInService.canCheckIn(reminderId, userId);
            return ResponseEntity.ok(ApiResponse.success(canCheckIn));
        } catch (Exception e) {
            logger.error("检查签到状态失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("检查签到状态失败: " + e.getMessage()));
        }
    }    /**
     * 获取提醒的签到记录
     */
    @GetMapping("/reminder/{reminderId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<EventCheckInDTO>> getCheckInRecord(@PathVariable Long reminderId) {
        
        String username = SecurityUtils.getCurrentUsername();
        if (username == null) {
            return ResponseEntity.ok(ApiResponse.error("用户未登录"));
        }
        
        logger.info("获取用户 {} 对提醒 {} 的签到记录", username, reminderId);
        
        try {
            String userId = getUserIdByUsername(username);
            Optional<EventCheckInDTO> checkIn = checkInService.getCheckInRecord(reminderId, userId);
            return ResponseEntity.ok(ApiResponse.success(checkIn.orElse(null)));
        } catch (Exception e) {
            logger.error("获取签到记录失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取签到记录失败: " + e.getMessage()));
        }    }
}