package com.mylog.config;

import org.hibernate.resource.jdbc.spi.StatementInspector;
import org.springframework.stereotype.Component;

/**
 * Hibernate SQL日志拦截器
 * 实现StatementInspector接口来拦截SQL语句，进一步确保SQL不会被记录
 */
@Component
public class HibernateSqlLogInterceptor implements StatementInspector {

    /**
     * 拦截SQL语句
     * 该方法会在Hibernate执行SQL语句前被调用，通过返回值可以修改或完全屏蔽SQL
     * 
     * @param sql 即将执行的SQL语句
     * @return 修改后的SQL语句，或原始SQL语句
     */
    @Override
    public String inspect(String sql) {
        // 这里我们直接返回原始SQL，不进行修改
        // 只是通过拦截确保上层配置中SQL日志被禁用
        return sql;
    }
}