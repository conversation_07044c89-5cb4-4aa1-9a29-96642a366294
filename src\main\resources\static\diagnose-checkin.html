<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签到系统诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .diagnostic-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .status-ok { border-color: #28a745; background-color: #d4edda; }
        .status-error { border-color: #dc3545; background-color: #f8d7da; }
        .status-warning { border-color: #ffc107; background-color: #fff3cd; }
        .test-result {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">签到系统诊断</h1>
        
        <div id="diagnostics">
            <!-- 诊断结果将在这里显示 -->
        </div>
        
        <button id="runDiagnostics" class="btn btn-primary">重新运行诊断</button>
    </div>

    <script>
        const currentUser = 'admin';
        
        async function runDiagnostics() {
            const diagnosticsDiv = document.getElementById('diagnostics');
            diagnosticsDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在运行诊断...</p></div>';
            
            const tests = [
                {
                    name: '1. 用户待签到事件API',
                    test: () => testAPI('/api/check-ins/user/admin/pending', 'GET')
                },
                {
                    name: '2. 用户签到统计API',
                    test: () => testAPI('/api/check-ins/user/admin/statistics', 'GET')
                },
                {
                    name: '3. 检查签到页面是否可访问',
                    test: () => testPageAccess('/check-in')
                },
                {
                    name: '4. 检查CheckInManager类',
                    test: () => testCheckInManager()
                },
                {
                    name: '5. 测试签到流程（如果有可签到事件）',
                    test: () => testCheckInFlow()
                }
            ];
            
            const results = [];
            for (const test of tests) {
                try {
                    const result = await test.test();
                    results.push({
                        name: test.name,
                        status: 'ok',
                        message: '测试通过',
                        details: result
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'error',
                        message: error.message,
                        details: error.stack || error.toString()
                    });
                }
            }
            
            displayResults(results);
        }
        
        async function testAPI(url, method = 'GET') {
            const response = await fetch(url, { method });
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return {
                status: response.status,
                success: data.success,
                dataCount: Array.isArray(data.data) ? data.data.length : (data.data ? 1 : 0),
                response: data
            };
        }
        
        async function testPageAccess(url) {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`页面无法访问: HTTP ${response.status}`);
            }
            const content = await response.text();
            const hasCurrentUserInfo = content.includes('currentUserInfo');
            const hasCheckInManager = content.includes('CheckInManager');
            
            return {
                status: response.status,
                hasCurrentUserInfo,
                hasCheckInManager,
                contentLength: content.length
            };
        }
        
        function testCheckInManager() {
            if (typeof CheckInManager === 'undefined') {
                // 检查是否在其他地方定义了
                const script = document.createElement('script');
                script.src = '/js/calendar/calendar.js';
                document.head.appendChild(script);
                
                return new Promise((resolve, reject) => {
                    script.onload = () => {
                        if (typeof CheckInManager !== 'undefined') {
                            const manager = new CheckInManager();
                            resolve({
                                available: true,
                                currentUser: manager.currentUser,
                                methods: Object.getOwnPropertyNames(CheckInManager.prototype)
                            });
                        } else {
                            reject(new Error('CheckInManager类未定义'));
                        }
                    };
                    script.onerror = () => reject(new Error('无法加载calendar.js'));
                });
            } else {
                const manager = new CheckInManager();
                return {
                    available: true,
                    currentUser: manager.currentUser,
                    methods: Object.getOwnPropertyNames(CheckInManager.prototype)
                };
            }
        }
        
        async function testCheckInFlow() {
            // 首先获取待签到事件
            const pendingResponse = await fetch('/api/check-ins/user/admin/pending');
            const pendingData = await pendingResponse.json();
            
            if (!pendingData.success || !pendingData.data || pendingData.data.length === 0) {
                return {
                    message: '没有待签到事件，跳过签到流程测试',
                    pendingCount: 0
                };
            }
            
            const firstEvent = pendingData.data[0];
            
            // 检查是否可以签到
            const canCheckInResponse = await fetch(`/api/check-ins/reminder/${firstEvent.reminderId}/user/admin/can-check-in`);
            const canCheckInData = await canCheckInResponse.json();
            
            return {
                pendingCount: pendingData.data.length,
                firstEventTitle: firstEvent.eventTitle,
                canCheckIn: canCheckInData.success ? canCheckInData.canCheckIn : false,
                withinWindow: canCheckInData.success ? canCheckInData.withinWindow : false
            };
        }
        
        function displayResults(results) {
            const diagnosticsDiv = document.getElementById('diagnostics');
            let html = '';
            
            for (const result of results) {
                const statusClass = result.status === 'ok' ? 'status-ok' : 
                                  result.status === 'warning' ? 'status-warning' : 'status-error';
                
                html += `
                    <div class="diagnostic-item ${statusClass}">
                        <h5>${result.name}</h5>
                        <p><strong>状态:</strong> ${result.status.toUpperCase()}</p>
                        <p><strong>消息:</strong> ${result.message}</p>
                        <details>
                            <summary>详细信息</summary>
                            <div class="test-result">
                                <pre>${JSON.stringify(result.details, null, 2)}</pre>
                            </div>
                        </details>
                    </div>
                `;
            }
            
            diagnosticsDiv.innerHTML = html;
        }
        
        // 页面加载时自动运行诊断
        document.addEventListener('DOMContentLoaded', runDiagnostics);
        
        // 绑定重新运行按钮
        document.getElementById('runDiagnostics').addEventListener('click', runDiagnostics);
    </script>
</body>
</html>
