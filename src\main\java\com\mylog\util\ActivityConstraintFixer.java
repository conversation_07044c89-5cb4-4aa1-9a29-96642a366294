package com.mylog.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.ResultSet;

/**
 * 修复用户活动日志表的活动类型约束工具类
 */
public class ActivityConstraintFixer {
    
    private static final String DB_URL = "*********************************";
    
    public static void main(String[] args) {
        System.out.println("开始修复用户活动日志表的活动类型约束...");
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // 检查当前表结构
            System.out.println("检查当前表结构...");
            
            // 执行修复操作
            fixActivityTypeConstraint(conn);
            
            // 验证修复结果
            verifyFix(conn);
            
            System.out.println("修复操作完成！");
            
        } catch (Exception e) {
            System.err.println("修复过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void fixActivityTypeConstraint(Connection conn) throws Exception {
        System.out.println("开始修复约束...");
        
        Statement stmt = conn.createStatement();
        
        // 禁用外键约束
        stmt.execute("PRAGMA foreign_keys=off");
        
        // 开始事务
        stmt.execute("BEGIN TRANSACTION");
        
        try {
            // 创建临时表，具有正确的CHECK约束
            String createTempTable = """
                CREATE TABLE user_activity_logs_temp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    userId INTEGER NOT NULL,
                    username TEXT NOT NULL,
                    activityType TEXT NOT NULL CHECK (activityType IN ('LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'DOWNLOAD', 'EXPORT', 'SETTINGS_CHANGE', 'OTHER')),
                    description TEXT NOT NULL,
                    ipAddress TEXT,
                    entityType TEXT,
                    entityId INTEGER,
                    accessType TEXT,
                    createdDate TEXT NOT NULL
                )
                """;
            stmt.execute(createTempTable);
            System.out.println("创建临时表成功");
            
            // 将原表数据复制到临时表
            String copyData = """
                INSERT INTO user_activity_logs_temp (id, userId, username, activityType, description, ipAddress, entityType, entityId, accessType, createdDate)
                SELECT id, userId, username, activityType, description, ipAddress, entityType, entityId, accessType, createdDate
                FROM user_activity_logs
                """;
            stmt.execute(copyData);
            System.out.println("数据复制成功");
            
            // 删除原表
            stmt.execute("DROP TABLE user_activity_logs");
            System.out.println("删除原表成功");
            
            // 重命名临时表为原表名
            stmt.execute("ALTER TABLE user_activity_logs_temp RENAME TO user_activity_logs");
            System.out.println("重命名表成功");
            
            // 提交事务
            stmt.execute("COMMIT");
            
        } catch (Exception e) {
            // 回滚事务
            stmt.execute("ROLLBACK");
            throw e;
        } finally {
            // 重新启用外键约束
            stmt.execute("PRAGMA foreign_keys=on");
            stmt.close();
        }
    }
    
    private static void verifyFix(Connection conn) throws Exception {
        System.out.println("验证修复结果...");
        
        Statement stmt = conn.createStatement();
        
        // 查询所有不同的活动类型
        ResultSet rs = stmt.executeQuery("SELECT DISTINCT activityType FROM user_activity_logs");
        System.out.println("当前数据库中的活动类型:");
        while (rs.next()) {
            System.out.println("- " + rs.getString("activityType"));
        }
        rs.close();
        
        // 测试插入DOWNLOAD类型记录
        try {
            stmt.execute("INSERT INTO user_activity_logs (userId, username, activityType, description, createdDate) " +
                        "VALUES (1, 'test_user', 'DOWNLOAD', '测试下载操作约束修复', datetime('now', 'localtime'))");
            System.out.println("✓ DOWNLOAD类型约束修复成功！");
            
            // 删除测试记录
            stmt.execute("DELETE FROM user_activity_logs WHERE description = '测试下载操作约束修复'");
            System.out.println("✓ 测试记录已清理");
            
        } catch (Exception e) {
            System.err.println("✗ DOWNLOAD类型约束修复失败: " + e.getMessage());
        }
        
        stmt.close();
    }
}
