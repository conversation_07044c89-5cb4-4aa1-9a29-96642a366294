-- 添加累计工期和剩余工期字段到Tasks表
-- 执行日期: 2025年7月28日

-- 添加累计工期字段
ALTER TABLE Tasks ADD COLUMN cumulative_duration_days DECIMAL(10,2) DEFAULT 0.00;

-- 添加剩余工期字段  
ALTER TABLE Tasks ADD COLUMN remaining_duration_days DECIMAL(10,2) DEFAULT 0.00;

-- 为新字段添加注释
COMMENT ON COLUMN Tasks.cumulative_duration_days IS '累计工期（天）';
COMMENT ON COLUMN Tasks.remaining_duration_days IS '剩余工期（天）';

-- 初始化现有任务的工期字段
-- 这里可以选择运行一个初始化脚本来计算现有任务的累计工期和剩余工期
-- 或者依赖应用程序在下次保存任务时自动计算

-- 提示：执行此脚本后，需要重启应用程序以确保新字段映射生效
