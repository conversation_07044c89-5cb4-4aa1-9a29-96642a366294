package com.mylog.controller;

import com.mylog.model.Submit2;
import com.mylog.model.ProjectTask;
import com.mylog.model.Project;
import com.mylog.model.SubTask;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.service.Submit2Service;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.ProjectService;
import com.mylog.service.SubTaskService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.util.SubmitFileUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
@RequestMapping("/submits2")
public class Submit2Controller extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(Submit2Controller.class);

    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private TaskService taskService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private WorkflowInstanceService workflowInstanceService;

    @Value("${mylog.data.path:data}")
    private String dataPath;

    @PostMapping("/save")
    public String saveSubmit(
            @RequestParam("taskId") Long taskId,
            @RequestParam("remarks") String remarks,
            @RequestParam(value = "submitName", defaultValue = "提交方案书和规格书") String submitName,
            @RequestParam(value = "file1", required = false) MultipartFile file1,
            @RequestParam(value = "file2", required = false) MultipartFile file2,
            RedirectAttributes redirectAttributes) {

        logger.info("接收到任务提交请求: taskId={}", taskId);

        // 验证备注是否为空
        if (remarks == null || remarks.trim().isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "提交备注不能为空");
            return "redirect:/tasks/" + taskId;
        }

        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        // 检查任务是否存在
        Optional<ProjectTask> taskOpt = taskService.findTaskById(taskId);
        if (!taskOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "无法找到指定任务");
            return "redirect:/tasks/" + taskId;
        }

        ProjectTask task = taskOpt.get();

        // 获取项目信息
        String projectCode = "unknown";
        String projectName = "未知项目";
        try {
            // 首先尝试从task中获取project
            Project project = task.getProject();
            if (project != null) {
                projectCode = project.getProjectCode();
                projectName = project.getProjectName();
            } else {
                // 如果延迟加载失败，则直接从数据库查询项目信息
                Long projectId = task.getProjectId();
                Optional<Project> projectOpt = projectService.findProjectById(projectId);
                if (projectOpt.isPresent()) {
                    projectCode = projectOpt.get().getProjectCode();
                    projectName = projectOpt.get().getProjectName();
                }
            }
        } catch (Exception e) {
            logger.warn("获取项目信息时出错: {}", e.getMessage());
            // 继续使用默认值
        }

        // 如果提交名称使用的是旧的默认值，则替换为任务名称
        if ("提交方案书和规格书".equals(submitName)) {
            submitName = task.getTaskName();
        }

        String taskName = task.getTaskName();

        Submit2 submit = new Submit2();
        submit.setTaskId(taskId);
        submit.setRemarks(remarks);
        submit.setSubmitName(submitName);
        submit.setSubmitter(currentUsername);
        submit.setSubmitDateTime(LocalDateTime.now());

        // 根据任务名称确定提交目录
        String submitDir = SubmitFileUtils.determineSubmitDirectory(submitName);

        // 创建提交文件目录
        Path submitDirPath = Paths.get(dataPath, submitDir);
        try {
            if (!Files.exists(submitDirPath)) {
                Files.createDirectories(submitDirPath);
                logger.info("创建提交文件目录: {}", submitDirPath);
            }

            // 处理文件1
            if (file1 != null && !file1.isEmpty()) {
                String originalFilename = file1.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }

                // 根据格式创建新文件名
                Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                        projectCode, projectName, taskName,
                        1, fileExtension);
                Files.copy(file1.getInputStream(), filePath);
                submit.setFilePath1(filePath.toString());
                logger.info("保存提交文件1: {}", filePath);
            }

            // 处理文件2
            if (file2 != null && !file2.isEmpty()) {
                String originalFilename = file2.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }

                // 根据格式创建新文件名
                Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                        projectCode, projectName, taskName,
                        2, fileExtension);
                Files.copy(file2.getInputStream(), filePath);
                submit.setFilePath2(filePath.toString());
                logger.info("保存提交文件2: {}", filePath);
            }

            // 保存提交记录
            Submit2 savedSubmit = submit2Service.saveSubmit(submit);
            logger.info("成功保存提交记录: {}", savedSubmit.getSubmitId());

            // 如果有备注，自动创建一条评论
            if (remarks != null && !remarks.trim().isEmpty()) {
                SubTask commentTask = new SubTask();
                commentTask.setTaskId(taskId);
                commentTask.setLogContent("提交备注：" + remarks);
                commentTask.setCreatedDateTime(LocalDateTime.now());
                commentTask.setCreatedBy(currentUsername);

                // 保存评论
                subTaskService.saveSubTask(commentTask);
                logger.info("已添加提交备注作为评论");
            }
            // 完成任务
            try {
                taskService.completeTask(taskId);
                logger.info("已将任务 {} 标记为已完成", taskId);
            } catch (Exception e) {
                logger.error("完成任务时出错: {}", e.getMessage());
                // 提交记录已保存，即使任务状态更新失败也继续执行
            }

            // 记录活动
            String ipAddress = getClientIpAddress();

            // 获取当前用户ID
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logCreate(
                    userId, // 传入用户ID而不是null
                    currentUsername,
                    "为任务 " + task.getTaskName() + " 添加了提交记录",
                    ipAddress,
                    "Task",
                    task.getTaskId(),
                    getAccessType());

            redirectAttributes.addFlashAttribute("message", "提交记录已保存");

        } catch (IOException e) {
            logger.error("保存提交文件时出错", e);
            redirectAttributes.addFlashAttribute("error", "提交记录保存失败: " + e.getMessage());
        }

        return "redirect:/tasks/" + taskId;
    }

    @PostMapping("/delete")
    public String deleteSubmit(
            @RequestParam("submitId") Long submitId,
            @RequestParam("taskId") Long taskId,
            RedirectAttributes redirectAttributes) {

        logger.info("接收到删除提交请求: submitId={}", submitId);
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));

        // 权限检查：只有管理员可以删除
        if (!isAdmin) {
            redirectAttributes.addFlashAttribute("error", "只有管理员才能删除提交记录");
            return "redirect:/tasks/" + taskId;
        }

        // 获取提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "找不到指定的提交记录");
            return "redirect:/tasks/" + taskId;
        }

        Submit2 submit = submitOpt.get();

        try {
            // 删除文件
            if (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) {
                Path filePath = Paths.get(submit.getFilePath1());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    logger.info("删除提交文件1: {}", filePath);
                }
            }

            if (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) {
                Path filePath = Paths.get(submit.getFilePath2());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    logger.info("删除提交文件2: {}", filePath);
                }
            }

            // 删除提交记录
            submit2Service.deleteSubmit(submitId);
            logger.info("成功删除提交记录: {}", submitId);

            // 记录活动
            String ipAddress = getClientIpAddress();

            // 获取当前用户ID
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logDelete(
                    userId, // 传入用户ID而不是null
                    currentUsername,
                    "为任务 " + taskId + " 删除了提交记录",
                    ipAddress,
                    "Task",
                    taskId,
                    getAccessType());

            redirectAttributes.addFlashAttribute("message", "提交记录已删除");

        } catch (IOException e) {
            logger.error("删除提交文件时出错", e);
            redirectAttributes.addFlashAttribute("error", "删除提交记录失败: " + e.getMessage());
        }

        return "redirect:/tasks/" + taskId;
    }

    /**
     * 检查文件下载权限
     */
    @GetMapping("/check/{id}")
    public org.springframework.http.ResponseEntity<String> checkDownloadPermission(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {

        logger.info("检查文件下载权限: submitId={}, fileNumber={}", submitId, fileNumber);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        if (currentUsername == null) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.UNAUTHORIZED)
                    .body("用户未登录");
        }

        // 查找提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("提交记录不存在");
        }

        Submit2 submit = submitOpt.get();

        // 权限检查：只允许管理员和经理下载文件（与原下载方法保持一致）
        if (!isAdmin && !isManager) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.FORBIDDEN)
                    .body("您没有权限下载此文件，请联系管理员或文件提交者获取授权。");
        }

        // 检查文件是否存在
        String filename = null;
        switch (fileNumber) {
            case 1: filename = submit.getFilePath1(); break;
            case 2: filename = submit.getFilePath2(); break;
            default:
                return org.springframework.http.ResponseEntity
                        .status(org.springframework.http.HttpStatus.BAD_REQUEST)
                        .body("无效的文件编号");
        }

        if (filename == null || filename.trim().isEmpty()) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("文件不存在");
        }

        // 检查文件路径
        Path filePath = Paths.get(filename);  // 直接使用数据库中的完整路径
        if (!Files.exists(filePath)) {
            return org.springframework.http.ResponseEntity
                    .status(org.springframework.http.HttpStatus.NOT_FOUND)
                    .body("文件不存在或已被移动：" + filename);
        }

        return org.springframework.http.ResponseEntity.ok("权限检查通过");
    }

    @GetMapping("/download/{id}")
    public org.springframework.http.ResponseEntity<byte[]> downloadFile(
            @PathVariable("id") Long submitId,
            @RequestParam("file") int fileNumber) {

        logger.info("接收到文件下载请求: submitId={}, fileNumber={}", submitId, fileNumber);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 获取提交记录
        Optional<Submit2> submitOpt = submit2Service.findSubmitById(submitId);
        if (!submitOpt.isPresent()) {
            return org.springframework.http.ResponseEntity.notFound().build();
        }

        Submit2 submit = submitOpt.get();

        // 检查是否为当前审批人
        boolean isCurrentApprover = false;

        // 获取与该提交关联的任务ID
        Long taskId = submit.getTaskId();
        if (taskId != null) {
            // 查找与该任务关联的工作流实例
            List<WorkflowInstance> instances = workflowInstanceService.findInstancesByBusiness("任务", taskId);

            // 检查当前用户是否为任一工作流实例的当前审批人
            for (WorkflowInstance instance : instances) {
                if (currentUsername.equals(instance.getCurrentApprover())) {
                    isCurrentApprover = true;
                    logger.info("用户 {} 是任务 {} 的当前审批人，允许下载文件", currentUsername, taskId);
                    break;
                }
            }
        }

        // 权限检查：允许管理员、提交者本人和当前审批人下载
        // if (!isAdmin && !currentUsername.equals(submit.getSubmitter()) &&
        // !isCurrentApprover) {

        if (!isAdmin && !isManager) {// 只允许管理员和经理下载文件
            logger.warn("用户 {} 无权下载提交 {} 的文件", currentUsername, submitId);

            // 返回更优雅的提示信息而不是403状态码
            byte[] messageBytes = "您没有权限下载此文件，请联系管理员或文件提交者获取授权。".getBytes();
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
            headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(messageBytes.length)
                    .body(messageBytes);
        }

        // 获取文件路径
        String filePath = fileNumber == 1 ? submit.getFilePath1() : submit.getFilePath2();
        if (filePath == null || filePath.isEmpty()) {
            logger.warn("提交记录 {} 的文件{} 路径为空", submitId, fileNumber);
            return org.springframework.http.ResponseEntity.notFound().build();
        }

        try {
            // 读取文件并返回
            Path path = Paths.get(filePath);
            
            // 检查文件是否存在
            if (!Files.exists(path)) {
                logger.error("文件不存在: {}", path.toAbsolutePath());
                
                // 尝试使用相对于数据目录的路径
                Path relativePath = Paths.get(dataPath, filePath);
                if (Files.exists(relativePath)) {
                    logger.info("在数据目录中找到文件: {}", relativePath.toAbsolutePath());
                    path = relativePath;
                } else {
                    logger.error("在数据目录中也未找到文件: {}", relativePath.toAbsolutePath());
                    
                    // 返回友好的错误信息
                    String errorMessage = String.format("文件不存在或已被移动：%s", path.getFileName().toString());
                    byte[] messageBytes = errorMessage.getBytes("UTF-8");
                    org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                    headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
                    headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

                    return org.springframework.http.ResponseEntity
                            .ok()
                            .headers(headers)
                            .contentLength(messageBytes.length)
                            .body(messageBytes);
                }
            }

            // 从文件路径中提取原始文件名，格式为：项目编号_项目名称_任务名称_序号.扩展名
            String filename = path.getFileName().toString();
            String[] parts = filename.split("_");
            if (parts.length >= 4) {
                // 使用正确的项目编号_项目名称_任务名称格式
                filename = String.join("_", parts);
            }

            byte[] data = Files.readAllBytes(path);
            logger.info("成功读取文件: {}, 大小: {} bytes", path.toAbsolutePath(), data.length);

            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            // 处理文件名中可能包含的中文字符，使用标准的RFC 5987编码
            String encodedFilename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            // 为了兼容性，filename部分也使用编码后的文件名，避免HTTP头中的非ASCII字符
            headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);

            // 记录下载活动日志
            try {
                // 获取当前用户ID
                Long userId = null;
                Optional<com.mylog.model.user.User> userOpt = userService.findUserByUsername(currentUsername);
                if (userOpt.isPresent()) {
                    userId = userOpt.get().getUserId();
                }

                // 构建下载描述信息
                String description = String.format("下载了提交记录 %d 的文件%d：%s",
                        submitId, fileNumber, path.getFileName().toString());

                // 记录下载活动日志
                activityLogService.logDownload(
                        userId,
                        currentUsername,
                        description,
                        getClientIpAddress(),
                        "Task",
                        taskId,
                        getAccessType());

                logger.info("用户 {} 下载了提交记录 {} 的文件{}：{}", 
                           currentUsername, submitId, fileNumber, path.getFileName().toString());

            } catch (Exception logException) {
                // 日志记录失败不影响文件下载
                logger.error("记录下载活动日志时出错: {}", logException.getMessage(), logException);
            }

            return org.springframework.http.ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(data.length)
                    .contentType(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM)
                    .body(data);

        } catch (IOException e) {
            logger.error("下载文件时出错: {}", e.getMessage(), e);
            
            // 为特定类型的IO异常提供更友好的错误信息
            String errorMessage;
            if (e instanceof java.nio.file.NoSuchFileException) {
                errorMessage = "文件不存在或已被移动，请联系管理员检查文件状态。";
            } else if (e instanceof java.nio.file.AccessDeniedException) {
                errorMessage = "无法访问文件，可能文件正在被其他程序使用。";
            } else {
                errorMessage = "下载文件时发生错误：" + e.getMessage();
            }
            
            // 返回友好的错误信息而不是HTTP 500错误
            try {
                byte[] messageBytes = errorMessage.getBytes("UTF-8");
                org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
                headers.add(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION, "inline");
                headers.add(org.springframework.http.HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");

                return org.springframework.http.ResponseEntity
                        .ok()
                        .headers(headers)
                        .contentLength(messageBytes.length)
                        .body(messageBytes);
            } catch (Exception encodeException) {
                logger.error("编码错误信息时出错", encodeException);
                return org.springframework.http.ResponseEntity
                        .status(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }
    }
}