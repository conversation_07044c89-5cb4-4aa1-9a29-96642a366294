package com.mylog.model.workflow;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 审批记录实体类
 * 记录每个步骤的审批情况
 */
@Entity
@Table(name = "approval_records")
@Data
public class ApprovalRecord {
    
    private static final Logger logger = LoggerFactory.getLogger(ApprovalRecord.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 审批操作类型枚举
     */
    public enum ApprovalAction {
        SUBMIT,     // 提交
        APPROVE,    // 同意
        REJECT,     // 拒绝
        TRANSFER,   // 转交
        WITHDRAW,   // 撤回
        TERMINATE,  // 终止
        COMMENT     // 评论
    }
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long recordId;
    
    /**
     * 所属流程实例
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "instance_id", nullable = false)
    private WorkflowInstance instance;
    
    /**
     * 所属流程步骤
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "step_id")
    private WorkflowStep step;
    
    /**
     * 审批操作类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ApprovalAction action;
    
    /**
     * 审批人
     */
    @Column(nullable = false, length = 50)
    private String approver;
    
    /**
     * 下一步审批人
     * 存储指定的下一步审批人用户名
     */
    @Column(length = 50)
    private String nextApprover;
    
    /**
     * 审批意见
     */
    @Column(columnDefinition = "TEXT")
    private String comment;
    
    /**
     * 附件路径
     * 多个附件路径用分号分隔
     */
    @Column(columnDefinition = "TEXT")
    private String attachments;
    
    /**
     * 创建时间
     */
    @Column(nullable = false)
    private String createdDate;
    
    /**
     * 设置创建时间
     */
    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.createdDate = formatDateTime(dateTime);
    }
    
    /**
     * 获取创建时间
     */
    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.createdDate);
    }
    
    /**
     * 解析日期时间字符串
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (Exception e) {
            logger.error("解析日期时间字符串失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }
}
