<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('已审批任务')}">
    <meta charset="UTF-8">
    <title>已审批任务</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">已审批任务</h1>
            <div>
                <a th:href="@{/workflow/my}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
            </div>
        </div>

        <!-- 已审批任务列表 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">已审批任务列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 成功/错误消息 -->
                        <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <span th:text="${message}">操作成功</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                        <!-- 已审批任务表格 -->
                        <div class="table-responsive" th:if="${!donePage.empty}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>流程标题</th>
                                        <th>流程模板</th>
                                        <th>发起人</th>
                                        <th>提交时间</th>
                                        <th>状态</th>
                                        <th>业务类型</th>
                                        <th>业务ID</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${donePage.content}">
                                        <td th:text="${task.title}">流程标题</td>
                                        <td th:text="${task.template != null ? task.template.templateName : '未知模板'}">流程模板</td>
                                        <td th:text="${task.initiator}">发起人</td>
                                        <td th:text="${task.submittedDateTime != null ? #temporals.format(task.submittedDateTime, 'yyyy-MM-dd HH:mm') : '-'}">提交时间</td>
                                        <td>
                                            <span th:if="${task.status != null && task.status.name() == 'DRAFT'}" class="badge bg-secondary">草稿</span>
                                            <span th:if="${task.status != null && task.status.name() == 'PROCESSING'}" class="badge bg-primary">处理中</span>
                                            <span th:if="${task.status != null && task.status.name() == 'APPROVED'}" class="badge bg-success">已批准</span>
                                            <span th:if="${task.status != null && task.status.name() == 'REJECTED'}" class="badge bg-danger">已拒绝</span>
                                            <span th:if="${task.status != null && task.status.name() == 'CANCELED'}" class="badge bg-warning">已取消</span>
                                            <span th:if="${task.status != null && task.status.name() == 'TERMINATED'}" class="badge bg-dark">已终止</span>
                                            <span th:if="${task.status == null}" class="badge bg-secondary">未知状态</span>
                                        </td>
                                        <td th:text="${task.businessType != null ? task.businessType : '-'}">业务类型</td>
                                        <td>
                                            <!-- 如果业务类型为任务，则显示为任务链接 -->
                                            <a th:if="${task.businessId != null && task.businessType == '任务'}" 
                                               th:href="@{/tasks/{id}(id=${task.businessId})}"
                                               th:text="${task.businessId}">123</a>
                                            <!-- 如果业务类型不是任务或为空，则显示普通文本 -->
                                            <span th:if="${task.businessId != null && task.businessType != '任务'}" 
                                                  th:text="${task.businessId}">123</span>
                                            <span th:unless="${task.businessId != null}">-</span>
                                        </td>
                                        <td>
                                            <a th:href="@{/workflow/instances/{id}(id=${task.instanceId})}" class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-eye"></i> 查看
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center py-3" th:if="${donePage.empty}">
                            <p class="text-muted mb-0">暂无已审批任务</p>
                        </div>
                    </div>
                    <!-- 分页控件 -->
                    <div class="card-footer" th:if="${donePage.totalPages > 0}">
                        <div th:replace="~{fragments/pagination :: pagination(${donePage}, @{/workflow/instances/done})}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
