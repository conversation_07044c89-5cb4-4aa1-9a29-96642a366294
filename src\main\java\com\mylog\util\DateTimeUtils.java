package com.mylog.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 日期时间工具类，提供统一的日期时间处理方法
 */
public class DateTimeUtils {
    
    // 中国时区 UTC+8
    public static final ZoneId CHINA_ZONE = ZoneId.of("Asia/Shanghai");
    
    // 标准日期时间格式
    public static final DateTimeFormatter STANDARD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 获取当前中国时区的LocalDateTime
     * @return 当前中国时区时间
     */
    public static LocalDateTime nowInChina() {
        return ZonedDateTime.now(CHINA_ZONE).toLocalDateTime();
    }
    
    /**
     * 解析日期时间字符串为LocalDateTime对象
     * 只接受标准格式 "yyyy-MM-dd HH:mm:ss"
     * @param dateStr 日期时间字符串
     * @return 解析后的LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 尝试标准格式解析
            return LocalDateTime.parse(dateStr, STANDARD_FORMATTER);
        } catch (DateTimeParseException e) {
            // 解析失败
            return null;
        }
    }
    
    /**
     * 将LocalDateTime格式化为标准格式字符串
     * @param dateTime 日期时间对象
     * @return 格式化后的字符串，如果输入为null则返回null
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(STANDARD_FORMATTER) : null;
    }
    
    /**
     * 将ISO格式（如HTML datetime-local控件的值）转换为标准格式
     * @param isoDateTime ISO格式日期时间字符串，如 "yyyy-MM-ddTHH:mm"
     * @return 标准格式字符串，如果转换失败则返回原字符串
     */
    public static String convertIsoToStandard(String isoDateTime) {
        if (isoDateTime == null || isoDateTime.trim().isEmpty()) {
            return null;
        }
        
        try {
            if (isoDateTime.contains("T")) {
                // ISO格式: yyyy-MM-ddThh:mm
                LocalDateTime dateTime = LocalDateTime.parse(isoDateTime);
                return dateTime.format(STANDARD_FORMATTER);
            } else {
                // 可能已经是标准格式
                return isoDateTime;
            }
        } catch (DateTimeParseException e) {
            // 解析失败，返回原字符串
            return isoDateTime;
        }
    }
}
