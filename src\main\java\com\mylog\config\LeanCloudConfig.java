package com.mylog.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;

@Configuration
@Profile("leancloud")
public class LeanCloudConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(LeanCloudConfig.class);
    
    @PostConstruct
    public void init() {
        // 获取 LeanCloud 应用根目录
        String appRoot = System.getenv("LEANCLOUD_APP_ROOT");
        if (appRoot == null) {
            appRoot = ".";
        }
        
        // 确保数据目录存在
        File dataDir = new File(appRoot, "data");
        if (!dataDir.exists()) {
            dataDir.mkdirs();
            logger.info("Created data directory at: {}", dataDir.getAbsolutePath());
        }
        
        // 设置系统属性
        System.setProperty("user.timezone", "Asia/Shanghai");
        
        logger.info("LeanCloud configuration initialized");
        logger.info("App root: {}", appRoot);
        logger.info("Data directory: {}", dataDir.getAbsolutePath());
    }
} 