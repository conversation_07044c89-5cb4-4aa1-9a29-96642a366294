package com.mylog.controller;

// 项目内部类
import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.service.ProjectService;
import com.mylog.service.TaskService;

// 第三方库
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;

// Spring框架类
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.lang.reflect.Field;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminImportController {

    private static final Logger logger = LoggerFactory.getLogger(AdminImportController.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter[] DATE_PARSERS = new DateTimeFormatter[] {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/M/d HH:mm"),
            DateTimeFormatter.ofPattern("yyyy/M/dd HH:mm"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd")
    };

    @Autowired
    private ProjectService projectService;

    @Autowired
    private TaskService taskService;

    @PostMapping("/import")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> importData(@RequestParam("file") MultipartFile file,
            @RequestParam("tableName") String tableName,
            @RequestParam(value = "clearExisting", defaultValue = "false") boolean clearExisting,
            @RequestParam(value = "autoProjectOffset", defaultValue = "true") boolean autoProjectOffset) {
        logger.info("开始导入数据 - 表名: {}, 文件名: {}, 清空现有数据: {}, 自动获取项目ID偏移量: {}",
                tableName, file.getOriginalFilename(), clearExisting, autoProjectOffset);
        Map<String, Object> response = new HashMap<>();

        try {
            // 读取CSV文件
            logger.info("开始读取CSV文件");
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8));
            CSVReader csvReader = new CSVReaderBuilder(reader)
                    .withSkipLines(0)
                    .build();

            // 读取表头和数据
            List<String[]> allData = csvReader.readAll();
            if (allData.isEmpty()) {
                logger.warn("CSV文件为空");
                response.put("success", false);
                response.put("message", "CSV文件为空");
                return ResponseEntity.ok(response);
            }

            logger.info("CSV文件读取成功，共 {} 行数据", allData.size());

            // 获取表头
            String[] headers = allData.get(0);
            logger.info("CSV表头: {}", String.join(", ", headers));

            // 获取数据行
            List<String[]> dataRows = allData.subList(1, allData.size());
            logger.info("数据行数: {}", dataRows.size());

            // 根据表名选择处理方法
            logger.info("开始处理数据，表名: {}", tableName);
            Map<String, Object> result;
            switch (tableName) {
                case "projects":
                    result = handleProjectImport(headers, dataRows, clearExisting);
                    break;
                case "tasks":
                    result = handleTaskImport(headers, dataRows, clearExisting, autoProjectOffset);
                    break;
                default:
                    logger.warn("不支持的表名: {}", tableName);
                    response.put("success", false);
                    response.put("message", "不支持的表名：" + tableName);
                    return ResponseEntity.ok(response);
            }

            logger.info("导入处理完成，结果: {}", result);
            // 确保result中包含success字段
            if (!result.containsKey("success")) {
                logger.warn("处理结果中缺少success字段，添加默认值false");
                result.put("success", false);
            }
            if (!result.containsKey("message")) {
                logger.warn("处理结果中缺少message字段，添加默认消息");
                result.put("message", "处理完成但未返回具体消息");
            }
            logger.info("最终返回结果: success={}, message={}", result.get("success"), result.get("message"));

            return ResponseEntity
                    .ok()
                    .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                    .body(result);

        } catch (Exception e) {
            logger.error("导入数据时发生错误", e);
            response.put("success", false);
            response.put("message", "导入失败：" + e.getMessage());
            return ResponseEntity
                    .ok()
                    .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                    .body(response);
        }
    }

    private Map<String, Object> handleProjectImport(String[] headers, List<String[]> dataRows, boolean clearExisting) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 验证表头字段
            Set<String> requiredFields = new HashSet<>(Arrays.asList(
                    "projectName", "projectCode", "customerName", "responsible",
                    "status", "visionType", "createdBy", "plannedStartDate", "plannedEndDate", "createdDate",
                    "SalesOrderNumber", "ProductPartNumber", "Remarks", "Risk"));

            // 创建不区分大小写的表头集合
            Set<String> headerSet = new HashSet<>();
            for (String header : headers) {
                headerSet.add(header.toLowerCase());
            }

            // 检查是否缺少必需字段（不区分大小写）
            Set<String> missingFields = requiredFields.stream()
                    .filter(field -> !headerSet.contains(field.toLowerCase()))
                    .collect(Collectors.toSet());

            if (!missingFields.isEmpty()) {
                logger.warn("缺少必需字段: {}",
                        String.join(", ", missingFields));
                response.put("success", false);
                response.put("message", "CSV文件缺少必需字段：" +
                        String.join(", ", missingFields));
                return response;
            }

            // 创建不区分大小写的表头映射
            Map<String, Integer> headerMap = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].toLowerCase(), i);
            }

            // 清空现有数据
            logger.info("开始清空现有项目数据");
            if (clearExisting) {
                projectService.deleteAllProjects();
            }
            logger.info("现有项目数据清空完成");

            // 重置项目表自增ID
            try {
                logger.info("开始重置项目表自增ID");
                projectService.resetProjectId();
                logger.info("项目表自增ID重置完成");
            } catch (Exception e) {
                logger.error("重置项目表自增ID失败: {}", e.getMessage(), e);
                throw new RuntimeException("重置项目表自增ID失败: " + e.getMessage(), e);
            }

            // 导入新数据
            List<Project> projects = new ArrayList<>();

            logger.info("开始处理项目数据，共 {} 行", dataRows.size());

            for (String[] row : dataRows) {
                Project project = new Project();

                // 设置基本字段
                project.setProjectName(getValueFromRow(row, headerMap, "projectName"));
                project.setProjectCode(getValueFromRow(row, headerMap, "projectCode"));
                project.setCustomerName(getValueFromRow(row, headerMap, "customerName"));
                project.setResponsible(getValueFromRow(row, headerMap, "responsible"));
                project.setStatus(getValueFromRow(row, headerMap, "status"));
                project.setVisionType(getValueFromRow(row, headerMap, "visionType"));
                project.setSalesOrderNumber(getValueFromRow(row, headerMap, "SalesOrderNumber"));
                project.setProductPartNumber(getValueFromRow(row, headerMap, "ProductPartNumber"));
                project.setSalesResponsible(getValueFromRow(row, headerMap, "salesResponsible"));
                project.setMechanicalResponsible(getValueFromRow(row, headerMap, "mechanicalResponsible"));
                project.setElectricalResponsible(getValueFromRow(row, headerMap, "electricalResponsible"));
                project.setCreatedBy(getValueFromRow(row, headerMap, "createdBy"));
                project.setRemarks(getValueFromRow(row, headerMap, "Remarks"));
                project.setRisk(getValueFromRow(row, headerMap, "Risk"));

                // 设置数值字段
                String visionCostStr = getValueFromRow(row, headerMap, "visionCost");
                if (visionCostStr != null && !visionCostStr.trim().isEmpty()) {
                    project.setVisionCost(new BigDecimal(visionCostStr));
                }

                String durationDaysStr = getValueFromRow(row, headerMap, "durationDays");
                if (durationDaysStr != null && !durationDaysStr.trim().isEmpty()) {
                    project.setDurationDays(new BigDecimal(durationDaysStr));
                }

                String bonusStr = getValueFromRow(row, headerMap, "bonus");
                if (bonusStr != null && !bonusStr.trim().isEmpty()) {
                    project.setBonus(new BigDecimal(bonusStr));
                }

                String archiveStr = getValueFromRow(row, headerMap, "archive");
                if (archiveStr != null && !archiveStr.trim().isEmpty()) {
                    project.setArchive(Integer.valueOf(archiveStr));
                }

                // 设置日期字段
                setDateField(project, "plannedStartDate", getValueFromRow(row, headerMap, "plannedStartDate"));
                setDateField(project, "plannedEndDate", getValueFromRow(row, headerMap, "plannedEndDate"));
                setDateField(project, "actualStartDate", getValueFromRow(row, headerMap, "actualStartDate"));
                setDateField(project, "actualEndDate", getValueFromRow(row, headerMap, "actualEndDate"));
                setDateField(project, "createdDate", getValueFromRow(row, headerMap, "createdDate"));

                logger.debug("处理项目数据: projectName={}", project.getProjectName());

                projects.add(project);
            }

            // 批量保存项目
            logger.info("开始保存 {} 个项目", projects.size());
            List<Project> savedProjects = projectService.saveAllProjects(projects);
            logger.info("成功保存 {} 个项目", savedProjects.size());

            response.put("success", true);
            response.put("message", "成功导入 " + savedProjects.size() + " 个项目");
            return response;

        } catch (Exception e) {
            logger.error("导入项目数据时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "导入项目失败：" + e.getMessage());
            return response;
        }
    }

    private Map<String, Object> handleTaskImport(String[] headers, List<String[]> dataRows, boolean clearExisting,
            boolean autoProjectOffset) {
        Map<String, Object> response = new HashMap<>();
        long count = 0;
        try {
            // 验证表头字段
            Set<String> requiredFields = new HashSet<>(Arrays.asList(
                    "ProjectId", "TaskName", "Responsible", "Status", "Risk", "CreatedDate", "Type"));

            // 创建不区分大小写的表头集合
            Set<String> headerSet = new HashSet<>();
            for (String header : headers) {
                headerSet.add(header.toLowerCase());
            }

            // 检查是否缺少必需字段（不区分大小写）
            Set<String> missingFields = requiredFields.stream()
                    .filter(field -> !headerSet.contains(field.toLowerCase()))
                    .collect(Collectors.toSet());

            if (!missingFields.isEmpty()) {
                logger.warn("缺少必需字段: {}",
                        String.join(", ", missingFields));
                response.put("success", false);
                response.put("message", "CSV文件缺少必需字段：" +
                        String.join(", ", missingFields));
                return response;
            }

            // 创建不区分大小写的表头映射
            Map<String, Integer> headerMap = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].toLowerCase(), i);
            }

            long projectOffset = 0;
            long taskOffset = 0;
            if (clearExisting) {
                // 清空现有数据（包含重置自增ID）
                logger.info("开始清空现有数据");

                taskService.deleteAllTasks();
                logger.info("现有数据清空完成");
            } else {
                // 如果不清空现有数据，获取当前项目最大ID作为偏移量
                List<Project> allProjects = projectService.findAllProjects();
                if (!allProjects.isEmpty()) {
                    // 获取最大的projectId
                    projectOffset = allProjects.stream()
                            .mapToLong(Project::getProjectId)
                            .max()
                            .orElse(0);
                }
                logger.info("当前项目最大ID(作为偏移量): {}", projectOffset);

                // 获取当前任务最大ID作为偏移量
                Long maxTaskId = taskService.findMaxTaskId();
                if (maxTaskId != null) {
                    taskOffset = maxTaskId;
                }
                logger.info("当前任务最大ID(作为偏移量): {}", taskOffset);
            }

            // 导入新数据
            List<ProjectTask> tasks = new ArrayList<>();

            for (String[] row : dataRows) {
                count++;
                ProjectTask task = new ProjectTask();


                // 设置其他基本字段
                String projectIdStr = getValueFromRow(row, headerMap, "ProjectId");
                if (projectIdStr == null || projectIdStr.trim().isEmpty()) {
                    logger.error("任务数据缺少ProjectId字段");
                    throw new IllegalArgumentException("任务数据缺少ProjectId字段");
                }
                try {
                    // 在非清空模式下，添加项目偏移量
                    long originalProjectId = Long.valueOf(projectIdStr.trim());
                    if (autoProjectOffset) {
                        task.setProjectId(originalProjectId + projectOffset);
                        logger.debug("设置任务项目ID: 原始ID={}, 偏移量={}, 最终ID={}",
                                originalProjectId, projectOffset, originalProjectId + projectOffset);
                    } else {
                        task.setProjectId(originalProjectId);
                        logger.debug("设置任务项目ID: 原始ID={}", originalProjectId);
                    }
                } catch (NumberFormatException e) {
                    logger.error("ProjectId格式不正确: {}", projectIdStr);
                    throw new IllegalArgumentException("ProjectId格式不正确: " + projectIdStr);
                }

                // 获取任务ID
                String taskIdStr = getValueFromRow(row, headerMap, "taskid");
                if (taskIdStr == null || taskIdStr.trim().isEmpty()) {
                    // 输出所有列标题以便调试
                    StringBuilder headerList = new StringBuilder();
                    for (String key : headerMap.keySet()) {
                        headerList.append(key).append(", ");
                    }
                    logger.error("任务数据缺少TaskId字段，可用列标题: {}", headerList.toString());
                    throw new IllegalArgumentException("任务数据缺少TaskId字段");
                }

                // 将字符串转换为Long类型
                long originalTaskId = Long.valueOf(taskIdStr.trim());
                // 设置任务ID，并在备注中记录原始ID
                long finalTaskId = originalTaskId + taskOffset;
                task.setTaskId(finalTaskId); // 设置任务ID，但这个值在保存时会被忽略

                logger.debug("设置任务ID: 原始ID={}, 偏移量={}, 最终ID={}",
                        originalTaskId, taskOffset, finalTaskId);

                task.setTaskName(getValueFromRow(row, headerMap, "TaskName"));
                task.setResponsible(getValueFromRow(row, headerMap, "Responsible"));
                task.setStatus(getValueFromRow(row, headerMap, "Status"));
                task.setRisk(getValueFromRow(row, headerMap, "Risk"));
                task.setRemarks(getValueFromRow(row, headerMap, "Remarks"));

                // 设置日期字段
                setDateField(task, "actualStartDate", getValueFromRow(row, headerMap, "ActualStartDate"));
                setDateField(task, "actualEndDate", getValueFromRow(row, headerMap, "ActualEndDate"));
                setDateField(task, "createdDate", getValueFromRow(row, headerMap, "CreatedDate"));

                // 设置数值字段
                String durationDaysStr = getValueFromRow(row, headerMap, "DurationDays");
                if (durationDaysStr != null && !durationDaysStr.trim().isEmpty()) {
                    task.setDurationDays(new BigDecimal(durationDaysStr));
                }

                String ratioStr = getValueFromRow(row, headerMap, "Ratio");
                if (ratioStr != null && !ratioStr.trim().isEmpty()) {
                    task.setRatio(Double.valueOf(ratioStr));
                }                String bonusStr = getValueFromRow(row, headerMap, "Bonus");
                if (bonusStr != null && !bonusStr.trim().isEmpty()) {
                    task.setBonus(new BigDecimal(bonusStr));
                }

                String progressStr = getValueFromRow(row, headerMap, "Progress");
                if (progressStr != null && !progressStr.trim().isEmpty()) {
                    task.setProgress(Integer.parseInt(progressStr));
                }

                task.setType(getValueFromRow(row, headerMap, "Type"));
                task.setCreatedBy(getValueFromRow(row, headerMap, "CreatedBy"));

                tasks.add(task);
            }

            logger.info("开始保存 {} 个任务", tasks.size());

            int successCount = 0;
            int failCount = 0;

            // 使用服务层方法保存任务，而非直接 JDBC 插入
            for (ProjectTask task : tasks) {
                try {
                    // 使用服务层方法保存任务
                    ProjectTask savedTask = taskService.createTaskWithCustomId(task);
                    
                    if (savedTask != null && savedTask.getTaskId() != null) {
                        successCount++;
                        logger.debug("成功插入任务ID: {}", savedTask.getTaskId());
                    } else {
                        failCount++;
                        logger.error("保存任务失败，任务ID: {}", task.getTaskId());
                    }
                } catch (Exception e) {
                    failCount++;
                    logger.error("插入任务ID {} 失败: {}", task.getTaskId(), e.getMessage(), e);
                }
            }

            logger.info("成功保存 {} 个任务，失败 {} 个", successCount, failCount);

            response.put("success", true);
            response.put("message", "成功导入 " + successCount + " 个任务，失败 " + failCount + " 个");
            return response;

        } catch (Exception e) {
            logger.error("导入任务数据时发生错误: {}", count, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "导入任务失败：" + e.getMessage());
            return response;
        }
    }

    private String getValueFromRow(String[] row, Map<String, Integer> headerMap, String fieldName) {
        // 创建不区分大小写的表头映射
        Map<String, Integer> caseInsensitiveHeaderMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : headerMap.entrySet()) {
            String key = entry.getKey();
            // 去除可能的前后空格和不可见字符
            key = key.trim().toLowerCase();
            caseInsensitiveHeaderMap.put(key, entry.getValue());
            // 添加额外的调试日志
            logger.debug("添加列映射: '{}' -> {}", key, entry.getValue());
        }

        // 处理要查找的字段名
        String normalizedFieldName = fieldName.trim().toLowerCase();
        logger.debug("查找字段: '{}', 规范化后: '{}'", fieldName, normalizedFieldName);

        // 输出所有键以便调试
        if (logger.isDebugEnabled()) {
            StringBuilder keysInfo = new StringBuilder("当前所有键: ");
            for (String key : caseInsensitiveHeaderMap.keySet()) {
                keysInfo.append("'").append(key).append("', ");
            }
            logger.debug(keysInfo.toString());
        }

        // 尝试直接查找
        Integer index = caseInsensitiveHeaderMap.get(normalizedFieldName);

        // 如果找不到，尝试模糊匹配
        if (index == null) {
            logger.debug("直接查找失败，尝试模糊匹配");
            for (Map.Entry<String, Integer> entry : caseInsensitiveHeaderMap.entrySet()) {
                if (entry.getKey().contains(normalizedFieldName) || normalizedFieldName.contains(entry.getKey())) {
                    index = entry.getValue();
                    logger.debug("模糊匹配成功: '{}' 匹配 '{}'", normalizedFieldName, entry.getKey());
                    break;
                }
            }
        } else {
            logger.debug("直接查找成功: index={}", index);
        }

        if (index != null && index < row.length) {
            String value = row[index];
            logger.debug("找到字段 '{}' 的值: '{}'", fieldName, value);
            return value;
        }

        logger.debug("未找到字段 '{}' 的值", fieldName);
        return null;
    }

    private void setDateField(Object obj, String fieldName, String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return;
        }

        try {
            Field dateField = obj.getClass().getDeclaredField(fieldName);
            dateField.setAccessible(true);

            // 预处理日期字符串
            dateStr = dateStr.trim().replaceAll("\\s+", " ");

            LocalDateTime dateTime = null;

            // 如果包含时间
            if (dateStr.contains(" ")) {
                String[] parts = dateStr.split(" ");
                if (parts.length == 2) {
                    String datePart = parts[0].trim();
                    String timePart = parts[1].trim();

                    // 解析日期部分
                    String[] dateParts = datePart.contains("/") ? datePart.split("/") : datePart.split("-");
                    if (dateParts.length == 3) {
                        int year = Integer.parseInt(dateParts[0]);
                        int month = Integer.parseInt(dateParts[1]);
                        int day = Integer.parseInt(dateParts[2]);

                        // 解析时间部分
                        String[] timeParts = timePart.split(":");
                        if (timeParts.length >= 2) {
                            int hour = Integer.parseInt(timeParts[0].trim());
                            int minute = Integer.parseInt(timeParts[1].trim());
                            int second = timeParts.length > 2 ? Integer.parseInt(timeParts[2].trim()) : 0;

                            // 验证所有值的合法性
                            if (year >= 1900 && year <= 9999 &&
                                    month >= 1 && month <= 12 &&
                                    day >= 1 && day <= 31 &&
                                    hour >= 0 && hour <= 23 &&
                                    minute >= 0 && minute <= 59 &&
                                    second >= 0 && second <= 59) {

                                dateTime = LocalDateTime.of(year, month, day, hour, minute, second);
                            }
                        }
                    }
                }
            } else {
                // 只有日期的情况
                String[] dateParts = dateStr.split("/");
                if (dateParts.length == 3) {
                    int year = Integer.parseInt(dateParts[0]);
                    int month = Integer.parseInt(dateParts[1]);
                    int day = Integer.parseInt(dateParts[2]);

                    if (year >= 1900 && year <= 9999 &&
                            month >= 1 && month <= 12 &&
                            day >= 1 && day <= 31) {

                        dateTime = LocalDateTime.of(year, month, day, 0, 0, 0);
                    }
                }
            }

            if (dateTime == null) {
                logger.warn("无法解析日期字符串: {} 为字段: {}", dateStr, fieldName);
                return;
            }

            // 将日期格式化为标准格式并设置字段值
            String standardDateStr = dateTime.format(DATE_FORMATTER);
            dateField.set(obj, standardDateStr);

        } catch (Exception e) {
            logger.error("设置日期字段时发生错误: {} - {}", fieldName, e.getMessage());
        }
    }
}