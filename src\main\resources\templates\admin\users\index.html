<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('用户管理')}">
    <meta charset="UTF-8">
    <title>用户管理</title>
    <!-- CSRF Token -->
    <meta name="_csrf" th:content="${_csrf?.token}">
    <meta name="_csrf_header" th:content="${_csrf?.headerName}">
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">用户管理</h1>
                </div>


                <!-- 显示消息 -->
                <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${message}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 操作按钮区域 -->
                <div class="mb-4">
                    <a th:href="@{/system/users/new}" class="btn btn-primary me-2">
                        <i class="bi bi-person-plus"></i> 添加用户
                    </a>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="bi bi-upload"></i> 导入数据
                    </button>
                </div>

                <!-- 用户列表表格 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-table"></i>
                        用户列表
                    </div>
                    <div class="card-body">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>最后修改时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="user : ${users}">
                                    <td th:text="${user.username}">用户名</td>
                                    <td th:text="${user.role}">角色</td>
                                    <td>
                                        <span class="badge bg-success">启用</span>
                                    </td>
                                    <td th:text="${#temporals.format(user.createdDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                                    <td th:text="${#temporals.format(user.lastModifiedTime, 'yyyy-MM-dd HH:mm:ss')}">最后修改时间</td>
                                    <td>
                                        <a th:href="@{/system/users/{id}/edit(id=${user.userId})}" class="btn btn-sm btn-primary me-1">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger"
                                                th:onclick="'deleteUser(\'' + ${user.userId} + '\')'">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 导入数据模态框 -->
                <div class="modal fade" id="importModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">导入数据</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="importForm" th:action="@{/admin/import}" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label class="form-label">选择要导入的表：</label>
                                        <select class="form-select" name="tableName" required>
                                            <option value="">请选择...</option>
                                            <option value="projects">项目表</option>
                                            <option value="tasks">任务表</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">选择CSV文件：</label>
                                        <input type="file" class="form-control" name="file" accept=".csv" required>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="clearExisting" id="clearExisting">
                                            <label class="form-check-label" for="clearExisting">清空现有数据</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="autoProjectOffset" id="autoProjectOffset" checked>
                                            <label class="form-check-label" for="autoProjectOffset">任务导入时自动获取项目ID偏移量并加到CSV文件中的项目ID上</label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="submitImport()">导入</button>
                            </div>
                        </div>
                    </div>
                </div>

    </div>

    <!-- Bootstrap & jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

    <!-- 页面特定的JavaScript -->
    <script th:inline="javascript">
        function submitImport() {
            console.log("开始导入数据...");

            const fileInput = document.querySelector('#importForm input[name="file"]');
            const tableSelect = document.querySelector('#importForm select[name="tableName"]');
            const clearExisting = document.querySelector('#importForm input[name="clearExisting"]').checked;
            const autoProjectOffset = document.querySelector('#importForm input[name="autoProjectOffset"]').checked;

            if (!fileInput.files.length) {
                alert('请选择要导入的文件');
                return;
            }

            const file = fileInput.files[0];
            const tableName = tableSelect.value;

            if (!tableName) {
                alert('请选择要导入的表');
                return;
            }

            console.log(`选择的表: ${tableName}, 文件: ${file.name}, 清空现有数据: ${clearExisting}, 自动获取项目ID偏移量: ${autoProjectOffset}`);

            if (!confirm('确定要导入数据吗？此操作可能会影响现有数据。')) {
                console.log("用户取消导入");
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('tableName', tableName);
            formData.append('clearExisting', clearExisting);
            formData.append('autoProjectOffset', autoProjectOffset);

            // 获取 CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            console.log("准备发送请求...");

            fetch('/admin/import', {
                method: 'POST',
                body: formData,
                headers: {
                    [header]: token,
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                console.log("收到响应状态:", response.status);
                console.log("响应headers:", Object.fromEntries(response.headers.entries()));
                if (!response.ok) {
                    console.error("响应不成功，状态码:", response.status);
                    throw new Error('导入失败，HTTP状态: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log("导入结果详情:", JSON.stringify(data, null, 2));
                if (data.success) {
                    alert(data.message || '数据导入成功！');
                    location.reload();
                } else {
                    console.error("导入失败，服务器返回:", data);
                    throw new Error(data.message || '导入失败');
                }
            })
            .catch(error => {
                console.error("导入错误详情:", error);
                alert('导入失败: ' + error.message);
            });
        }

        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = /*[[@{/system/users/delete}]]*/ '';

                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'userId';
                input.value = userId;

                // 添加 CSRF 防护，使用 meta 标签中的值
                const csrfTokenMeta = document.querySelector('meta[name="_csrf"]');
                const csrfHeaderMeta = document.querySelector('meta[name="_csrf_header"]');

                if (csrfTokenMeta && csrfHeaderMeta) {
                    const csrf = document.createElement('input');
                    csrf.type = 'hidden';
                    csrf.name = csrfHeaderMeta.content;
                    csrf.value = csrfTokenMeta.content;
                    form.appendChild(csrf);
                }

                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>