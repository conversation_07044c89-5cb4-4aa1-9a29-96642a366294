package com.mylog.service;

import com.mylog.model.SubTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SubTaskService {
    
    List<SubTask> findAllSubTasks();
    
    Optional<SubTask> findSubTaskById(Long id);
    
    List<SubTask> findSubTasksByTaskId(Long taskId);
    
    Page<SubTask> findSubTasksByTaskId(Long taskId, Pageable pageable);
    
    SubTask saveSubTask(SubTask subTask);
    
    void deleteSubTask(Long id);
    
    Integer getNextSequenceNumber(Long taskId);
    
    List<SubTask> searchSubTasks(String keyword);
    
    List<SubTask> findSubTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    Optional<SubTask> findLatestSubTaskByTaskId(Long taskId);
    
    /**
     * 批量获取多个任务的最新子任务
     * @param taskIds 任务ID列表
     * @return 每个任务的最新子任务
     */
    List<SubTask> findLatestSubTasksByTaskIds(List<Long> taskIds);

    /**
     * 分页查询所有评论
     * @param pageable 分页参数
     * @return 分页的评论列表
     */
    Page<SubTask> findAllSubTasksPaged(Pageable pageable);

    /**
     * 动态搜索评论
     * @param searchCriteria 搜索条件
     * @param pageable 分页参数
     * @return 分页的评论列表
     */
    Page<SubTask> dynamicSearchSubTasks(Map<String, String> searchCriteria, Pageable pageable);
}