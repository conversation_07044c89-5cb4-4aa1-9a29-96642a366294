package com.mylog.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "PersonnelStatus")
public class PersonnelStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "status")
    private String status;

    @Column(name = "location")
    private String location;

    @Column(name = "create_time")
    private String createTime;  // 使用字符串类型存储，格式yyyyMMdd HH:mm:ss    
    
    @Column(name = "effective_time")
    private String effectiveTime;  // 使用字符串类型存储，格式yyyyMMdd HH:mm:ss

    @Column(name = "workflow_instance_id")
    private Long workflowInstanceId;  // 关联的流程实例ID

    @Transient // 非持久化字段，不映射到数据库
    private Integer inProgressProjectCount;
    
    @Transient // 非持久化字段，不映射到数据库
    private Integer inProgressTaskCount;
    
    @Transient // 非持久化字段，不映射到数据库
    private Integer newProjectsInSixMonths;
    
    @Transient // 非持久化字段，不映射到数据库
    private Integer newTasksInSixMonths;
    
    @Transient // 非持久化字段，不映射到数据库
    private Integer completedProjectsInSixMonths;
    
    @Transient // 非持久化字段，不映射到数据库
    private Integer completedTasksInSixMonths;

    public PersonnelStatus() {
    }    public PersonnelStatus(String name, String status, String location, String createTime, String effectiveTime) {
        this.name = name;
        this.status = status;
        this.location = location;
        this.createTime = createTime;
        this.effectiveTime = effectiveTime;
    }

    public PersonnelStatus(String name, String status, String location, String createTime, String effectiveTime, Long workflowInstanceId) {
        this.name = name;
        this.status = status;
        this.location = location;
        this.createTime = createTime;
        this.effectiveTime = effectiveTime;
        this.workflowInstanceId = workflowInstanceId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Long getWorkflowInstanceId() {
        return workflowInstanceId;
    }

    public void setWorkflowInstanceId(Long workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
    }

    public Integer getInProgressProjectCount() {
        return inProgressProjectCount == null ? 0 : inProgressProjectCount;
    }

    public void setInProgressProjectCount(Integer inProgressProjectCount) {
        this.inProgressProjectCount = inProgressProjectCount;
    }    public Integer getInProgressTaskCount() {
        return inProgressTaskCount == null ? 0 : inProgressTaskCount;
    }

    public void setInProgressTaskCount(Integer inProgressTaskCount) {
        this.inProgressTaskCount = inProgressTaskCount;
    }

    public Integer getNewProjectsInSixMonths() {
        return newProjectsInSixMonths == null ? 0 : newProjectsInSixMonths;
    }

    public void setNewProjectsInSixMonths(Integer newProjectsInSixMonths) {
        this.newProjectsInSixMonths = newProjectsInSixMonths;
    }

    public Integer getNewTasksInSixMonths() {
        return newTasksInSixMonths == null ? 0 : newTasksInSixMonths;
    }  
    
    public void setNewTasksInSixMonths(Integer newTasksInSixMonths) {
        this.newTasksInSixMonths = newTasksInSixMonths;
    }
    
    public Integer getCompletedProjectsInSixMonths() {
        return completedProjectsInSixMonths == null ? 0 : completedProjectsInSixMonths;
    }

    public void setCompletedProjectsInSixMonths(Integer completedProjectsInSixMonths) {
        this.completedProjectsInSixMonths = completedProjectsInSixMonths;
    }

    public Integer getCompletedTasksInSixMonths() {
        return completedTasksInSixMonths == null ? 0 : completedTasksInSixMonths;
    }

    public void setCompletedTasksInSixMonths(Integer completedTasksInSixMonths) {
        this.completedTasksInSixMonths = completedTasksInSixMonths;
    }@Override
    public String toString() {
        return "PersonnelStatus{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status='" + status + '\'' +
                ", location='" + location + '\'' +
                ", createTime='" + createTime + '\'' +
                ", effectiveTime='" + effectiveTime + '\'' +
                ", workflowInstanceId=" + workflowInstanceId +
                ", inProgressProjectCount=" + inProgressProjectCount +
                ", inProgressTaskCount=" + inProgressTaskCount +
                ", newProjectsInSixMonths=" + newProjectsInSixMonths +
                ", newTasksInSixMonths=" + newTasksInSixMonths +
                ", completedProjectsInSixMonths=" + completedProjectsInSixMonths +
                ", completedTasksInSixMonths=" + completedTasksInSixMonths +
                '}';
    }
}
