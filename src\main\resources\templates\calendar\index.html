<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('日程管理')}">
    <meta charset="UTF-8">
    <title>日程管理</title>
    <link rel="stylesheet" th:href="@{/css/calendar/calendar.css}">
</head>
<body th:replace="~{fragments/layout :: body(~{::content}, ~{::scripts})}">
    <content>
        <!-- 隐藏的用户信息，供JavaScript使用 -->
        <div data-current-user="" sec:authentication="name" style="display: none;"></div>
        
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i class="bi bi-calendar3 me-2"></i>日程管理
            </h1>            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="prevMonth">
                        <i class="bi bi-chevron-left"></i> 上月
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="today">今天</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="nextMonth">
                        下月 <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="monthView">月视图</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="weekView">周视图</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="dayView">日视图</button>
                </div>
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showCheckInStatistics()">
                        <i class="bi bi-graph-up"></i> 签到统计
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showCheckInHistory()">
                        <i class="bi bi-clock-history"></i> 签到历史
                    </button>
                </div>
                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#eventModal">
                    <i class="bi bi-plus"></i> 新建事件
                </button>
            </div>
        </div>        <!-- 日历主体 -->
        <div class="row calendar-layout align-items-start">
            <!-- 左侧日历面板 -->
            <div class="col-md-2">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">我的日历</h6>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#calendarModal">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                    <div class="card-body p-2">
                        <div id="calendarList">
                            <!-- 日历列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 小日历 -->
                <div class="card mt-3">
                    <div class="card-body p-2">
                        <div id="miniCalendar">
                            <!-- 小日历将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧主日历区域 -->
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 id="calendarTitle" class="mb-0">2025年5月</h4>
                    </div>
                    <div class="card-body p-0">
                        <div id="mainCalendar">
                            <!-- 主日历内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 事件创建/编辑模态框 -->
        <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="eventModalLabel">新建事件</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="eventForm">                        <div class="modal-body event-form-two-column">
                            <input type="hidden" id="eventId" name="id">
                            
                            <!-- 第一行：标题 和 日历 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="eventTitle" class="form-label">标题 *</label>
                                    <input type="text" class="form-control" id="eventTitle" name="title" required maxlength="200">
                                </div>
                                <div class="col-md-6">
                                    <label for="eventCalendar" class="form-label">日历 *</label>
                                    <select class="form-select" id="eventCalendar" name="calendarId" required>
                                        <!-- 选项将通过JavaScript动态加载 -->
                                    </select>
                                </div>
                            </div>

                            <!-- 第二行：开始时间 和 结束时间 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="startTime" class="form-label">开始时间 *</label>
                                    <input type="datetime-local" class="form-control" id="startTime" name="startTime" required>
                                </div>
                                <div class="col-md-6" id="endTimeRow">
                                    <label for="endTime" class="form-label">结束时间</label>
                                    <input type="datetime-local" class="form-control" id="endTime" name="endTime">
                                </div>
                            </div>

                            <!-- 第三行：地点 和 类型 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="eventLocation" class="form-label">地点</label>
                                    <input type="text" class="form-control" id="eventLocation" name="location" maxlength="200">
                                </div>
                                <div class="col-md-6">
                                    <label for="eventType" class="form-label">类型</label>
                                    <select class="form-select" id="eventType" name="eventType">
                                        <option value="MEETING">会议</option>
                                        <option value="TASK">任务</option>
                                        <option value="APPOINTMENT">约会</option>
                                        <option value="REMINDER">提醒</option>
                                        <option value="OTHER">其他</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 第四行：优先级 和 空位 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="eventPriority" class="form-label">优先级</label>
                                    <select class="form-select" id="eventPriority" name="priority">
                                        <option value="LOW">低</option>
                                        <option value="NORMAL" selected>普通</option>
                                        <option value="HIGH">高</option>
                                        <option value="URGENT">紧急</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <!-- 复选框区域 -->
                                    <div class="d-flex flex-column">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="isAllDay" name="isAllDay">
                                            <label class="form-check-label" for="isAllDay">全天事件</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isRecurring" name="isRecurring">
                                            <label class="form-check-label" for="isRecurring">重复事件</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第五行：描述 -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label for="eventDescription" class="form-label">描述</label>
                                    <textarea class="form-control" id="eventDescription" name="description" rows="3" maxlength="1000"></textarea>
                                </div>
                            </div>                            <!-- 重复事件配置 -->
                            <div id="recurringOptions" style="display: none;">
                                <div class="row mb-3 recurring-options-three-column">
                                    <div class="col-md-4">
                                        <label for="recurrenceType" class="form-label">重复类型</label>
                                        <select class="form-select" id="recurrenceType" name="recurrenceType">
                                            <option value="DAILY">每天</option>
                                            <option value="WEEKLY">每周</option>
                                            <option value="MONTHLY">每月</option>
                                            <option value="YEARLY">每年</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="recurrenceInterval" class="form-label">间隔</label>
                                        <input type="number" class="form-control" id="recurrenceInterval" name="recurrenceInterval" value="1" min="1">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="recurrenceEndDate" class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="recurrenceEndDate" name="recurrenceEndDate">
                                    </div>
                                </div>                            </div>

                            <!-- 提醒设置 -->
                            <div class="row mb-3">
                                <label class="col-sm-3 col-form-label">提醒</label>
                                <div class="col-sm-9">
                                    <div id="reminderList">
                                        <!-- 提醒列表 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="addReminder">
                                        <i class="bi bi-plus"></i> 添加提醒
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-danger" id="deleteEvent" style="display: none;">删除</button>
                            <button type="submit" class="btn btn-primary" id="saveEvent">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 日历创建/编辑模态框 -->
        <div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="calendarModalLabel">新建日历</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="calendarForm">
                        <div class="modal-body">
                            <input type="hidden" id="calendarId" name="id">
                            
                            <div class="mb-3">
                                <label for="calendarName" class="form-label">日历名称 *</label>
                                <input type="text" class="form-control" id="calendarName" name="name" required maxlength="100">
                            </div>

                            <div class="mb-3">
                                <label for="calendarDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="calendarDescription" name="description" rows="3" maxlength="500"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="calendarColor" class="form-label">颜色</label>
                                <input type="color" class="form-control form-control-color" id="calendarColor" name="color" value="#007bff">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isDefault" name="isDefault">
                                    <label class="form-check-label" for="isDefault">设为默认日历</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isShared" name="isShared">
                                    <label class="form-check-label" for="isShared">共享日历</label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-danger" id="deleteCalendar" style="display: none;">删除</button>
                            <button type="submit" class="btn btn-primary" id="saveCalendar">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 事件详情模态框 -->
        <div class="modal fade" id="eventDetailModal" tabindex="-1" aria-labelledby="eventDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="eventDetailModalLabel">事件详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="eventDetailContent">
                        <!-- 事件详情内容 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" id="editEventBtn">编辑</button>
                    </div>                </div>
            </div>
        </div>
    </content>    <scripts>
        <link rel="stylesheet" th:href="@{/css/calendar/calendar.css}">        <script th:src="@{/js/calendar/calendar.js}"></script>
          <script th:inline="javascript">
        // 人员数据传递到前端
        window.personnelData = [[${personnel}]];
        
        // 调试信息        console.log('📊 Thymeleaf传递的人员数据:', window.personnelData);
        console.log('📊 人员数据类型:', typeof window.personnelData);
        console.log('📊 人员数据长度:', window.personnelData ? window.personnelData.length : 'undefined');
        
        // CalendarManager 已在 calendar.js 中初始化，这里不需要重复初始化
        
        // 签到统计和历史功能的全局函数
        function showCheckInStatistics() {
            if (checkInManager) {
                const startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 1);
                const endDate = new Date();
                
                const startStr = startDate.toISOString().split('T')[0] + 'T00:00:00';
                const endStr = endDate.toISOString().split('T')[0] + 'T23:59:59';
                
                checkInManager.showCheckInStatistics(startStr, endStr);
            }
        }
        
        function showCheckInHistory() {
            if (checkInManager) {
                const startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 1);
                const endDate = new Date();
                
                const startStr = startDate.toISOString().split('T')[0] + 'T00:00:00';
                const endStr = endDate.toISOString().split('T')[0] + 'T23:59:59';
                
                checkInManager.showCheckInHistory(startStr, endStr);
            }
        }
        </script>
    </scripts>
</body>
</html>
