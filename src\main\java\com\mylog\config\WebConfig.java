package com.mylog.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.lang.NonNull;

import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${data.path}")
    private String dataPath;

    @Override
    public void configureContentNegotiation(@NonNull ContentNegotiationConfigurer configurer) {
        configurer
            .favorParameter(false)
            .ignoreAcceptHeader(false)
            .defaultContentType(MediaType.TEXT_HTML)
            .mediaType("html", MediaType.TEXT_HTML)
            .mediaType("json", MediaType.APPLICATION_JSON);
    }

    /**
     * 配置静态资源处理
     * 添加data目录作为静态资源位置，确保不与API路径冲突
     */
    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // 获取data目录的绝对路径
        Path dataDir = Paths.get(dataPath).toAbsolutePath().normalize();
        String dataDirPath = dataDir.toString().replace('\\', '/');

        // 如果路径不以/结尾，添加/
        if (!dataDirPath.endsWith("/")) {
            dataDirPath += "/";
        }

        // 添加file:前缀，表示这是一个文件系统路径
        String location = "file:" + dataDirPath;

        // 配置/data/**路径映射到data目录，确保不与API路径冲突
        registry.addResourceHandler("/data/**")
                .addResourceLocations(location)
                .setCachePeriod(0);  // 禁用缓存以避免开发时的问题
    }
}