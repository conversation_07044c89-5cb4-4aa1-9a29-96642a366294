package com.mylog.service.impl;

import com.mylog.model.UserActivityLog;
import com.mylog.repository.UserActivityLogRepository;
import com.mylog.service.UserActivityLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class UserActivityLogServiceImpl implements UserActivityLogService {

    @Autowired
    private UserActivityLogRepository logRepository;

    @Override
    @Transactional
    public UserActivityLog saveLog(UserActivityLog log) {
        System.out.println("===> 保存用户活动日志: 类型=" + log.getActivityType() + ", 描述=" + log.getDescription());
        
        try {
            UserActivityLog savedLog = logRepository.save(log);
            System.out.println("===> 用户活动日志保存成功: ID=" + savedLog.getId());
            return savedLog;
        } catch (Exception e) {
            System.err.println("===> 保存用户活动日志失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    @Transactional
    public UserActivityLog logLogin(Long userId, String username, String ipAddress, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.LOGIN);
        log.setDescription("用户登录");
        log.setIpAddress(ipAddress);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }

    @Override
    @Transactional
    public UserActivityLog logLogout(Long userId, String username, String ipAddress, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.LOGOUT);
        log.setDescription("用户登出");
        log.setIpAddress(ipAddress);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }

    @Override
    @Transactional
    public UserActivityLog logCreate(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.CREATE);
        log.setDescription(description);
        log.setIpAddress(ipAddress);
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }

    @Override
    @Transactional
    public UserActivityLog logUpdate(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.UPDATE);
        log.setDescription(description);
        log.setIpAddress(ipAddress);
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }

    @Override
    @Transactional
    public UserActivityLog logDelete(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.DELETE);
        log.setDescription(description);
        log.setIpAddress(ipAddress);
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }    @Override
    @Transactional
    public UserActivityLog logView(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.VIEW);
        log.setDescription(description);
        log.setIpAddress(ipAddress);
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }    @Override
    @Transactional
    public UserActivityLog logDownload(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.DOWNLOAD);
        log.setDescription(description);
        log.setIpAddress(ipAddress);
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }

    @Override
    @Transactional
    public UserActivityLog logSettingsChange(Long userId, String username, String description, String ipAddress, String entityType, Long entityId, String accessType) {
        UserActivityLog log = new UserActivityLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setActivityType(UserActivityLog.ActivityType.SETTINGS_CHANGE);
        log.setDescription(description);
        log.setIpAddress(ipAddress);
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setAccessType(accessType);
        log.setTimestamp(LocalDateTime.now());
        return saveLog(log);
    }

    @Override
    public Optional<UserActivityLog> findLogById(Long id) {
        return logRepository.findById(id);
    }

    @Override
    public List<UserActivityLog> findAllLogs() {
        return logRepository.findAll();
    }

    @Override
    public Page<UserActivityLog> findAllLogs(Pageable pageable) {
        return logRepository.findAll(pageable);
    }

    @Override
    public Page<UserActivityLog> findLogsByUserId(Long userId, Pageable pageable) {
        return logRepository.findByUserId(userId, pageable);
    }

    @Override
    public Page<UserActivityLog> findLogsByActivityType(UserActivityLog.ActivityType activityType, Pageable pageable) {
        return logRepository.findByActivityType(activityType, pageable);
    }

    @Override
    public Page<UserActivityLog> findLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return logRepository.findByTimestampBetween(startTime, endTime, pageable);
    }

    @Override
    public Page<UserActivityLog> searchLogs(Map<String, Object> searchCriteria, Pageable pageable) {
        Long userId = null;
        String username = null;
        UserActivityLog.ActivityType activityType = null;
        String entityType = null;
        Long entityId = null;
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        
        if (searchCriteria.containsKey("userId")) {
            userId = (Long) searchCriteria.get("userId");
        }
        
        if (searchCriteria.containsKey("username")) {
            username = (String) searchCriteria.get("username");
        }
        
        if (searchCriteria.containsKey("activityType")) {
            Object activityTypeObj = searchCriteria.get("activityType");
            if (activityTypeObj instanceof String) {
                try {
                    activityType = UserActivityLog.ActivityType.valueOf((String) activityTypeObj);
                } catch (IllegalArgumentException e) {
                    // 忽略无效的活动类型
                }
            } else if (activityTypeObj instanceof UserActivityLog.ActivityType) {
                activityType = (UserActivityLog.ActivityType) activityTypeObj;
            }
        }
        
        if (searchCriteria.containsKey("entityType")) {
            entityType = (String) searchCriteria.get("entityType");
        }
        
        if (searchCriteria.containsKey("entityId")) {
            entityId = (Long) searchCriteria.get("entityId");
        }
        
        if (searchCriteria.containsKey("startTime")) {
            Object startTimeObj = searchCriteria.get("startTime");
            if (startTimeObj instanceof LocalDateTime) {
                startTime = (LocalDateTime) startTimeObj;
            } else if (startTimeObj instanceof String) {
                try {
                    startTime = LocalDateTime.parse((String) startTimeObj);
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }
        }
        
        if (searchCriteria.containsKey("endTime")) {
            Object endTimeObj = searchCriteria.get("endTime");
            if (endTimeObj instanceof LocalDateTime) {
                endTime = (LocalDateTime) endTimeObj;
            } else if (endTimeObj instanceof String) {
                try {
                    endTime = LocalDateTime.parse((String) endTimeObj);
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }
        }
        
        return logRepository.searchLogs(userId, username, activityType, entityType, entityId, startTime, endTime, pageable);
    }

    @Override
    @Transactional
    public long deleteLogsBefore(LocalDateTime beforeTime) {
        // 注意：这里现在使用兼容性方法，内部实际调用的是基于 createdDate 字段的查询
        List<UserActivityLog> logsToDelete = logRepository.findByTimestampBetween(LocalDateTime.MIN, beforeTime);
        long count = logsToDelete.size();
        logRepository.deleteAll(logsToDelete);
        return count;
    }
}