package com.mylog.service;

import com.mylog.dto.CalendarDTO;
import com.mylog.model.Calendar;
import java.util.List;
import java.util.Optional;

/**
 * 日历服务接口
 */
public interface CalendarService {
    
    /**
     * 创建日历
     */
    CalendarDTO createCalendar(CalendarDTO calendarDTO);
    
    /**
     * 更新日历
     */
    CalendarDTO updateCalendar(Long id, CalendarDTO calendarDTO);
    
    /**
     * 删除日历
     */
    void deleteCalendar(Long id);
    
    /**
     * 根据ID获取日历
     */
    Optional<CalendarDTO> getCalendarById(Long id);
    
    /**
     * 根据用户ID获取日历列表
     */
    List<CalendarDTO> getCalendarsByUserId(Long userId);
    
    /**
     * 获取用户的默认日历
     */
    Optional<CalendarDTO> getDefaultCalendar(Long userId);
    
    /**
     * 设置默认日历
     */
    CalendarDTO setDefaultCalendar(Long userId, Long calendarId);
    
    /**
     * 获取共享日历列表
     */
    List<CalendarDTO> getSharedCalendars();
    
    /**
     * 获取用户可访问的所有日历（包括共享日历）
     */
    List<CalendarDTO> getAccessibleCalendars(Long userId);
    
    /**
     * 检查日历名称是否存在
     */
    boolean isCalendarNameExists(Long userId, String name);
    
    /**
     * 检查用户是否有日历的访问权限
     */
    boolean hasCalendarAccess(Long userId, Long calendarId);
    
    /**
     * 创建用户默认日历
     */
    CalendarDTO createDefaultCalendar(Long userId, String userName);
}
