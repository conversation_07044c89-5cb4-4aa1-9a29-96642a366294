<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('分管任务')}">
    <meta charset="UTF-8">
    <title>分管任务</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">分管任务 <small class="fs-6">（<span class="badge bg-primary rounded-pill" th:text="'进行中 ' + ${inProgressCount}">进行中 0</span>/<span th:text="${totalCount}">0</span>）</small></h1>
                </div>

                <!-- 任务列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">分管任务列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" th:if="${delegatedTasks == null || delegatedTasks.empty}">
                            <i class="bi bi-info-circle me-2"></i>
                            暂无符合条件的分管任务。分管任务需要满足：1、任务名称包含"客户现场调试"；2、与该任务同一个项目下必须有一个类型为"分管"的任务且这个任务的责任人为您。
                        </div>

                        <!-- 任务表格 -->
                        <div class="table-responsive" th:if="${delegatedTasks != null && !delegatedTasks.empty}">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th style="white-space: nowrap;">任务名称</th>
                                        <th style="white-space: nowrap;">所属项目</th>
                                        <th style="white-space: nowrap;">负责人</th>
                                        <th style="white-space: nowrap;">状态</th>
                                        <th style="white-space: nowrap;">风险</th>
                                        <th style="white-space: nowrap;">进度</th>
                                        <th style="white-space: nowrap;">评论</th>
                                        <th style="white-space: nowrap;">创建日期</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${delegatedTasks}">
                                        <td><a th:href="@{/tasks/{id}(id=${task.taskId})}" th:text="${task.taskName}">示例任务</a></td>
                                        <td><a th:href="@{/projects/{id}(id=${task.projectId})}" th:text="${task.project != null ? task.project.projectName : '未知项目'}">项目名称</a></td>
                                        <td th:text="${task.responsible}">张三</td>
                                        <td>
                                            <span th:class="${'badge ' +
                                                (task.status == '进行中' ? 'bg-primary' :
                                                (task.status == '已完成' ? 'bg-success' :
                                                (task.status == '已暂停' ? 'bg-danger' :
                                                (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                                th:text="${task.status}">状态</span>
                                        </td>
                                        <td>
                                            <span th:class="${task.risk == '高' ? 'badge bg-danger' : (task.risk == '中' ? 'badge bg-warning' : 'badge bg-success')}"
                                                  th:text="${task.risk}">风险</span>
                                        </td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                    th:style="'width: ' + (${task.progressPercentage} ?: 0) + '%'"
                                                    th:class="${'progress-bar ' +
                                                      ((task.progressPercentage ?: 0) <= 30 ? 'bg-danger' :
                                                      ((task.progressPercentage ?: 0) <= 70 ? 'bg-warning' : 'bg-success'))}"
                                                    th:text="(${task.progressPercentage} ?: 0) + '%'">0%</div>
                                            </div>
                                        </td>
                                        <td>
                                            <span th:if="${task.commentDays != null}"
                                                  th:class="${'badge ' +
                                                    (task.commentDays >= 7 ? 'bg-danger' :
                                                    (task.commentDays >= 4 ? 'bg-warning' :
                                                    (task.commentDays >= 0 ? 'bg-success' : 'bg-dark')))}"
                                                  th:text="${task.commentDays}">0.0</span>
                                            <span th:unless="${task.commentDays != null}">-</span>
                                        </td>
                                        <td th:text="${task.createdDate}" style="white-space: nowrap;">2024-01-01 12:00:00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页控件 -->
                        <div class="d-flex justify-content-center mt-4" th:if="${delegatedTasks.totalPages > 0}">
                            <nav aria-label="Page navigation">
                                <ul class="pagination">
                                    <li class="page-item" th:classappend="${delegatedTasks.first ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/delegated-tasks(page=0)}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item" th:classappend="${delegatedTasks.first ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/delegated-tasks(page=${delegatedTasks.number - 1})}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item" th:each="i : ${#numbers.sequence(0, delegatedTasks.totalPages - 1)}"
                                        th:classappend="${i == delegatedTasks.number ? 'active' : ''}">
                                        <a class="page-link" th:href="@{/delegated-tasks(page=${i})}" th:text="${i + 1}">1</a>
                                    </li>
                                    <li class="page-item" th:classappend="${delegatedTasks.last ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/delegated-tasks(page=${delegatedTasks.number + 1})}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item" th:classappend="${delegatedTasks.last ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/delegated-tasks(page=${delegatedTasks.totalPages - 1})}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script th:src="@{/js/history-tracker.js}"></script>
</body>
</html>