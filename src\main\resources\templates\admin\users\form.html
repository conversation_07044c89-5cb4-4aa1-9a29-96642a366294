<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head(${user.userId == null ? '新建用户' : '编辑用户'})}">
    <meta charset="UTF-8">
    <title>新建/编辑用户</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" th:text="${user.userId == null ? '新建用户' : '编辑用户'}">新建/编辑用户</h1>
                </div>

                <!-- 消息提示 -->
                <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${message}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 表单 -->
                <div class="row">
                    <div class="col-md-12">
                        <form th:action="@{/system/users/save}" method="post" th:object="${user}" class="needs-validation" novalidate>
                            <input type="hidden" th:field="*{userId}" />

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" th:field="*{username}" required th:readonly="${user.userId != null}">
                                    <div class="invalid-feedback">请输入用户名</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="password" class="form-label">
                                        密码 <span class="text-danger" th:if="${user.userId == null}">*</span>
                                        <small class="text-muted" th:if="${user.userId != null}">(留空表示不修改)</small>
                                    </label>
                                    <input type="password" class="form-control" id="password" th:field="*{password}" th:required="${user.userId == null}">
                                    <div class="invalid-feedback">请输入密码</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="role" class="form-label">角色 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="role" th:field="*{role}" required>
                                        <option value="">-- 请选择角色 --</option>
                                        <option th:each="roleOption : ${roles}"
                                                th:value="${roleOption}"
                                                th:text="${roleOption.name() == 'ADMIN' ? '管理员' :
                                                         (roleOption.name() == 'MANAGER' ? '经理' : '操作员')}">
                                            角色
                                        </option>
                                    </select>
                                    <div class="invalid-feedback">请选择角色</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary">保存</button>
                                    <a th:href="@{/admin/users}" class="btn btn-secondary">取消</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
    </div>

    <script>
        // 表单验证
        (function() {
            'use strict';

            // 获取所有需要验证的表单
            var forms = document.querySelectorAll('.needs-validation');

            // 循环并阻止提交
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }

                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html>