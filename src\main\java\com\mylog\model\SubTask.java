package com.mylog.model;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Entity
@Table(name = "SubTasks")
@Data
public class SubTask {
    
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long subTaskId;
    
    @Column(nullable = false)
    private Long taskId;
    
    @Column(nullable = false)
    private Integer sequenceNumber;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String logContent;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String CreatedDate;
    
    @Column(nullable = true, length = 50)
    private String createdBy;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "taskId", referencedColumnName = "taskId", insertable = false, updatable = false)
    private ProjectTask task;

    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.CreatedDate = formatDateTime(dateTime);
    }
    
    public void setCreatedDate(String dateStr) {
        this.CreatedDate = dateStr;
    }
    
    public String getCreatedDate() {
        return this.CreatedDate;
    }
    
    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.CreatedDate);
    }
    
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, formatter);
        } catch (Exception e) {
            return null;
        }
    }
    
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(formatter) : null;
    }
}