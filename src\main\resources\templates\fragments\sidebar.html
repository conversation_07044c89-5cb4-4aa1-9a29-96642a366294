﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head>
    <meta charset="UTF-8">
</head>

<body>
    <!-- 通用侧边栏导航组件 -->
    <nav th:fragment="sidebar" id="sidebar" class="col-md-2 col-lg-1 d-md-block bg-dark sidebar">
        <div class="position-sticky pt-3">
            <div class="d-flex justify-content-between align-items-center px-3 mb-2">
                <div class="text-center">
                    <h5 class="text-white mb-1 sidebar-title">项目管理系统</h5>
                    <p class="text-white-50 mb-2 px-1 sidebar-username" sec:authentication="name">用户名</p>
                </div>
                <button id="sidebarCollapseBtn" class="btn btn-sm btn-outline-light sidebar-collapse-btn"
                    title="收起/展开侧边栏" data-bs-toggle="tooltip" data-bs-placement="right">
                    <i class="bi bi-chevron-left"></i>
                </button>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'dashboard' ? 'active bg-primary' : ''}"
                        th:href="@{/dashboard}">
                        <i class="bi bi-speedometer2 me-2"></i><span>仪表盘</span>
                    </a>
                </li>
                <li class="nav-item my-work-parent">
                    <a class="nav-link text-white toggle-submenu my-work-toggle" href="#" data-bs-toggle="collapse"
                        data-bs-target="#myWorkSubmenu" aria-expanded="true" aria-controls="myWorkSubmenu">
                        <i class="bi bi-briefcase me-2"></i><span>我的工作</span>
                        <i class="bi bi-chevron-up float-end my-work-arrow"></i>
                    </a>
                    <div class="collapse show" id="myWorkSubmenu">
                        <ul class="nav flex-column ms-3 my-work-submenu">
                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'mytasks' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/tasks/order-tasks}">
                                    <i class="bi bi-list-task me-2"></i><span>订单任务</span>
                                    <span th:if="${inProgressTaskCount != null && inProgressTaskCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1"
                                        th:text="${inProgressTaskCount}">0</span>
                                </a>
                            </li>


                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'difficulttasks' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/tasks/difficult}">
                                    <i class="bi bi-exclamation-triangle me-2"></i><span>难点焦点</span>
                                    <span
                                        th:if="${inProgressDifficultTaskCount != null && inProgressDifficultTaskCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1"
                                        th:text=" ${inProgressDifficultTaskCount}">0</span>
                                </a>
                            </li>
                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'specialtasks' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/tasks/special}">
                                    <i class="bi bi-star me-2"></i><span>专项任务</span>
                                    <span
                                        th:if="${inProgressSpecialTaskCount != null && inProgressSpecialTaskCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1"
                                        th:text="${inProgressSpecialTaskCount}">0</span>
                                </a>
                            </li>
                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'delegatedTasks' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/delegated-tasks}">
                                    <i class="bi bi-person-workspace me-2"></i><span>分管任务</span>
                                    <span
                                        th:if="${inProgressDelegatedTaskCount != null && inProgressDelegatedTaskCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1"
                                        th:text="${inProgressDelegatedTaskCount}">0</span>
                                </a>
                            </li>
                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'trainingTasks' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/tasks/training}">
                                    <i class="bi bi-mortarboard me-2"></i><span>教育培训</span>
                                    <span
                                        th:if="${inProgressTrainingTaskCount != null && inProgressTrainingTaskCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1"
                                        th:text="${inProgressTrainingTaskCount}">0</span>
                                </a>
                            </li>
                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'myprojects' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/projects/my}">
                                    <i class="bi bi-briefcase me-2"></i><span>我的项目</span>
                                    <span th:if="${myInProgressProjectCount != null && myInProgressProjectCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1"
                                        th:text="${myInProgressProjectCount}">0</span>
                                </a>
                            </li>

                            <li class="nav-item my-work-item" sec:authorize="hasRole('ADMIN')">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'checkin' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/check-in}">
                                    <i class="bi bi-check-circle me-2"></i><span>签到管理</span>
                                    <span th:if="${pendingCheckInCount != null && pendingCheckInCount > 0}"
                                        class="badge bg-warning rounded-pill ms-1"
                                        th:text="${pendingCheckInCount}">0</span>
                                </a>
                            </li>

                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'myworkflow' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/workflow/my}">
                                    <i class="bi bi-diagram-3 me-2"></i><span>我的流程</span>
                                    <span th:if="${myProcessingWorkflowCount != null && myProcessingWorkflowCount > 0}"
                                        class="badge bg-primary rounded-pill ms-1" title="处理中"
                                        th:text="${myProcessingWorkflowCount}">0</span>
                                    <span th:if="${todoTasksCount != null && todoTasksCount > 0}"
                                        class="badge bg-mycolor rounded-pill ms-1" title="待审批"
                                        th:text="${todoTasksCount}">0</span>
                                </a>
                            </li>

                            <li class="nav-item my-work-item">
                                <a class="nav-link text-white"
                                    th:classappend="${activeMenu == 'my-points' ? 'active bg-white bg-opacity-25' : ''}"
                                    th:href="@{/my-points}">
                                    <i class="bi bi-star me-2"></i><span>我的积分</span>
                                    <span
                                        th:if="${session.recentRewardPenaltyCount != null && session.recentRewardPenaltyCount > 0}"
                                        class="badge bg-danger rounded-pill ms-1"
                                        th:text="${session.recentRewardPenaltyCount}" title="最近7天有新的奖罚记录">0</span>
                                </a>
                            </li>

                        </ul>
                    </div>
                </li>
                <li class="nav-item" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'projects' ? 'active bg-primary' : ''}" th:href="@{/projects}">
                        <i class="bi bi-kanban me-2"></i><span>项目管理</span>

                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'taskmanagement' ? 'active bg-primary' : ''}"
                        th:href="@{/tasks/management}">
                        <i class="bi bi-clipboard2-check me-2"></i><span>任务管理</span>

                    </a>
                </li>
                <li class="nav-item" th:if="${#authorization.expression('hasRole(''ADMIN'')') or #authentication.name == '邓利鹏'}">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'commentmanagement' ? 'active bg-primary' : ''}"
                        th:href="@{/subtasks/management}">
                        <i class="bi bi-chat-dots me-2"></i><span>评论管理</span>
                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasAnyRole('ADMIN')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'work-hours' ? 'active bg-primary' : ''}"
                        th:href="@{/work-hours}">
                        <i class="bi bi-clock-history me-2"></i><span>工期管理</span>
                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'reports' ? 'active bg-primary' : ''}" th:href="@{/reports}">
                        <i class="bi bi-bar-chart me-2"></i><span>报表查询</span>
                    </a>
                </li>

                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'workflow' ? 'active bg-primary' : ''}" th:href="@{/workflow}">
                        <i class="bi bi-diagram-2 me-2"></i><span>流程管理</span>
                        <span th:if="${processingInstanceCount != null && processingInstanceCount > 0}"
                            class="badge bg-primary rounded-pill ms-1" th:text="${processingInstanceCount}">0</span>
                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'points-management' ? 'active bg-primary' : ''}"
                        th:href="@{/points-management}">
                        <i class="bi bi-award me-2"></i><span>积分管理</span>
                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link text-white" th:classappend="${activeMenu == 'users' ? 'active bg-primary' : ''}"
                        th:href="@{/admin/users}">
                        <i class="bi bi-people me-2"></i><span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'activitylogs' ? 'active bg-primary' : ''}"
                        th:href="@{/admin/activity-logs}">
                        <i class="bi bi-activity me-2"></i><span>活动日志</span>
                    </a>
                </li>


                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'calendar' ? 'active bg-primary' : ''}" th:href="@{/calendar}">
                        <i class="bi bi-calendar3 me-2"></i><span>日程管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'messages' ? 'active bg-primary' : ''}" th:href="@{/messages}">
                        <i class="bi bi-envelope me-2"></i><span>消息</span>
                        <span th:if="${unreadMessageCount != null && unreadMessageCount > 0}"
                            class="badge bg-danger rounded-pill ms-1" th:text="${unreadMessageCount}">0</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white"
                        th:classappend="${activeMenu == 'settings' ? 'active bg-primary' : ''}" th:href="@{/settings}">
                        <i class="bi bi-gear me-2"></i><span>设置</span>
                    </a>
                </li>
                <li class="nav-item">
                    <form th:action="@{/logout}" method="post">
                        <button type="submit" class="nav-link text-white border-0 bg-transparent w-100 text-start">
                            <i class="bi bi-box-arrow-right me-2"></i><span>退出登录</span>
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>
</body>

</html>