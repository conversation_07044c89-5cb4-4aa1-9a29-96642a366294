<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('系统错误')}">
    <meta charset="UTF-8">
    <title>系统错误</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">系统错误</h1>
            <div>
                <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> 返回上一页
                </button>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>发生错误</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h4 class="alert-heading">操作无法完成</h4>
                            <p th:text="${error}" class="mb-0">系统处理请求时发生未知错误。</p>
                        </div>
                        
                        <div class="mt-4">
                            <p>您可以：</p>
                            <ul>
                                <li>返回<a href="javascript:history.back()">上一页</a></li>
                                <li>返回<a th:href="@{/}">首页</a></li>
                                <li th:if="${activeMenu == 'workflow'}">返回<a th:href="@{/workflow}">流程管理</a>页面</li>
                                <li th:if="${activeMenu == 'myworkflow'}">返回<a th:href="@{/workflow/my}">我的流程</a>页面</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>