package com.mylog.service.impl;

import com.mylog.model.Submit2;
import com.mylog.repository.Submit2Repository;
import com.mylog.service.Submit2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class Submit2ServiceImpl implements Submit2Service {
    
    @Autowired
    private Submit2Repository submit2Repository;
    
    @Override
    public Submit2 saveSubmit(Submit2 submit) {
        return submit2Repository.save(submit);
    }
    
    @Override
    public Optional<Submit2> findSubmitById(Long id) {
        return submit2Repository.findById(id);
    }
    
    @Override
    public void deleteSubmit(Long id) {
        submit2Repository.deleteById(id);
    }
    
    @Override
    public Page<Submit2> findSubmitsByTaskId(Long taskId, Pageable pageable) {
        return submit2Repository.findByTaskId(taskId, pageable);
    }
    
    @Override
    public List<Submit2> findAllSubmitsByTaskId(Long taskId) {
        return submit2Repository.findByTaskId(taskId);
    }
    
    @Override
    public long countSubmitsByTaskId(Long taskId) {
        return submit2Repository.countByTaskId(taskId);
    }
} 