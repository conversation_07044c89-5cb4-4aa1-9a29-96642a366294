package com.mylog.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mylog.config.LocalDateTimeAttributeConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 事件签到实体类
 */
@Entity
@Table(name = "event_check_ins")
public class EventCheckIn {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "事件提醒ID不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_reminder_id", nullable = false)
    private EventReminder eventReminder;
    
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @NotNull(message = "签到时间不能为空")
    @Column(name = "check_in_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime checkInTime;
    
    @Column(name = "latitude")
    private Double latitude;
    
    @Column(name = "longitude")
    private Double longitude;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "created_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime createdTime;
    
    // 构造函数
    public EventCheckIn() {
        this.createdTime = com.mylog.util.DateTimeUtils.nowInChina();
        this.checkInTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    public EventCheckIn(EventReminder eventReminder, Long userId) {
        this();
        this.eventReminder = eventReminder;
        this.userId = userId;
    }
    
    // PrePersist
    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = com.mylog.util.DateTimeUtils.nowInChina();
        }
        if (checkInTime == null) {
            checkInTime = com.mylog.util.DateTimeUtils.nowInChina();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public EventReminder getEventReminder() {
        return eventReminder;
    }
    
    public void setEventReminder(EventReminder eventReminder) {
        this.eventReminder = eventReminder;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCheckInTime() {
        return checkInTime;
    }
    
    public void setCheckInTime(LocalDateTime checkInTime) {
        this.checkInTime = checkInTime;
    }
    
    public Double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    
    public Double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
}
