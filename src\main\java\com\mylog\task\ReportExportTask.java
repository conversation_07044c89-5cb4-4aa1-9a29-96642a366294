package com.mylog.task;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.mylog.model.Message;
import com.mylog.model.ProjectTask;
import com.mylog.model.ReportExportConfig;
import com.mylog.model.RewardPenaltyRecord;
import com.mylog.service.MessageService;
import com.mylog.service.ReportExportConfigService;
import com.mylog.service.ReportService;
import com.mylog.service.RewardPenaltyRecordService;
import com.mylog.service.UserService;
import com.mylog.util.WeixinMessageUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ReportExportTask {

    private ReportService reportService;
    private ReportExportConfigService reportExportConfigService;
    private MessageService messageService;
    private UserService userService;
    private WeixinMessageUtil weixinMessageUtil;
    private RewardPenaltyRecordService rewardPenaltyRecordService;

    @Autowired
    public ReportExportTask(
            ReportService reportService,
            ReportExportConfigService reportExportConfigService,
            MessageService messageService,
            UserService userService,
            WeixinMessageUtil weixinMessageUtil,
            RewardPenaltyRecordService rewardPenaltyRecordService) {
        this.reportService = reportService;
        this.reportExportConfigService = reportExportConfigService;
        this.messageService = messageService;
        this.userService = userService;
        this.weixinMessageUtil = weixinMessageUtil;
        this.rewardPenaltyRecordService = rewardPenaltyRecordService;
    }

    // 添加一个在下一个整点执行的测试任务
    @Scheduled(cron = "0 0 * * * ?") // 每小时整点执行一次检查
    public void testHourlyTask() {
        // 手动调用导出任务，测试是否可以正常执行
        try {
            exportViolationTasksReport(true, false);
        } catch (Exception e) {
            log.error("手动调用 exportViolationTasksReport 方法时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 导出违规任务报表，可控制是否发送微信消息
     * 
     * @param isSendWeixin  是否向微信发送消息
     * @param isForceExport 是否强制导出，为true时将跳过时间和条件检查
     * @return 导出的文件路径，如果导出失败或不需要导出，则返回null
     */
    public String exportViolationTasksReport(boolean isSendWeixin, boolean isForceExport) {
        log.debug("定时任务 exportViolationTasksReport 开始执行，当前时间: {}，发送微信消息: {}, 强制导出: {}",
                LocalDateTime.now(), isSendWeixin, isForceExport);
        try {
            // 获取导出配置
            Optional<ReportExportConfig> configOpt = reportExportConfigService.getConfig();
            if (!configOpt.isPresent()) {
                log.warn("导出配置未找到，跳过本次导出");
                return null;
            }
            ReportExportConfig config = configOpt.get(); // 判断是否为强制导出
            LocalDateTime now = LocalDateTime.now();

            // 如果不是强制导出，则进行条件检查
            if (!isForceExport) {
                // 检查是否启用自动导出
                if (!config.isEnabled()) {
                    log.info("自动导出未启用，跳过本次导出");
                    return null;
                }

                // 检查是否应该今天导出
                if (!reportExportConfigService.shouldExportToday()) {
                    log.info("今天不需要导出报表，跳过本次导出");
                    return null;
                }

                // 检查当前时间是否与配置的导出时间匹配
                int currentHour = now.getHour();
                LocalTime ExportTime = config.getExportTime();
                int configHour = ExportTime.getHour();

                // 只在配置的小时执行导出
                if (currentHour != configHour) {
                    log.info("当前时间 {} 点不是配置的导出时间 {} 点，跳过本次导出", currentHour, configHour);
                    return null;
                }

                // 检查是否已经导出过
                LocalDateTime lastExportTime = config.getLastExportTime();
                if (lastExportTime != null) {
                    // 获取最后导出时间的日期和小时
                    java.time.LocalDate lastExportDate = lastExportTime.toLocalDate();
                    int lastExportHour = lastExportTime.getHour();

                    // 如果最后导出的日期是今天，并且导出时间的小时与配置的导出时间相同，则跳过
                    if (lastExportDate.equals(now.toLocalDate()) && lastExportHour == configHour) {
                        log.info("今天已经在配置的时间 {} 点导出过报表，跳过本次导出", configHour);
                        return null;
                    }
                }
            } else {
                log.info("已开启强制导出模式，跳过时间和条件检查，直接执行导出");
            }

            // 获取违规任务列表
            List<ProjectTask> violationTasks = reportService.getViolationTasks();

            // 获取导出路径
            String exportPath = config.getExportPath();
            if (exportPath == null || exportPath.trim().isEmpty()) {
                exportPath = System.getProperty("user.home") + File.separator + "reports";
            }

            // 生成带时间戳的文件名
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = String.format("violation_tasks_report_%s.xlsx", timestamp);

            // 确保导出目录存在
            File exportDir = new File(exportPath);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }

            // 创建工作簿和工作表
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("违规任务列表");            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = { "任务ID", "任务名称", "所属项目", "负责人", "风险", "进度", "状态", "评论天数", "创建时间" };
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }            // 使用StringBuilder构建内容，更高效
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("| 负责人 | 评论天数 | 任务ID | 任务名称 | 项目名称 |\n");            // 填充数据行
            int rowNum = 1;
            for (ProjectTask task : violationTasks) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(task.getTaskId());
                row.createCell(1).setCellValue(task.getTaskName());
                row.createCell(2).setCellValue(task.getProject() != null ? task.getProject().getProjectName() : "");
                row.createCell(3).setCellValue(task.getResponsible());
                row.createCell(4).setCellValue(task.getRisk());
                row.createCell(5).setCellValue(task.getRatio() != null ? task.getRatio() * 100 + "%" : "0%");
                row.createCell(6).setCellValue(task.getStatus());
                row.createCell(7).setCellValue(task.getCommentDays() != null ? task.getCommentDays() : 0);
                row.createCell(8).setCellValue(
                        task.getCreatedDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

                // 添加到Markdown表格中
                String responsible = task.getResponsible();
                String weixinID = responsible; // 默认使用用户名

                if (responsible != null && !responsible.contains("未分配")) {
                    Optional<com.mylog.model.user.User> userOpt = userService.findUserByUsername(responsible);
                    if (userOpt.isPresent() && userOpt.get().getWeixinID() != null
                            && !userOpt.get().getWeixinID().isEmpty()) {
                        weixinID = userOpt.get().getWeixinID();
                        log.debug("找到用户 {} 的微信ID: {}", responsible, weixinID);
                    } else {
                        log.warn("未找到用户 {} 的微信ID，使用用户名代替", responsible);
                    }                    contentBuilder.append(" |<@").append(weixinID)
                            .append("> | ").append("<font color='warning'>")
                            .append(task.getCommentDays() != null ? task.getCommentDays() : 0).append("</font>")
                            .append("| ").append(task.getTaskId())
                            .append("| ").append(task.getTaskName())
                            .append(" | ").append(task.getProject() != null ? task.getProject().getProjectName() : "")
                            .append(" |\n");
                    // 同时对每一条违规任务的负责人进行惩罚，并创建记录保存到rewardPenaltyRecord表中
                    try {
                        // 从备注字段中解析违规扣分信息
                        int penaltyPoints = parseViolationScoreFromRemarks(task.getRemarks());
                        String violationRuleName = parseViolationRuleNameFromRemarks(task.getRemarks());
                        int commentDays = task.getCommentDays() != null ? task.getCommentDays().intValue() : 0;

                        // 创建奖罚记录对象
                        RewardPenaltyRecord penaltyRecord = new RewardPenaltyRecord();
                        penaltyRecord.setName(responsible);
                        penaltyRecord.setType("任务违规");

                        penaltyRecord.setReason("违规任务：" + task.getTaskName() +
                                "/" + (task.getProject() != null ? task.getProject().getProjectName() : "无") +
                                "，任务ID：" + task.getTaskId());
                        penaltyRecord.setRemarks((task.getRemarks() != null && !task.getRemarks().isEmpty()
                                ? task.getRemarks()  : ""));
                        penaltyRecord.setPoints(penaltyPoints);

                        // 使用服务保存记录（自动计算存量积分）
                        rewardPenaltyRecordService.createRecordWithTotalPoints(penaltyRecord);

                        log.info("为用户 {} 创建违规任务惩罚记录，扣除 {} 积分", responsible, Math.abs(penaltyPoints));
                    } catch (Exception e) {
                        log.error("创建违规任务惩罚记录时发生错误: {}", e.getMessage(), e);
                    }
                }
            } // 添加导出时间
            contentBuilder.append("\n< 统计时间：")
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            // contentBuilder.append("\n@刘红阳").append("@kh");

            String content = contentBuilder.toString();
            // 根据参数决定是否发送微信消息
            if (isSendWeixin) {
                log.info("准备发送微信消息通知，isSendWeixin={}", isSendWeixin);
                List<String> mentionlist = new ArrayList<>();
                mentionlist.add("@all");
                weixinMessageUtil.sendWeixinMessage("违规任务列表", content, mentionlist);
                log.info("已发送微信消息通知");
            } else {
                log.info("根据参数设置(isSendWeixin={})，跳过发送微信消息通知", isSendWeixin);
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 保存文件到本地
            File file = new File(exportDir, filename);
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
                log.info("违规任务报表已导出到: {}", file.getAbsolutePath());
            }

            // 更新最后导出时间
            reportExportConfigService.updateLastExportTime(); // 发送成功通知消息
            sendNotificationMessage(
                    reportExportConfigService.getConfig(),
                    true,
                    file.getAbsolutePath(),
                    violationTasks.size(),
                    now,
                    null);

            // 记录成功日志
            log.info("违规任务报表导出完成 - 文件路径: {}, 导出任务数: {}",
                    file.getAbsolutePath(),
                    violationTasks.size());
            workbook.close();

            // 返回导出的文件路径
            return file.getAbsolutePath();
        } catch (IOException e) {
            log.error("导出违规任务报表时发生IO错误: {}", e.getMessage(), e);
            sendErrorNotification("文件IO错误", e);
            return null;
        } catch (Exception e) {
            log.error("导出违规任务报表时发生未预期错误: {}", e.getMessage(), e);
            sendErrorNotification("未预期错误", e);
            return null;
        }
    }

    /**
     * 发送错误通知
     * 
     * @param errorType 错误类型
     * @param e         异常
     */
    private void sendErrorNotification(String errorType, Exception e) {
        try {
            Optional<ReportExportConfig> configOpt = reportExportConfigService.getConfig();
            if (configOpt.isPresent()) {
                // 创建错误消息内容（Markdown格式）
                String errorContent = String.format(
                        "## <font color=\"warning\">违规任务报表导出失败</font>\n\n" +
                                "**错误类型**: %s\n" +
                                "**错误信息**: %s\n" +
                                "**发生时间**: %s",
                        errorType,
                        e.getMessage(),
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 发送系统消息
                Message errorMessage = new Message();
                errorMessage.setReceiver(configOpt.get().getNotificationReceiver());
                errorMessage.setMessageTitle("违规任务报表导出失败");
                errorMessage.setMessageContent(errorContent);
                errorMessage.setRelatedType("Report");
                errorMessage.setRelatedId(0L); // 设置一个默认值，因为该字段不能为空
                errorMessage.setRead(false);
                errorMessage.setCreatedDate(LocalDateTime.now());

                messageService.saveMessage(errorMessage);
                log.info("已发送导出失败通知");
            }
        } catch (Exception ex) {
            log.error("发送失败通知消息时发生错误", ex);
        }
    }

    /**
     * 发送通知消息
     */
    private void sendNotificationMessage(
            Optional<ReportExportConfig> configOpt,
            boolean isSuccess,
            String filePath,
            int taskCount,
            LocalDateTime exportTime,
            Exception error) {

        if (!configOpt.isPresent()) {
            log.warn("未找到报表导出配置，无法发送通知");
            return;
        }

        try {
            Message message = new Message();
            message.setReceiver(configOpt.get().getNotificationReceiver());

            if (isSuccess) {
                message.setMessageTitle("违规任务报表导出成功");
                message.setMessageContent(String.format(
                        "违规任务报表已成功导出\n\n" +
                                "文件路径: %s\n" +
                                "导出任务数: %d\n" +
                                "导出时间: %s",
                        filePath,
                        taskCount,
                        exportTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));

            } else {
                message.setMessageTitle("违规任务报表导出失败");
                message.setMessageContent(String.format(
                        "违规任务报表导出失败\n\n" +
                                "错误信息: %s\n" +
                                "发生时间: %s",
                        error.getMessage(),
                        exportTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));

            }

            message.setRelatedType("Report");
            message.setRelatedId(0L); // 设置一个默认值，因为该字段不能为空
            message.setRead(false);
            message.setCreatedDate(exportTime);

            messageService.saveMessage(message);

            // 同时发送企业微信消息
            List<String> mentionedList = new ArrayList<>();
            mentionedList.add(configOpt.get().getNotificationReceiver());
            mentionedList.add("@all"); // @所有人
        } catch (Exception e) {
            log.error("发送通知消息时发生错误", e);
        }
    }

    /**
     * 从备注字段中解析违规扣分信息
     * 格式：[违规扣分:X分-规则:规则名称]
     * 
     * @param remarks 备注字段
     * @return 扣分积分（负数），如果解析失败则返回-10（默认值）
     */
    private int parseViolationScoreFromRemarks(String remarks) {
        if (remarks == null || remarks.isEmpty()) {
            return -10; // 默认扣分
        }

        try {
            // 使用正则表达式匹配格式：[违规扣分:数字分-规则:规则名称]
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\[违规扣分:(\\d+)分-规则:.*?\\]");
            java.util.regex.Matcher matcher = pattern.matcher(remarks);

            if (matcher.find()) {
                int score = Integer.parseInt(matcher.group(1));
                return -score; // 返回负数表示扣分
            }
        } catch (Exception e) {
            log.warn("解析备注中的违规扣分信息失败: {}, 使用默认扣分", remarks, e);
        }

        return -10; // 默认扣分
    }

    /**
     * 从备注字段中解析违规规则名称
     * 格式：[违规扣分:X分-规则:规则名称]
     * 
     * @param remarks 备注字段
     * @return 规则名称，如果解析失败则返回null
     */
    private String parseViolationRuleNameFromRemarks(String remarks) {
        if (remarks == null || remarks.isEmpty()) {
            return null;
        }

        try {
            // 使用正则表达式匹配格式：[违规扣分:数字分-规则:规则名称]
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\[违规扣分:\\d+分-规则:(.*?)\\]");
            java.util.regex.Matcher matcher = pattern.matcher(remarks);

            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.warn("解析备注中的违规规则名称失败: {}", remarks, e);
        }

        return null;
    }
}