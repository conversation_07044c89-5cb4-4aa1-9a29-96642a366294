package com.mylog.repository;

import com.mylog.model.WorkHoursLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工期登记Repository接口
 * 提供对工期登记表的数据库操作
 */
@Repository
public interface WorkHoursLogRepository extends JpaRepository<WorkHoursLog, Long> {
    
    /**
     * 根据业务类型查找工期记录
     * @param businessType 业务类型
     * @return 工期记录列表
     */
    List<WorkHoursLog> findByBusinessTypeOrderByCreatedTimeDesc(String businessType);
    
    /**
     * 根据业务ID查找工期记录
     * @param businessId 业务ID
     * @return 工期记录列表
     */
    List<WorkHoursLog> findByBusinessIdOrderByCreatedTimeDesc(Integer businessId);
    
    /**
     * 根据业务类型和业务ID查找工期记录
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 工期记录列表
     */
    List<WorkHoursLog> findByBusinessTypeAndBusinessIdOrderByCreatedTimeDesc(String businessType, Integer businessId);
    
    /**
     * 根据业务类型和业务ID查找工期记录（分页）
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    Page<WorkHoursLog> findByBusinessTypeAndBusinessIdOrderByCreatedTimeDesc(String businessType, Integer businessId, Pageable pageable);
    
    /**
     * 根据业务类型查找工期记录（分页）
     * @param businessType 业务类型
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    Page<WorkHoursLog> findByBusinessTypeOrderByCreatedTimeDesc(String businessType, Pageable pageable);
    
    /**
     * 根据创建时间范围查找工期记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工期记录列表
     */
    @Query("SELECT w FROM WorkHoursLog w WHERE w.createdTime >= :startTime AND w.createdTime <= :endTime ORDER BY w.createdTime DESC")
    List<WorkHoursLog> findByCreatedTimeRangeOrderByCreatedTimeDesc(@Param("startTime") String startTime, @Param("endTime") String endTime);
    
    /**
     * 根据创建时间范围查找工期记录（分页）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    @Query("SELECT w FROM WorkHoursLog w WHERE w.createdTime >= :startTime AND w.createdTime <= :endTime ORDER BY w.createdTime DESC")
    Page<WorkHoursLog> findByCreatedTimeRangeOrderByCreatedTimeDesc(@Param("startTime") String startTime, @Param("endTime") String endTime, Pageable pageable);
    
    /**
     * 根据业务类型和时间范围查找工期记录
     * @param businessType 业务类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工期记录列表
     */
    @Query("SELECT w FROM WorkHoursLog w WHERE w.businessType = :businessType AND w.createdTime >= :startTime AND w.createdTime <= :endTime ORDER BY w.createdTime DESC")
    List<WorkHoursLog> findByBusinessTypeAndCreatedTimeRangeOrderByCreatedTimeDesc(@Param("businessType") String businessType, @Param("startTime") String startTime, @Param("endTime") String endTime);
    
    /**
     * 统计某个业务的工期记录数量
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 记录数量
     */
    @Query("SELECT COUNT(w) FROM WorkHoursLog w WHERE w.businessType = :businessType AND w.businessId = :businessId")
    Long countByBusinessTypeAndBusinessId(@Param("businessType") String businessType, @Param("businessId") Integer businessId);
      /**
     * 获取某个业务的最新累计工期
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 最新的累计工期，如果没有记录则返回null
     */
    @Query("SELECT w.daysActual FROM WorkHoursLog w WHERE w.businessType = :businessType AND w.businessId = :businessId ORDER BY w.createdTime DESC LIMIT 1")
    Double getLatestHoursInventory(@Param("businessType") String businessType, @Param("businessId") Integer businessId);
      /**
     * 计算某个业务的工期变化总和
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 工期变化总和
     */
    @Query("SELECT COALESCE(SUM(w.daysChange), 0.0) FROM WorkHoursLog w WHERE w.businessType = :businessType AND w.businessId = :businessId")
    Double calculateTotalHoursChange(@Param("businessType") String businessType, @Param("businessId") Integer businessId);
}
