@echo off
chcp 65001 >nul

echo 正在创建数据库自动备份计划任务...

:: 获取脚本当前目录
set SCRIPT_DIR=%~dp0

:: 任务名称
set TASK_NAME=MyLog-DB-Backup

:: 删除已存在的任务（如果有）
schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1

:: 创建新的计划任务
schtasks /create ^
    /tn "%TASK_NAME%" ^
    /tr "\"%SCRIPT_DIR%backup-databases.bat\"" ^
    /sc daily ^
    /st 22:00 ^
    /ru "SYSTEM" ^
    /rl highest ^
    /f

if %errorlevel% equ 0 (
    echo 数据库自动备份任务创建成功！
    echo 任务名称: %TASK_NAME%
    echo 执行时间: 每天晚上22:00
    echo 执行脚本: %SCRIPT_DIR%backup-databases.bat
    echo.
    echo 您可以通过以下方式管理此任务：
    echo 1. 查看任务状态: schtasks /query /tn "%TASK_NAME%"
    echo 2. 手动运行任务: schtasks /run /tn "%TASK_NAME%"
    echo 3. 删除任务: schtasks /delete /tn "%TASK_NAME%" /f
    echo.
    echo 备份文件将保存在: %SCRIPT_DIR%dataBk\
    echo 备份日志将保存在: %SCRIPT_DIR%dataBk\backup.log
) else (
    echo 创建计划任务失败！请检查权限或手动创建。
    echo 注意：需要管理员权限才能创建系统计划任务。
)

pause
