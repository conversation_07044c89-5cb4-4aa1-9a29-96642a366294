package com.mylog.service;

import com.mylog.dto.CalendarEventDTO;
import com.mylog.model.CalendarEvent;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 日历事件服务接口
 */
public interface CalendarEventService {
    
    /**
     * 创建事件
     */
    CalendarEventDTO createEvent(CalendarEventDTO eventDTO);
    
    /**
     * 更新事件
     */
    CalendarEventDTO updateEvent(Long id, CalendarEventDTO eventDTO);
    
    /**
     * 删除事件
     */
    void deleteEvent(Long id);
    
    /**
     * 根据ID获取事件
     */
    Optional<CalendarEventDTO> getEventById(Long id);
    
    /**
     * 根据日历ID获取事件列表
     */
    List<CalendarEventDTO> getEventsByCalendarId(Long calendarId);
    
    /**
     * 根据用户ID获取事件列表
     */
    List<CalendarEventDTO> getEventsByUserId(Long userId);
    
    /**
     * 根据时间范围获取事件
     */
    List<CalendarEventDTO> getEventsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据用户可访问的日历和时间范围获取事件（包括共享日历事件）
     */
    List<CalendarEventDTO> getAccessibleEventsByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据日历ID和时间范围获取事件
     */
    List<CalendarEventDTO> getEventsByCalendarIdAndTimeRange(Long calendarId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据用户ID和时间范围获取事件
     */
    List<CalendarEventDTO> getEventsByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据多个日历ID和时间范围获取事件
     */
    List<CalendarEventDTO> getEventsByCalendarIdsAndTimeRange(List<Long> calendarIds, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 搜索事件
     */
    List<CalendarEventDTO> searchEvents(String keyword);
    
    /**
     * 根据用户ID搜索事件
     */
    List<CalendarEventDTO> searchEventsByUserId(Long userId, String keyword);
    
    /**
     * 根据事件类型获取事件
     */
    List<CalendarEventDTO> getEventsByType(CalendarEvent.EventType eventType);
    
    /**
     * 根据优先级获取事件
     */
    List<CalendarEventDTO> getEventsByPriority(CalendarEvent.Priority priority);
    
    /**
     * 获取重复事件
     */
    List<CalendarEventDTO> getRecurringEvents();
    
    /**
     * 创建重复事件实例
     */
    List<CalendarEventDTO> createRecurringEventInstances(CalendarEventDTO eventDTO, LocalDateTime endDate);
    
    /**
     * 检查用户是否有事件的访问权限
     */
    boolean hasEventAccess(Long userId, Long eventId);
    
    /**
     * 获取今日事件
     */
    List<CalendarEventDTO> getTodayEvents(Long userId);
    
    /**
     * 获取本周事件
     */
    List<CalendarEventDTO> getWeekEvents(Long userId);
    
    /**
     * 获取本月事件
     */
    List<CalendarEventDTO> getMonthEvents(Long userId);
}
