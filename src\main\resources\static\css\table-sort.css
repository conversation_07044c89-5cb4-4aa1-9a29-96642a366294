/* 表格排序样式 */
.sortable {
    cursor: pointer !important;
    position: relative;
    user-select: none;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    padding: 12px 8px;
    border-bottom: 2px solid transparent;
}

.sortable:hover {
    background-color: rgba(13, 110, 253, 0.1);
    border-bottom-color: #0d6efd;
}

.sort-icon {
    font-size: 0.9em;
    margin-left: 8px;
    opacity: 0.8;
    transition: all 0.3s ease;
    display: inline-block;
    font-weight: bold;
}

th[data-direction="asc"] .sort-icon {
    opacity: 1 !important;
    color: #0d6efd !important;
    transform: scale(1.1);
}

th[data-direction="desc"] .sort-icon {
    opacity: 1 !important;
    color: #0d6efd !important;
    transform: scale(1.1);
}

th[data-direction="asc"], 
th[data-direction="desc"] {
    background-color: rgba(13, 110, 253, 0.15) !important;
    font-weight: 600;
    border-bottom-color: #0d6efd !important;
}

/* 确保带有数据属性的单元格正确显示 */
td[data-value] a, 
td[data-value] span {
    display: inline-block;
}

/* 排序动画效果 */
.sort-highlight {
    animation: sortHighlight 1s ease-out;
    background-color: rgba(13, 110, 253, 0.1) !important;
}

@keyframes sortHighlight {
    0% {
        background-color: rgba(13, 110, 253, 0.3);
        transform: scale(1.01);
    }
    50% {
        background-color: rgba(13, 110, 253, 0.2);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* 表格行悬停效果增强 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* 确保排序图标在所有情况下都可见 */
.bi-arrow-up, .bi-arrow-down, .bi-arrow-down-up {
    font-weight: bold !important;
    font-size: 1em !important;
}
