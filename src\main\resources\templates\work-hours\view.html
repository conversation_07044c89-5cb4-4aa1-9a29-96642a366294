<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('工期记录详情')">
    <meta charset="UTF-8">
    <title>工期记录详情</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">工期记录详情 #<span th:text="${workHours.id}">1</span></h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a th:href="@{/work-hours}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>返回列表
                    </a>
                    <a th:href="@{/work-hours/{id}/edit(id=${workHours.id})}" class="btn btn-sm btn-warning" sec:authorize="hasAnyRole('ADMIN', 'MANAGER')">
                        <i class="bi bi-pencil me-1"></i>编辑
                    </a>
                    <button type="button" class="btn btn-sm btn-danger" sec:authorize="hasRole('ADMIN')"
                            onclick="confirmDelete(this)" 
                            th:data-url="@{/work-hours/delete/{id}(id=${workHours.id})}">
                        <i class="bi bi-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- 基本信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">记录ID</label>
                                <div class="fw-bold" th:text="${workHours.id}">1</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">业务类型</label>
                                <div>
                                    <span th:class="${'badge fs-6 ' +
                                        (workHours.businessType == '项目' ? 'bg-primary' :
                                        (workHours.businessType == '任务' ? 'bg-success' :
                                        (workHours.businessType == '培训' ? 'bg-info' :
                                        (workHours.businessType == '会议' ? 'bg-warning' : 'bg-secondary'))))}"
                                        th:text="${workHours.businessType}">项目</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">业务ID</label>
                                <div class="fw-bold" th:text="${workHours.businessId}">1001</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">创建时间</label>
                                <div th:text="${workHours.createdTime}">2025-01-01 09:00:00</div>
                            </div>                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">创建人</label>
                                <div th:text="${workHours.creator ?: '未填写'}">创建人姓名</div>
                            </div>
                            <div class="col-md-6 mb-3" th:if="${workHours.bonus != null && workHours.bonus > 0}">
                                <label class="form-label text-muted">绩效分</label>
                                <div class="fw-bold text-success">
                                    ¥<span th:text="${#numbers.formatDecimal(workHours.bonus, 1, 2)}">100.00</span>
                                </div>
                            </div>
                            <div class="col-md-12 mb-3" th:if="${workHours.remark != null && !workHours.remark.isEmpty()}">
                                <label class="form-label text-muted">备注</label>
                                <div class="bg-light p-2 rounded" th:text="${workHours.remark}">备注信息</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工期信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clock me-2"></i>工期信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">累计工期</label>
                                <div class="fs-4 fw-bold text-primary">
                                    <span th:text="${#numbers.formatDecimal(workHours.hoursInventory, 1, 1)}">8.0</span> 小时
                                </div>
                                <small class="text-muted">当前该业务的累计工期</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">工期变化</label>
                                <div class="fs-4 fw-bold" th:class="${workHours.hoursChange >= 0 ? 'text-success' : 'text-danger'}">
                                    <span th:text="${workHours.hoursChange >= 0 ? '+' : ''} + ${#numbers.formatDecimal(workHours.hoursChange, 1, 1)}">+2.0</span> 小时
                                </div>
                                <small class="text-muted" th:text="${workHours.hoursChange >= 0 ? '增加的工期' : '减少的工期'}">本次记录的工期变化</small>
                            </div>
                        </div>

                        <!-- 工期变化可视化 -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex align-items-center">
                                    <span class="text-muted me-3">工期变化:</span>
                                    <div class="flex-grow-1">
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-secondary" role="progressbar" 
                                                 th:style="'width: ' + ${workHours.hoursInventory > 0 ? (workHours.hoursInventory - workHours.hoursChange) / workHours.hoursInventory * 100 : 0} + '%'"
                                                 title="变化前的工期">
                                            </div>
                                            <div th:class="${'progress-bar ' + (workHours.hoursChange >= 0 ? 'bg-success' : 'bg-danger')}" role="progressbar"
                                                 th:style="'width: ' + ${workHours.hoursInventory > 0 ? Math.abs(workHours.hoursChange) / workHours.hoursInventory * 100 : 0} + '%'"
                                                 th:title="${workHours.hoursChange >= 0 ? '新增工期' : '减少工期'}">
                                            </div>
                                        </div>
                                    </div>
                                    <span class="text-muted ms-3" th:text="${#numbers.formatDecimal(workHours.hoursInventory - workHours.hoursChange, 1, 1)} + 'h → ' + ${#numbers.formatDecimal(workHours.hoursInventory, 1, 1)} + 'h'">
                                        6.0h → 8.0h
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 原因说明 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-chat-text me-2"></i>原因说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0" style="white-space: pre-wrap;" th:text="${workHours.reason}">工期变化的详细原因说明...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="col-md-4">
                <!-- 统计信息 -->
                <div class="card mb-4" th:if="${statistics != null}">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-bar-chart me-2"></i>该业务统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <div class="h4 text-primary mb-0" th:text="${#numbers.formatDecimal(statistics.totalHours, 1, 1)}">25.5</div>
                                <small class="text-muted">累计工期</small>
                            </div>
                            <div class="col-6">
                                <div class="h5 text-success mb-0" th:text="${statistics.recordCount}">8</div>
                                <small class="text-muted">记录数</small>
                            </div>
                            <div class="col-6">
                                <div class="h5 text-info mb-0" th:text="${#numbers.formatDecimal(statistics.averageHours, 1, 1)}">3.2</div>
                                <small class="text-muted">平均工期</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 相关记录 -->
                <div class="card mb-4" th:if="${relatedRecords != null && !relatedRecords.empty}">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>相关记录</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item px-0 py-2" th:each="record : ${relatedRecords}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold small text-truncate" style="max-width: 150px;" th:text="${record.reason}" th:title="${record.reason}">原因</div>
                                        <small class="text-muted" th:text="${#temporals.format(record.createdTime, 'MM-dd HH:mm')}">01-15 09:00</small>
                                    </div>
                                    <div class="text-end">
                                        <span th:class="${record.hoursChange >= 0 ? 'badge bg-success' : 'badge bg-danger'}"
                                              th:text="${record.hoursChange >= 0 ? '+' : ''} + ${#numbers.formatDecimal(record.hoursChange, 1, 1)} + 'h'">+2.0h</span>
                                        <br>
                                        <small class="text-muted" th:text="'存量: ' + ${#numbers.formatDecimal(record.hoursInventory, 1, 1)} + 'h'">存量: 8.0h</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-2" th:if="${totalRelatedCount > 5}">
                            <a th:href="@{/work-hours(businessType=${workHours.businessType}, businessId=${workHours.businessId})}" class="btn btn-sm btn-outline-primary">
                                查看全部 <span th:text="${totalRelatedCount}">10</span> 条记录
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-list-ul me-2"></i>操作信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">记录创建</h6>
                                    <p class="timeline-text" th:text="${workHours.createdTime}">2025-01-15 09:00:00</p>
                                </div>
                            </div>
                            <!-- 可以在这里添加更多的操作历史，如编辑记录等 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 删除确认功能
        function confirmDelete(button) {
            const url = button.getAttribute('data-url');
            if (confirm('确定要删除这条工期记录吗？此操作不可恢复。')) {
                // 创建隐藏表单进行POST删除
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = url;
                
                // 添加CSRF令牌
                const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
                
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>

    <style>
        .timeline {
            position: relative;
            padding-left: 20px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 9px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -15px;
            top: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 2px solid #fff;
        }
        
        .timeline-content {
            margin-left: 10px;
        }
        
        .timeline-title {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .timeline-text {
            margin: 0;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</body>
</html>
