﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 项目管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f0f2f5;
            height: 100vh;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .login-container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .content-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1000px;
            width: 100%;
        }
        .brand-section {
            flex: 1;
            padding-right: 32px;
            max-width: 500px;
        }
        .brand-name {
            color: #1877f2;
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .brand-description {
            font-size: 1.5rem;
            line-height: 1.4;
            color: #1c1e21;
        }
        .login-form-section {
            flex: 1;
            max-width: 400px;
        }
        .login-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, .1), 0 8px 16px rgba(0, 0, 0, .1);
            padding: 1.5rem;
        }
        .form-control {
            height: 50px;
            font-size: 1.1rem;
            padding: 0.75rem;
            border-radius: 6px;
        }
        .btn-login {
            background-color: #1877f2;
            border: none;
            font-size: 1.25rem;
            font-weight: bold;
            height: 50px;
        }
        .btn-login:hover {
            background-color: #166fe5;
        }
        .divider {
            border-bottom: 1px solid #dadde1;
            margin: 20px 0;
        }
        .alert {
            border-radius: 8px;
        }
        @media (max-width: 900px) {
            .content-wrapper {
                flex-direction: column;
                text-align: center;
            }
            .brand-section {
                padding-right: 0;
                padding-bottom: 32px;
            }
            .login-form-section {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="content-wrapper">
            <!-- 品牌介绍部分 -->
            <div class="brand-section">
                <h1 class="brand-name">项目管理系统</h1>
                <p class="brand-description">
                    高效的项目管理平台，助您轻松管理任务、追踪进度、实现目标。
                </p>
            </div>
            
            <!-- 登录表单部分 -->
            <div class="login-form-section">
                <div class="login-card">
                    <form th:action="@{/login}" method="post" class="needs-validation" novalidate>
                        <!-- 错误消息显示 -->
                        <div th:if="${param.error}" class="alert alert-danger mb-4">
                            用户名或密码错误，请重试。
                        </div>
                        <div th:if="${param.logout}" class="alert alert-success mb-4">
                            您已成功退出登录。
                        </div>
                        
                        <!-- 用户名输入框 -->
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                            <label for="username">用户名</label>
                        </div>
                        
                        <!-- 密码输入框 -->
                        <div class="form-floating mb-4">
                            <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                            <label for="password">密码</label>
                        </div>
                        
                        <!-- 登录按钮 -->
                        <button type="submit" class="btn btn-primary btn-login w-100 mb-3">
                            登录
                        </button>
                        
                        <div class="divider"></div>
                        
                        <!-- 帮助信息 -->
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                如果您忘记了密码或需要帮助，请联系系统管理员。
                            </small>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    </script>
</body>
</html> 
