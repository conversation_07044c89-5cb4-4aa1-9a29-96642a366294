package com.mylog.model;

import com.mylog.config.LocalDateTimeAttributeConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 日历实体类
 */
@Entity
@Table(name = "calendars")
public class Calendar {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "日历名称不能为空")
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "color", length = 7, columnDefinition = "varchar(7) default '#007bff'")
    private String color = "#007bff";
    
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "is_default", columnDefinition = "boolean default false")
    private Boolean isDefault = false;
    
    @Column(name = "is_shared", columnDefinition = "boolean default false")
    private Boolean isShared = false;
      @Column(name = "created_time", nullable = false)
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime createdTime;
    
    @Column(name = "updated_time")
    @Convert(converter = LocalDateTimeAttributeConverter.class)
    private LocalDateTime updatedTime;
    
    @OneToMany(mappedBy = "calendar", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CalendarEvent> events = new ArrayList<>();
      // 构造函数
    public Calendar() {
        this.createdTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    public Calendar(String name, Long userId) {
        this();
        this.name = name;
        this.userId = userId;
    }
      // PrePersist and PreUpdate
    @PrePersist
    protected void onCreate() {
        createdTime = com.mylog.util.DateTimeUtils.nowInChina();
        updatedTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = com.mylog.util.DateTimeUtils.nowInChina();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Boolean getIsDefault() {
        return isDefault;
    }
    
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    public Boolean getIsShared() {
        return isShared;
    }
    
    public void setIsShared(Boolean isShared) {
        this.isShared = isShared;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public List<CalendarEvent> getEvents() {
        return events;
    }
    
    public void setEvents(List<CalendarEvent> events) {
        this.events = events;
    }
    
    // 重写toString方法，方便日志输出
    @Override
    public String toString() {
        return "Calendar{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", color='" + color + '\'' +
                ", userId=" + userId +
                ", isDefault=" + isDefault +
                ", isShared=" + isShared +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
