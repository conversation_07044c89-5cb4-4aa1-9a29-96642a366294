<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('签到管理')}" th:with="extraStyles=~{::styles}">
    <th:block th:fragment="styles">
        <link th:href="@{/css/calendar/calendar.css}" rel="stylesheet">
        <style>
            .check-in-card {
                border-radius: 12px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .check-in-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            }

            .check-in-btn {
                border-radius: 50px;
                padding: 15px 40px;
                font-size: 18px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                transition: all 0.3s ease;
                border: none;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            .check-in-btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }

            .check-in-btn.success {
                background: linear-gradient(135deg, #28a745, #34ce57);
            }

            .check-in-btn.danger {
                background: linear-gradient(135deg, #dc3545, #fd7e87);
            }

            .status-badge {
                font-size: 12px;
                padding: 6px 12px;
                border-radius: 20px;
            }

            .stats-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .stats-number {
                font-size: 2.5rem;
                font-weight: bold;
            }

            .event-item {
                background: #f8f9fa;
                border-left: 4px solid #007bff;
                padding: 15px;
                margin-bottom: 10px;
                border-radius: 8px;
                transition: all 0.2s ease;
            }

            .event-item:hover {
                background: #e9ecef;
                transform: translateX(5px);
            }

            .history-item {
                border-bottom: 1px solid #e9ecef;
                padding: 15px 0;
            }

            .history-item:last-child {
                border-bottom: none;
            }

            .location-badge {
                background: linear-gradient(45deg, #FF6B6B, #FFE66D);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.85em;
            }

            .time-badge {
                background: linear-gradient(45deg, #4ECDC4, #44A08D);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.85em;
            }

            .loading-spinner {
                display: none;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .check-in-btn {
                    padding: 12px 30px;
                    font-size: 16px;
                }

                .stats-number {
                    font-size: 2rem;
                }
            }
        </style>
    </th:block>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::customScript})}" th:class="${userTheme}">

    <div class="content-wrapper">
        <!-- 当前用户信息 (隐藏元素，供JavaScript使用) -->
        <div id="currentUserInfo" th:data-current-user="${currentUser}" th:data-current-user-id="${currentUserId}"
            style="display: none;"></div>

        <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-check-circle me-2"></i>签到管理</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-outline-secondary" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span th:text="${error}">错误信息</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number" id="todayCheckInsCount">-</div>
                    <div>今日签到</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number" id="weekCheckInsCount">-</div>
                    <div>本周签到</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number" id="monthCheckInsCount">-</div>
                    <div>本月签到</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number" id="totalCheckInsCount">-</div>
                    <div>总计签到</div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 待签到事件 -->
            <div class="col-md-6">
                <div class="check-in-card card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>待签到事件</h5>
                    </div>
                    <div class="card-body">
                        <div id="pendingEvents" class="loading-content">
                            <div class="text-center py-4">
                                <div class="loading-spinner spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">加载待签到事件...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速签到 -->
            <div class="col-md-6">
                <div class="check-in-card card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-geo-alt me-2"></i>快速签到</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <p class="text-muted">选择要签到的事件</p>
                            <select id="quickCheckInSelect" class="form-select">
                                <option value="">选择事件...</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea id="quickCheckInNotes" class="form-control" rows="3"
                                placeholder="签到备注（可选）"></textarea>
                        </div>

                        <button id="quickCheckInBtn" class="check-in-btn btn btn-lg success" disabled>
                            <i class="bi bi-check-circle me-2"></i>立即签到
                        </button>

                        <div id="locationInfo" class="mt-3 text-muted small">
                            <i class="bi bi-geo-alt"></i> 正在获取位置信息...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 签到历史 -->
        <div class="row">
            <div class="col-12">
                <div class="check-in-card card">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>签到历史</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-light" id="filterToday">今天</button>
                            <button class="btn btn-outline-light" id="filterWeek">本周</button>
                            <button class="btn btn-outline-light" id="filterMonth">本月</button>
                            <button class="btn btn-outline-light active" id="filterAll">全部</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="checkInHistory" class="loading-content">
                            <div class="text-center py-4">
                                <div class="loading-spinner spinner-border text-info" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">加载签到历史...</p>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <nav id="historyPagination" class="mt-3" style="display: none;">
                            <ul class="pagination justify-content-center">
                                <!-- 分页内容将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 签到成功模态框 -->
    <div class="modal fade" id="checkInSuccessModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle-fill me-2"></i>签到成功
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-success">签到成功！</h4>
                    <p class="text-muted" id="checkInSuccessMessage">您已成功完成签到</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 位置权限说明模态框 -->
    <div class="modal fade" id="locationPermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="bi bi-geo-alt me-2"></i>位置权限
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>为了提供更准确的签到服务，系统需要获取您的位置信息。</p>
                    <p class="text-muted">您的位置信息仅用于签到记录，不会被用于其他用途。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">稍后再说</button>
                    <button type="button" class="btn btn-primary" id="enableLocationBtn">允许位置访问</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:fragment="customScript">
        <script th:src="@{/js/calendar/calendar.js}"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // 初始化签到管理器
                const checkInManager = new CheckInManager();

                // 调试：输出初始化信息
                console.log('=== 签到页面调试信息 ===');
                console.log('CheckInManager已初始化');

                // 检查用户信息元素
                const userInfoElement = document.getElementById('currentUserInfo');
                if (userInfoElement) {
                    console.log('用户信息元素存在');
                    console.log('data-current-user:', userInfoElement.getAttribute('data-current-user'));
                    console.log('data-current-user-id:', userInfoElement.getAttribute('data-current-user-id'));
                } else {
                    console.error('用户信息元素不存在！');
                }
                console.log('========================');

                // 页面元素
                const quickCheckInSelect = document.getElementById('quickCheckInSelect');
                const quickCheckInBtn = document.getElementById('quickCheckInBtn');
                const quickCheckInNotes = document.getElementById('quickCheckInNotes');
                const refreshBtn = document.getElementById('refreshBtn');
                const locationInfo = document.getElementById('locationInfo');

                // 过滤按钮
                const filterButtons = {
                    today: document.getElementById('filterToday'),
                    week: document.getElementById('filterWeek'),
                    month: document.getElementById('filterMonth'),
                    all: document.getElementById('filterAll')
                };

                let currentLocation = null;
                let pendingEvents = [];
                let checkInHistory = [];
                let currentHistoryFilter = 'all';
                let historyPage = 0;
                const historyPageSize = 10;                // 初始化页面
                async function init() {
                    console.log('=== 签到页面初始化开始 ===');
                    
                    // 首先检查用户登录状态
                    try {
                        const response = await fetch('/api/user/current');
                        if (response.status === 403 || response.status === 401) {
                            showError('请先登录后再访问签到功能');
                            return;
                        }
                        const userData = await response.json();
                        console.log('当前用户:', userData);
                    } catch (error) {
                        console.error('检查用户登录状态失败:', error);
                        showError('无法验证登录状态，请刷新页面重试');
                        return;
                    }
                    
                    showLoadingSpinners();
                    
                    // 并行加载数据
                    try {
                        await Promise.all([
                            loadPendingEvents(),
                            loadCheckInStatistics(), 
                            loadCheckInHistory()
                        ]);
                    } catch (error) {
                        console.error('初始化数据加载失败:', error);
                    }
                    
                    getCurrentLocation();
                    bindEventListeners();
                    
                    console.log('=== 签到页面初始化完成 ===');
                }

                function showLoadingSpinners() {
                    document.querySelectorAll('.loading-spinner').forEach(spinner => {
                        spinner.style.display = 'block';
                    });
                }

                function hideLoadingSpinners() {
                    document.querySelectorAll('.loading-spinner').forEach(spinner => {
                        spinner.style.display = 'none';
                    });
                }

                function bindEventListeners() {
                    // 快速签到选择
                    quickCheckInSelect.addEventListener('change', function () {
                        const selected = this.value;
                        quickCheckInBtn.disabled = !selected;
                    });

                    // 快速签到按钮
                    quickCheckInBtn.addEventListener('click', handleQuickCheckIn);

                    // 刷新按钮
                    refreshBtn.addEventListener('click', function () {
                        init();
                    });

                    // 历史过滤按钮
                    Object.keys(filterButtons).forEach(key => {
                        filterButtons[key].addEventListener('click', function () {
                            setActiveFilter(key);
                            currentHistoryFilter = key;
                            historyPage = 0;
                            loadCheckInHistory();
                        });
                    });

                    // 位置权限按钮
                    document.getElementById('enableLocationBtn').addEventListener('click', function () {
                        getCurrentLocation();
                        const modal = bootstrap.Modal.getInstance(document.getElementById('locationPermissionModal'));
                        modal.hide();
                    });
                }

                function setActiveFilter(activeKey) {
                    Object.keys(filterButtons).forEach(key => {
                        filterButtons[key].classList.toggle('active', key === activeKey);
                    });
                }                async function loadPendingEvents() {
                    try {
                        console.log('开始加载待签到事件');

                        const events = await checkInManager.getPendingCheckIns();
                        console.log('通过CheckInManager获取到的待签到事件:', events);
                        console.log('事件数量:', events ? events.length : 0);
                        
                        pendingEvents = events || [];
                        console.log('开始渲染待签到事件...');
                        renderPendingEvents(pendingEvents);
                        console.log('待签到事件渲染完成');
                        
                        console.log('开始更新快速签到下拉列表...');
                        updateQuickCheckInSelect(pendingEvents);
                        console.log('快速签到下拉列表更新完成');
                    } catch (error) {
                        console.error('加载待签到事件失败:', error);
                        showError('加载待签到事件失败: ' + error.message);
                        
                        // 即使出错也要清空列表，避免显示过期数据
                        pendingEvents = [];
                        renderPendingEvents([]);
                        updateQuickCheckInSelect([]);
                    }
                }

                async function loadCheckInStatistics() {
                    try {
                        const stats = await checkInManager.getCheckInStatistics();
                        updateStatistics(stats);
                    } catch (error) {
                        console.error('加载签到统计失败:', error);
                    }
                }                async function loadCheckInHistory() {
                    try {
                        console.log('开始加载签到历史...');
                        const history = await checkInManager.getCheckInHistory(currentHistoryFilter, historyPage, historyPageSize);
                        console.log('获取到的签到历史:', history);
                        checkInHistory = history;
                        renderCheckInHistory(history);
                    } catch (error) {
                        console.error('加载签到历史失败:', error);
                        
                        // 显示错误信息
                        const container = document.getElementById('checkInHistory');
                        container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            <p class="mt-2 text-warning">加载签到历史失败</p>
                            <small class="text-muted">${error.message}</small>
                            <br>
                            <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadCheckInHistory()">
                                <i class="bi bi-arrow-repeat"></i> 重试
                            </button>
                        </div>
                        `;
                        
                        showError('加载签到历史失败: ' + error.message);
                    }
                }                function renderPendingEvents(events) {
                    console.log('开始渲染待签到事件，事件数量:', events.length);
                    const container = document.getElementById('pendingEvents');

                    if (events.length === 0) {
                        console.log('没有待签到事件，显示空状态');
                        container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                            <p class="mt-2 text-muted">暂无待签到事件</p>
                        </div>
                    `;
                        return;
                    }

                    console.log('开始处理', events.length, '个待签到事件');const html = events.map(event => {
                        console.log('渲染事件:', event); // 调试日志

                        // 增加详细的时间调试信息
                        const now = new Date();
                        console.log('原始提醒时间字符串:', event.eventReminder.reminderTime);                        // 增强的日期解析方法
                        let reminderTime;
                        if (Array.isArray(event.eventReminder.reminderTime)) {
                            // 处理数组格式 [year, month, day, hour, minute, second]
                            const timeArray = event.eventReminder.reminderTime;
                            reminderTime = new Date(
                                timeArray[0], // year
                                timeArray[1] - 1, // month (JavaScript月份从0开始)
                                timeArray[2], // day
                                timeArray[3] || 0, // hour
                                timeArray[4] || 0, // minute
                                timeArray[5] || 0  // second
                            );
                        } else if (typeof event.eventReminder.reminderTime === 'string') {
                            // 如果是字符串，尝试标准格式解析
                            if (event.eventReminder.reminderTime.includes('T')) {
                                reminderTime = new Date(event.eventReminder.reminderTime);
                            } else {
                                // 处理 "YYYY-MM-DD HH:MM:SS" 格式
                                reminderTime = new Date(event.eventReminder.reminderTime.replace(' ', 'T'));
                            }
                        } else if (typeof event.eventReminder.reminderTime === 'object' && event.eventReminder.reminderTime !== null) {
                            // 处理对象格式 (可能是 LocalDateTime 对象)
                            const timeObj = event.eventReminder.reminderTime;
                            if (timeObj.year && timeObj.monthValue && timeObj.dayOfMonth) {
                                reminderTime = new Date(
                                    timeObj.year,
                                    timeObj.monthValue - 1,
                                    timeObj.dayOfMonth,
                                    timeObj.hour || 0,
                                    timeObj.minute || 0,
                                    timeObj.second || 0
                                );                            } else {
                                reminderTime = new Date(event.eventReminder.reminderTime);
                            }
                        } else {
                            // 最后的fallback，尝试直接解析
                            console.warn('无法识别时间格式，尝试直接解析:', event.eventReminder.reminderTime);
                            try {
                                if (Array.isArray(event.eventReminder.reminderTime)) {
                                    // 再次尝试数组格式解析
                                    const timeArray = event.eventReminder.reminderTime;
                                    reminderTime = new Date(
                                        timeArray[0], 
                                        timeArray[1] - 1, 
                                        timeArray[2], 
                                        timeArray[3] || 0, 
                                        timeArray[4] || 0, 
                                        timeArray[5] || 0                                );
                            } else {
                                // 对象格式的fallback
                                try {
                                    if (Array.isArray(event.eventReminder.reminderTime)) {
                                        const timeArray = event.eventReminder.reminderTime;
                                        reminderTime = new Date(
                                            timeArray[0], 
                                            timeArray[1] - 1, 
                                            timeArray[2], 
                                            timeArray[3] || 0, 
                                            timeArray[4] || 0, 
                                            timeArray[5] || 0
                                        );
                                    } else {
                                        reminderTime = new Date(event.eventReminder.reminderTime);
                                    }
                                } catch (e) {
                                    console.error('对象格式解析失败:', e);
                                    reminderTime = new Date();
                                }
                            }
                            } catch (e) {
                                console.error('最终解析失败:', e);
                                reminderTime = new Date(); // 使用当前时间作为fallback
                            }
                        }

                        console.log('当前时间:', now);
                        console.log('解析后的提醒时间:', reminderTime);
                        console.log('提醒时间是否有效:', !isNaN(reminderTime.getTime()));

                        if (isNaN(reminderTime.getTime())) {
                            console.error('无法解析提醒时间:', event.eventReminder.reminderTime);
                            reminderTime = new Date(); // 设置为当前时间作为fallback
                        }                        console.log('时间差(分钟):', (reminderTime - now) / (1000 * 60));

                        // 获取签到窗口时间，默认30分钟
                        const checkInWindowMinutes = event.eventReminder.checkInWindowMinutes || 30;
                        console.log('签到窗口时间(分钟):', checkInWindowMinutes);

                        const timeWindow = checkInManager.getTimeWindow(reminderTime.toISOString(), checkInWindowMinutes);
                        const canCheckIn = checkInManager.canCheckIn(reminderTime.toISOString(), checkInWindowMinutes);

                        console.log('时间窗口状态:', timeWindow);
                        console.log('是否可以签到:', canCheckIn);

                        const statusClass = canCheckIn ? 'success' : (timeWindow.status === 'upcoming' ? 'warning' : 'danger');
                        const statusText = canCheckIn ? '可签到' : (timeWindow.status === 'upcoming' ? '未到时间' : '已过期');

                        return `
                        <div class="event-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${event.eventTitle}</h6>
                                    <p class="text-muted mb-2">${event.eventReminder.message || '无描述'}</p>                                    <div class="small">
                                        <span class="time-badge me-2">
                                            <i class="bi bi-clock"></i> ${reminderTime.toLocaleString()}
                                        </span>
                                        <span class="status-badge bg-${statusClass}">${statusText}</span>
                                    </div>
                                </div>                                <div class="text-end">
                                    ${canCheckIn ? `
                                        <button class="btn btn-sm btn-success check-in-event-btn" 
                                                data-event-id="${event.eventReminder.id}">
                                            <i class="bi bi-check-circle"></i> 签到
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                    }).join('');

                    container.innerHTML = html;                    // 绑定事件签到按钮
                    container.querySelectorAll('.check-in-event-btn').forEach(btn => {
                        btn.addEventListener('click', function () {
                            const reminderId = this.dataset.eventId; // 实际上是提醒ID
                            handleEventCheckIn(reminderId);
                        });
                    });
                }                function updateQuickCheckInSelect(events) {
                    console.log('开始更新快速签到下拉列表，事件数量:', events.length);
                    const select = quickCheckInSelect;
                    select.innerHTML = '<option value="">选择事件...</option>';
                    
                    let addedCount = 0;
                    events.forEach((event, index) => {
                        console.log(`处理事件 ${index + 1}:`, event.eventTitle);
                        try {
                            const checkInWindowMinutes = event.eventReminder.checkInWindowMinutes || 30;
                            const canCheckIn = checkInManager.canCheckIn(event.eventReminder.reminderTime, checkInWindowMinutes);
                            console.log(`事件 "${event.eventTitle}" 是否可签到:`, canCheckIn);
                            
                            if (canCheckIn) {
                                const option = document.createElement('option');
                                option.value = event.eventReminder.id; // 使用提醒ID
                                option.textContent = event.eventTitle;
                                select.appendChild(option);
                                addedCount++;
                                console.log(`已添加事件 "${event.eventTitle}" 到下拉列表`);
                            }
                        } catch (error) {
                            console.error(`处理事件 "${event.eventTitle}" 时出错:`, error);
                        }
                    });
                    
                    console.log(`快速签到下拉列表更新完成，共添加 ${addedCount} 个可签到事件`);
                    
                    // 确保按钮状态正确
                    quickCheckInBtn.disabled = select.value === '';
                    console.log('快速签到按钮状态:', quickCheckInBtn.disabled ? '禁用' : '启用');
                }

                function updateStatistics(stats) {
                    document.getElementById('todayCheckInsCount').textContent = stats.todayCount || 0;
                    document.getElementById('weekCheckInsCount').textContent = stats.weekCount || 0;
                    document.getElementById('monthCheckInsCount').textContent = stats.monthCount || 0;
                    document.getElementById('totalCheckInsCount').textContent = stats.totalCount || 0;
                }                function renderCheckInHistory(history) {
                    const container = document.getElementById('checkInHistory');

                    // 检查是否有历史数据
                    if (!history || !history.content || history.content.length === 0) {
                        container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-clock-history text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-2 text-muted">暂无签到历史</p>
                            <small class="text-muted">请尝试刷新页面或检查登录状态</small>
                        </div>
                    `;
                        return;
                    }

                    const items = history.content || [];
                    const html = items.map(item => `
                    <div class="history-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${item.eventReminder?.title || item.eventTitle || '未知事件'}</h6>
                                <p class="text-muted mb-2">${item.notes || '无备注'}</p>
                                <div class="small">
                                    <span class="time-badge me-2">
                                        <i class="bi bi-clock"></i> ${new Date(item.checkInTime).toLocaleString()}
                                    </span>
                                    ${item.latitude && item.longitude ? `
                                        <span class="location-badge me-2">
                                            <i class="bi bi-geo-alt"></i> ${item.latitude.toFixed(4)}, ${item.longitude.toFixed(4)}
                                        </span>
                                    ` : ''}
                                    <span class="badge bg-success">已签到</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');

                    container.innerHTML = html;

                    // 更新分页
                    updateHistoryPagination(history);
                }

                function updateHistoryPagination(history) {
                    const pagination = document.getElementById('historyPagination');

                    if (!history.totalPages || history.totalPages <= 1) {
                        pagination.style.display = 'none';
                        return;
                    }

                    pagination.style.display = 'block';
                    const ul = pagination.querySelector('ul');

                    let html = '';

                    // 上一页
                    if (history.number > 0) {
                        html += `<li class="page-item"><a class="page-link" href="#" data-page="${history.number - 1}">上一页</a></li>`;
                    }

                    // 页码
                    for (let i = 0; i < history.totalPages; i++) {
                        if (i === history.number) {
                            html += `<li class="page-item active"><span class="page-link">${i + 1}</span></li>`;
                        } else {
                            html += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i + 1}</a></li>`;
                        }
                    }

                    // 下一页
                    if (history.number < history.totalPages - 1) {
                        html += `<li class="page-item"><a class="page-link" href="#" data-page="${history.number + 1}">下一页</a></li>`;
                    }

                    ul.innerHTML = html;

                    // 绑定分页点击事件
                    ul.querySelectorAll('a[data-page]').forEach(link => {
                        link.addEventListener('click', function (e) {
                            e.preventDefault();
                            historyPage = parseInt(this.dataset.page);
                            loadCheckInHistory();
                        });
                    });
                }                async function handleQuickCheckIn() {
                    const reminderId = quickCheckInSelect.value; // 现在是提醒ID
                    if (!reminderId) return;

                    await performCheckIn(reminderId, quickCheckInNotes.value);
                }

                async function handleEventCheckIn(reminderId) { // 现在是提醒ID
                    await performCheckIn(reminderId, '');
                }                async function performCheckIn(reminderId, notes) { // 现在是提醒ID
                    try {
                        quickCheckInBtn.disabled = true;
                        quickCheckInBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>签到中...';

                        const result = await checkInManager.performCheckIn(reminderId, {
                            notes: notes,
                            latitude: currentLocation?.latitude,
                            longitude: currentLocation?.longitude
                        });                        if (result.success) {
                            showCheckInSuccess(result.message);
                            
                            // 等待一小段时间确保后端数据更新完成
                            console.log('签到成功，等待500ms后开始刷新数据...');
                            await new Promise(resolve => setTimeout(resolve, 500));
                            
                            // 刷新数据 - 使用顺序执行确保数据完整性
                            console.log('开始刷新签到后的数据...');
                            try {
                                // 先刷新待签到事件
                                console.log('刷新待签到事件...');
                                await loadPendingEvents();
                                console.log('待签到事件刷新完成');
                                
                                // 再刷新统计信息
                                console.log('刷新统计信息...');
                                await loadCheckInStatistics();
                                console.log('统计信息刷新完成');
                                
                                // 最后刷新历史记录
                                console.log('刷新签到历史...');
                                await loadCheckInHistory();
                                console.log('签到历史刷新完成');
                                
                                console.log('所有数据刷新完成');
                            } catch (refreshError) {
                                console.error('刷新数据失败:', refreshError);
                                showError('签到成功，但刷新数据失败: ' + refreshError.message + '，请手动刷新页面');
                            }
                            
                            // 清空表单
                            quickCheckInSelect.value = '';
                            quickCheckInNotes.value = '';
                            // 重新触发选择事件，更新按钮状态
                            quickCheckInSelect.dispatchEvent(new Event('change'));
                            
                            console.log('签到流程完全结束');
                        } else {
                            showError(result.message || '签到失败');
                        }
                    } catch (error) {
                        console.error('签到失败:', error);
                        showError('签到失败: ' + error.message);
                    } finally {
                        quickCheckInBtn.disabled = false;
                        quickCheckInBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>立即签到';
                    }
                }

                function getCurrentLocation() {
                    if (!navigator.geolocation) {
                        locationInfo.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i> 浏览器不支持位置服务';
                        return;
                    }

                    locationInfo.innerHTML = '<i class="bi bi-geo-alt"></i> 正在获取位置信息...';

                    navigator.geolocation.getCurrentPosition(
                        function (position) {
                            currentLocation = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude
                            };
                            locationInfo.innerHTML = `
                            <i class="bi bi-geo-alt-fill text-success"></i> 
                            位置已获取 (${currentLocation.latitude.toFixed(4)}, ${currentLocation.longitude.toFixed(4)})
                        `;
                        },
                        function (error) {
                            console.error('获取位置失败:', error);
                            locationInfo.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i> 无法获取位置信息';

                            // 显示位置权限提示
                            if (error.code === error.PERMISSION_DENIED) {
                                const modal = new bootstrap.Modal(document.getElementById('locationPermissionModal'));
                                modal.show();
                            }
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 300000
                        }
                    );
                }                function showCheckInSuccess(message) {
                    document.getElementById('checkInSuccessMessage').textContent = message || '您已成功完成签到';
                    const modal = new bootstrap.Modal(document.getElementById('checkInSuccessModal'));
                    modal.show();
                    
                    // 3秒后自动关闭模态框
                    setTimeout(() => {
                        modal.hide();
                    }, 3000);
                }

                function showError(message) {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                    const container = document.querySelector('.main-content');
                    container.insertBefore(alertDiv, container.firstChild.nextSibling);

                    // 自动消失
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 5000);                }
                
                // 启动初始化
                init();
                
                // 隐藏加载动画
                hideLoadingSpinners();
            });
        </script>
    </th:block>

</body>

</html>