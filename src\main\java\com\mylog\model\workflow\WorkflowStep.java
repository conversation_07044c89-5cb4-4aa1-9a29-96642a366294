package com.mylog.model.workflow;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程步骤实体类
 * 定义每个审批步骤的配置
 */
@Entity
@Table(name = "workflow_steps")
@Data
public class WorkflowStep {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowStep.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 审批类型枚举
     */
    public enum ApproverType {
        FIXED_USER,      // 固定用户
        ROLE,            // 角色
        DEPARTMENT_HEAD, // 部门主管
        DYNAMIC          // 动态指定
    }
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long stepId;
    
    /**
     * 所属流程模板
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", nullable = false)
    private WorkflowTemplate template;
    
    /**
     * 步骤名称
     */
    @Column(nullable = false, length = 100)
    private String stepName;
    
    /**
     * 步骤顺序
     */
    @Column(nullable = false)
    private Integer stepOrder;
    
    /**
     * 审批人类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ApproverType approverType;
    
    /**
     * 审批人配置
     * 根据审批人类型，可能是用户ID、角色名称等
     */
    @Column(length = 255)
    private String approverConfig;
    
    /**
     * 是否允许退回
     */
    @Column(nullable = false)
    private Boolean allowReject = true;
    
    /**
     * 是否允许转交
     */
    @Column(nullable = false)
    private Boolean allowTransfer = false;
    
    /**
     * 是否为条件分支
     */
    @Column(nullable = false)
    private Boolean isConditionBranch = false;
    
    /**
     * 条件表达式
     * 当isConditionBranch为true时有效
     */
    @Column(columnDefinition = "TEXT")
    private String conditionExpression;
    
    /**
     * 步骤说明
     */
    @Column(columnDefinition = "TEXT")
    private String description;
    
    /**
     * 创建时间
     */
    @Column(nullable = false)
    private String createdDate;
    
    /**
     * 最后修改时间
     */
    @Column
    private String lastModifiedDate;
    
    /**
     * 创建人
     */
    @Column(length = 50)
    private String createdBy;
    
    /**
     * 最后修改人
     */
    @Column(length = 50)
    private String lastModifiedBy;
    
    /**
     * 审批记录列表
     */
    @OneToMany(mappedBy = "step", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ApprovalRecord> approvalRecords = new ArrayList<>();
    
    /**
     * 设置创建时间
     */
    public void setCreatedDateTime(LocalDateTime dateTime) {
        this.createdDate = formatDateTime(dateTime);
    }
    
    /**
     * 获取创建时间
     */
    public LocalDateTime getCreatedDateTime() {
        return parseDateTime(this.createdDate);
    }
    
    /**
     * 设置最后修改时间
     */
    public void setLastModifiedDateTime(LocalDateTime dateTime) {
        this.lastModifiedDate = formatDateTime(dateTime);
    }
    
    /**
     * 获取最后修改时间
     */
    public LocalDateTime getLastModifiedDateTime() {
        return parseDateTime(this.lastModifiedDate);
    }
    
    /**
     * 解析日期时间字符串
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (Exception e) {
            logger.error("解析日期时间字符串失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }
}
