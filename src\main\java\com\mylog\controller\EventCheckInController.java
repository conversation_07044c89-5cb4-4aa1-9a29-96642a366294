package com.mylog.controller;

import com.mylog.dto.EventCheckInDTO;
import com.mylog.service.EventCheckInService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 事件签到控制器
 */
@RestController
@RequestMapping(value = "/api/check-ins", produces = MediaType.APPLICATION_JSON_VALUE)
@CrossOrigin(origins = "*")
public class EventCheckInController {

    private static final Logger logger = LoggerFactory.getLogger(EventCheckInController.class);

    @Autowired
    private EventCheckInService checkInService;

    /**
     * 用户签到
     */
    @PostMapping("/reminder/{reminderId}/user/{userId}")
    public ResponseEntity<Map<String, Object>> checkIn(@PathVariable Long reminderId,
                                                      @PathVariable String userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            EventCheckInDTO checkIn = checkInService.checkIn(reminderId, userId);
            response.put("success", true);
            response.put("message", "签到成功");
            response.put("data", checkIn);
            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "签到失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查是否可以签到
     */
    @GetMapping("/reminder/{reminderId}/user/{userId}/can-check-in")
    public ResponseEntity<Map<String, Object>> canCheckIn(@PathVariable Long reminderId,
                                                         @PathVariable String userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean canCheckIn = checkInService.canCheckIn(reminderId, userId);
            boolean withinWindow = checkInService.isWithinCheckInWindow(reminderId);

            response.put("success", true);
            response.put("canCheckIn", canCheckIn);
            response.put("withinWindow", withinWindow);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户的签到记录
     */
    @GetMapping("/reminder/{reminderId}/user/{userId}")
    public ResponseEntity<Map<String, Object>> getCheckInRecord(@PathVariable Long reminderId,
                                                               @PathVariable String userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            Optional<EventCheckInDTO> checkIn = checkInService.getCheckInRecord(reminderId, userId);
            response.put("success", true);
            response.put("data", checkIn.orElse(null));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取事件的所有签到记录
     */
    @GetMapping("/event/{eventId}")
    public ResponseEntity<Map<String, Object>> getEventCheckIns(@PathVariable Long eventId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<EventCheckInDTO> checkIns = checkInService.getEventCheckIns(eventId);
            response.put("success", true);
            response.put("data", checkIns);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户的签到历史（带日期参数）
     */
    @GetMapping("/user/{userId}/history-by-date")
    public ResponseEntity<Map<String, Object>> getUserCheckInHistoryByDate(
            @PathVariable String userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<EventCheckInDTO> checkIns = checkInService.getUserCheckInHistory(userId, startDate, endDate);
            response.put("success", true);
            response.put("data", checkIns);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查提醒是否在签到时间窗口内
     */
    @GetMapping("/reminder/{reminderId}/check-window")
    public ResponseEntity<Map<String, Object>> checkWindow(@PathVariable Long reminderId) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean withinWindow = checkInService.isWithinCheckInWindow(reminderId);
            response.put("success", true);
            response.put("withinWindow", withinWindow);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户待签到事件列表
     */
    @GetMapping("/user/{userId}/pending")
    public ResponseEntity<Map<String, Object>> getPendingCheckIns(@PathVariable String userId) {
        Map<String, Object> response = new HashMap<>();
        logger.info("========== Controller: 开始获取待签到事件 ==========");
        logger.info("Controller: 用户ID: {}", userId);

        try {
            // 获取当前登录用户（可以基于SecurityContext获取真实用户）
            // 目前为了测试，先直接使用传入的userId
            // TODO: 在生产环境中应该使用SecurityContextHolder获取当前用户

            List<EventCheckInService.PendingCheckInDTO> pendingCheckIns =
                    checkInService.getPendingCheckIns(userId);

            logger.info("Controller: 获取到的待签到事件数量: {}", pendingCheckIns.size());
            for (EventCheckInService.PendingCheckInDTO dto : pendingCheckIns) {
                // System.out.println("事件: " + dto.getEventTitle() + ", 提醒时间: " + dto.getEventReminder().getReminderTime());
                // 注意：如果 EventReminder 对象可能为 null，或者其 reminderTime 可能为 null，这里需要进行空检查以避免 NullPointerException
                String reminderTimeStr = "N/A";
                if (dto.getEventReminder() != null && dto.getEventReminder().getReminderTime() != null) {
                    reminderTimeStr = dto.getEventReminder().getReminderTime().toString();
                }
                logger.info("Controller: 事件: {}, 提醒时间: {}", dto.getEventTitle(), reminderTimeStr);
            }

            response.put("success", true);
            response.put("data", pendingCheckIns);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            // System.err.println("获取待签到事件失败: " + e.getMessage());
            // e.printStackTrace();
            logger.error("Controller: 获取待签到事件失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取签到统计信息（简化版，不需要日期参数）
     */
    @GetMapping("/user/{userId}/statistics")
    public ResponseEntity<Map<String, Object>> getSimpleCheckInStatistics(@PathVariable String userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            EventCheckInService.SimpleStatistics statistics =
                    checkInService.getSimpleCheckInStatistics(userId);
            response.put("success", true);
            response.put("data", statistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取分页签到历史
     */
    @GetMapping("/user/{userId}/history-paged")
    public ResponseEntity<Map<String, Object>> getPagedCheckInHistory(
            @PathVariable String userId,
            @RequestParam(defaultValue = "all") String filter,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> response = new HashMap<>();
        try {
            EventCheckInService.PagedCheckInHistory history =
                    checkInService.getPagedCheckInHistory(userId, filter, page, size);
            response.put("success", true);
            response.put("data", history);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 通过事件ID进行签到
     */
    @PostMapping("/event/{eventId}/user/{userId}")
    public ResponseEntity<Map<String, Object>> checkInByEvent(
            @PathVariable Long eventId,
            @PathVariable String userId,
            @RequestBody(required = false) Map<String, Object> requestBody) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 提取请求参数
            String notes = requestBody != null ? (String) requestBody.get("notes") : "";
            Double latitude = requestBody != null ? (Double) requestBody.get("latitude") : null;
            Double longitude = requestBody != null ? (Double) requestBody.get("longitude") : null;

            EventCheckInDTO checkIn = checkInService.checkInByEvent(eventId, userId, notes, latitude, longitude);
            response.put("success", true);
            response.put("message", "签到成功");
            response.put("data", checkIn);
            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "签到失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取签到统计信息（带日期参数）
     */
    @GetMapping("/user/{userId}/statistics-by-date")
    public ResponseEntity<Map<String, Object>> getCheckInStatisticsByDate(
            @PathVariable String userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            EventCheckInService.CheckInStatistics statistics =
                    checkInService.getCheckInStatistics(userId, startDate, endDate);
            response.put("success", true);
            response.put("data", statistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
