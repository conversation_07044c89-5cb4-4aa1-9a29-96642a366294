package com.mylog.controller.workflow;

import com.mylog.controller.BaseController;

import com.mylog.model.user.User;
import com.mylog.model.workflow.WorkflowTemplate;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.WorkflowTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;


import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 流程模板控制器
 */
@Controller
@RequestMapping("/workflow/templates")
@PreAuthorize("hasRole('ADMIN')")
public class WorkflowTemplateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowTemplateController.class);

    @Autowired
    private WorkflowTemplateService templateService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityLogService activityLogService;

    /**
     * 显示流程模板列表
     */
    @GetMapping
    public String listTemplates(
            Model model,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "scope", required = false) String scope,
            @RequestParam(value = "enabled", required = false) Boolean enabled) {

        try {
            logger.info("开始加载流程模板列表");
            List<WorkflowTemplate> templates;

            try {
                // 使用标准方法获取所有模板
                templates = templateService.findAllTemplates();
                logger.info("使用标准方法成功获取 {} 个流程模板", templates.size());
            } catch (Exception e) {
                // 如果标准查询失败，尝试使用备选方法
                logger.error("标准查询流程模板失败: {}", e.getMessage());
                templates = java.util.Collections.emptyList();
                logger.info("使用空列表作为备选");
            }

            // 过滤和排序模板列表
            List<WorkflowTemplate> filteredTemplates = templates.stream()
                    .filter(t -> keyword == null ||
                            (t.getTemplateName() != null && t.getTemplateName().contains(keyword)) ||
                            (t.getTemplateTitle() != null && t.getTemplateTitle().contains(keyword)) ||
                            (t.getDescription() != null && t.getDescription().contains(keyword)))
                    .filter(t -> scope == null || (t.getApplicableScope() != null && scope.equals(t.getApplicableScope())))
                    .filter(t -> enabled == null || (t.getEnabled() != null && enabled.equals(t.getEnabled())))
                    .sorted((t1, t2) -> {
                        if (t1.getCreatedAt() == null) return 1;
                        if (t2.getCreatedAt() == null) return -1;
                        return t2.getCreatedAt().compareTo(t1.getCreatedAt());
                    })
                    .toList();

            // 手动实现分页
            int totalItems = filteredTemplates.size();
            int totalPages = (int) Math.ceil((double) totalItems / size);
            page = Math.max(0, Math.min(page, totalPages - 1 < 0 ? 0 : totalPages - 1));

            int fromIndex = page * size;
            int toIndex = Math.min(fromIndex + size, totalItems);

            List<WorkflowTemplate> pageContent;
            if (fromIndex < toIndex) {
                pageContent = filteredTemplates.subList(fromIndex, toIndex);
            } else {
                pageContent = java.util.Collections.emptyList();
            }

            // 创建分页对象
            Page<WorkflowTemplate> templatePage = new org.springframework.data.domain.PageImpl<>(
                    pageContent,
                    PageRequest.of(page, size),
                    totalItems
            );

            // 添加到模型
            model.addAttribute("templatePage", templatePage);
            model.addAttribute("allTemplates", filteredTemplates); // 添加所有过滤后的模板
            model.addAttribute("keyword", keyword);
            model.addAttribute("scope", scope);
            model.addAttribute("enabled", enabled);
            model.addAttribute("activeMenu", "workflow");

            logger.info("成功加载流程模板列表：总记录数={}, 当前页={}, 每页大小={}", totalItems, page, size);

            return "workflow/templates/index";
        } catch (Exception e) {
            logger.error("加载流程模板列表时出错: {}", e.getMessage(), e);

            // 出错时提供一个空列表，确保界面不会崩溃
            model.addAttribute("templatePage", new org.springframework.data.domain.PageImpl<>(
                    java.util.Collections.emptyList(),
                    PageRequest.of(0, size),
                    0
            ));
            model.addAttribute("allTemplates", java.util.Collections.emptyList());
            model.addAttribute("error", "加载流程模板列表时出错: " + e.getMessage());
            model.addAttribute("activeMenu", "workflow");

            return "workflow/templates/index"; // 返回相同视图但带有错误信息
        }
    }

    /**
     * 显示创建流程模板表单
     */
    @GetMapping("/create")
    public String showCreateForm(Model model) {
        model.addAttribute("template", new WorkflowTemplate());
        model.addAttribute("activeMenu", "workflow");
        return "workflow/templates/form";
    }

    /**
     * 显示编辑流程模板表单
     */
    @GetMapping("/{id}/edit")
    public String showEditForm(@PathVariable("id") Long id, Model model, RedirectAttributes redirectAttributes) {
        Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(id);
        if (templateOpt.isPresent()) {
            model.addAttribute("template", templateOpt.get());
            model.addAttribute("activeMenu", "workflow");
            return "workflow/templates/form";
        } else {
            redirectAttributes.addFlashAttribute("error", "流程模板不存在");
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 保存流程模板
     */
    @PostMapping("/save")
    public String saveTemplate(WorkflowTemplate template, RedirectAttributes redirectAttributes) {
        try {
            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 设置创建人或最后修改人
            boolean isNewTemplate = (template.getTemplateId() == null);
            if (isNewTemplate) {
                template.setCreatedBy(currentUsername);
            } else {
                template.setLastModifiedBy(currentUsername);
            }

            // 记录模板保存前的状态
            logger.debug("保存前的模板信息: ID={}, 名称={}", template.getTemplateId(), template.getTemplateName());

            // 保存模板
            WorkflowTemplate savedTemplate = templateService.saveTemplate(template);

            // 确保获取到数据库生成的ID
            Long savedTemplateId = savedTemplate.getTemplateId();
            logger.info("保存流程模板成功，ID: {}, 名称: {}", savedTemplateId, savedTemplate.getTemplateName());

            // 不再使用JPA查询来验证保存操作，直接认为成功
            // 这样避免了可能的时间戳解析错误
            logger.info("流程模板已成功保存到数据库");

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String action = isNewTemplate ? "创建" : "更新";
                String ipAddress = getClientIpAddress();
                if (isNewTemplate) {
                    activityLogService.logCreate(
                            currentUser.get().getUserId(),
                            currentUsername,
                            action + "流程模板: " + template.getTemplateName(),
                            ipAddress,
                            "WorkflowTemplate",
                            savedTemplateId, // 使用保存后获取的ID
                            getAccessType()
                    );
                } else {
                    activityLogService.logUpdate(
                            currentUser.get().getUserId(),
                            currentUsername,
                            action + "流程模板: " + template.getTemplateName(),
                            ipAddress,
                            "WorkflowTemplate",
                            savedTemplateId, // 使用保存后获取的ID
                            getAccessType()
                    );
                }
            }

            redirectAttributes.addFlashAttribute("message", "流程模板保存成功");
            return "redirect:/workflow/templates";
        } catch (Exception e) {
            logger.error("保存流程模板时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "保存流程模板失败: " + e.getMessage());
            return "redirect:/workflow/templates";
        }
    }

    /**
     * 删除流程模板
     */
    @RequestMapping(value = "/delete", method = {RequestMethod.POST, RequestMethod.GET})
    public String deleteTemplate(@RequestParam("templateId") Long id, RedirectAttributes redirectAttributes) {
        try {
            // 获取要删除的模板信息（用于日志记录）
            Optional<WorkflowTemplate> templateToDelete = templateService.findTemplateById(id);
            if (!templateToDelete.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程模板不存在");
                return "redirect:/workflow/templates";
            }

            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);

            // 删除模板
            templateService.deleteTemplate(id);

            // 记录活动日志
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logDelete(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "删除流程模板: " + templateToDelete.get().getTemplateName(),
                        ipAddress,
                        "WorkflowTemplate",
                        id,
                        getAccessType()
                );
            }

            redirectAttributes.addFlashAttribute("message", "流程模板删除成功");
        } catch (Exception e) {
            logger.error("删除流程模板时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除流程模板失败: " + e.getMessage());
        }
        return "redirect:/workflow/templates";
    }

    /**
     * 切换流程模板状态
     */
    @RequestMapping(value = "/{id}/toggle-status", method = {RequestMethod.POST, RequestMethod.GET})
    public String toggleTemplateStatus(@PathVariable("id") Long id, RedirectAttributes redirectAttributes) {
        try {
            // 使用JPA方法查询模板信息
            Optional<WorkflowTemplate> templateOpt = templateService.findTemplateById(id);
            if (!templateOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程模板不存在");
                return "redirect:/workflow/templates";
            }

            WorkflowTemplate template = templateOpt.get();
            String templateName = template.getTemplateName();
            boolean currentEnabled = template.getEnabled() != null ? template.getEnabled() : false;

            // 计算新状态
            boolean newStatus = !currentEnabled;

            // 使用服务层方法更新状态
            templateService.toggleTemplateStatus(id, newStatus);
            logger.info("成功更新模板状态: ID={}, 新状态={}", id, newStatus);

            // 获取当前登录用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);

            // 记录活动日志
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        (newStatus ? "启用" : "禁用") + "流程模板: " + templateName,
                        ipAddress,
                        "WorkflowTemplate",
                        id,
                        getAccessType()
                );
            }

            redirectAttributes.addFlashAttribute("message", "流程模板状态已" + (newStatus ? "启用" : "禁用"));
        } catch (Exception e) {
            logger.error("切换流程模板状态时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "切换流程模板状态失败: " + e.getMessage());
        }
        return "redirect:/workflow/templates";
    }
}
