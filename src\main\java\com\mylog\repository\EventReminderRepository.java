package com.mylog.repository;

import com.mylog.model.EventReminder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件提醒Repository接口
 */
@Repository
public interface EventReminderRepository extends JpaRepository<EventReminder, Long> {

       /**
        * 根据事件ID查找提醒列表
        */
       List<EventReminder> findByEventIdOrderByReminderTimeAsc(Long eventId);

       /**
        * 查找未发送的提醒
        */
       List<EventReminder> findByIsSentFalseOrderByReminderTimeAsc();

       /**
        * 查找需要发送的提醒（时间已到且未发送）
        */
       @Query("SELECT r FROM EventReminder r WHERE r.isSent = false AND r.reminderTime <= :currentTime ORDER BY r.reminderTime ASC")
       List<EventReminder> findPendingReminders(@Param("currentTime") LocalDateTime currentTime);

       /**
        * 根据提醒类型查找提醒
        */
       List<EventReminder> findByReminderTypeOrderByReminderTimeAsc(EventReminder.ReminderType reminderType);

       /**
        * 根据时间范围查找提醒
        */
       @Query("SELECT r FROM EventReminder r WHERE r.reminderTime >= :startTime AND r.reminderTime <= :endTime ORDER BY r.reminderTime ASC")
       List<EventReminder> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 统计事件的提醒数量
        */
       long countByEventId(Long eventId);

       /**
        * 删除事件的所有提醒
        */
       void deleteByEventId(Long eventId);

       /**
        * 统计用户在指定时间段内需要签到的提醒数量
        */
       @Query("SELECT COUNT(r) FROM EventReminder r JOIN r.event e WHERE e.creatorId = CAST(:userId AS long) " +
                     "AND r.requiresCheckIn = true AND r.reminderTime >= :startTime AND r.reminderTime <= :endTime")
       long countByUserIdAndRequiresCheckInTrueAndReminderTimeBetween(
                     @Param("userId") String userId,
                     @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 查找用户待签到事件（未签到且需要签到的提醒）
        * 修改查询逻辑：查找所有需要签到的提醒，通过LEFT JOIN检查用户是否已签到
        * 同时确保事件是该用户创建的
        */
       @Query("SELECT r FROM EventReminder r " +
                     "JOIN r.event e " +
                     "LEFT JOIN EventCheckIn c ON c.eventReminder = r AND c.userId = :userId " +
                     "WHERE r.requiresCheckIn = true " +
                     "AND r.reminderTime >= :startTime AND r.reminderTime <= :endTime " +
                     "AND c.id IS NULL " +
                     "ORDER BY r.reminderTime DESC")
       List<EventReminder> findPendingCheckInsForUser(@Param("userId") Long userId,
                     @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 根据事件ID查找需要签到的提醒
        */
       List<EventReminder> findByEventIdAndRequiresCheckInTrue(Long eventId);
}
