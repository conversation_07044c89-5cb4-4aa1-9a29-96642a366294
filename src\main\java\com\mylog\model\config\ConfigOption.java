package com.mylog.model.config;

import jakarta.persistence.*;
import lombok.Data;
import java.io.Serializable;

@Entity
@Table(name = "ConfigOptions")
@Data
@IdClass(ConfigOption.ConfigOptionId.class)
public class ConfigOption {
    
    @Id
    @Column(name = "category", nullable = false)
    private String category;
    
    @Id
    @Column(name = "value", nullable = false)
    private String value;
    
    @Column(name = "sort_order")
    private Integer sortOrder;
      @Column(name = "ratio")
    private Double ratio;
    
    @Column(name = "ratio2")
    private Double ratio2;
    
    @Column(name = "remark")
    private String remark;
    
    // 默认构造函数
    public ConfigOption() {}
    
    // 构造函数
    public ConfigOption(String category, String value, Integer sortOrder) {
        this.category = category;
        this.value = value;
        this.sortOrder = sortOrder;
    }
    
    public ConfigOption(String category, String value, Integer sortOrder, Double ratio, String remark) {
        this.category = category;
        this.value = value;
        this.sortOrder = sortOrder;
        this.ratio = ratio;
        this.remark = remark;
    }
    
    // 复合主键类
    @Data
    public static class ConfigOptionId implements Serializable {
        private String category;
        private String value;
        
        public ConfigOptionId() {}
        
        public ConfigOptionId(String category, String value) {
            this.category = category;
            this.value = value;
        }
    }
}
