@echo off
echo 使用本地数据目录启动MyLog Web应用程序...

REM 确保存在必要的目录
if not exist "logs" mkdir logs
if not exist "data" mkdir data

echo 检查数据库文件...
if not exist "data\LogManagement.db" (
    echo [警告] 未找到data\LogManagement.db文件
    echo 请先运行 mylog-deploy\data-migration.bat 迁移数据库文件
    echo 或手动将数据库文件复制到data目录
    pause
    exit /b 1
)

echo 启动应用程序...
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dspring.profiles.active=local"

echo 应用程序已关闭
pause 