/**
 * 日历管理JavaScript
 */
class CalendarManager {
    constructor() {
        console.log('🚀 CalendarManager构造函数被调用');
        this.currentDate = new Date();
        this.viewMode = 'month'; // month, week, day
        this.selectedDate = null;
        this.calendars = [];
        this.events = [];
        this.visibleCalendars = new Set();
        this.personnel = []; // 人员数据

        console.log('📅 当前日期:', this.currentDate);
        this.init();
    } async init() {
        try {
            this.bindEvents();
            this.loadPersonnel();
            await this.loadCalendars();
            await this.loadEvents();
            this.renderCalendar();
            this.renderMiniCalendar();
        } catch (error) {
            console.error('CalendarManager初始化失败:', error);
        }
    }

    bindEvents() {
        // 导航按钮
        document.getElementById('prevMonth').addEventListener('click', () => this.previousMonth());
        document.getElementById('nextMonth').addEventListener('click', () => this.nextMonth());
        document.getElementById('today').addEventListener('click', () => this.goToday());

        // 视图切换
        document.getElementById('monthView').addEventListener('click', () => this.setViewMode('month'));
        document.getElementById('weekView').addEventListener('click', () => this.setViewMode('week'));
        document.getElementById('dayView').addEventListener('click', () => this.setViewMode('day'));

        // 表单提交
        document.getElementById('eventForm').addEventListener('submit', (e) => this.saveEvent(e));
        document.getElementById('calendarForm').addEventListener('submit', (e) => this.saveCalendar(e));

        // 删除按钮
        document.getElementById('deleteEvent').addEventListener('click', () => this.deleteEvent());
        document.getElementById('deleteCalendar').addEventListener('click', () => this.deleteCalendar());

        // 全天事件切换
        document.getElementById('isAllDay').addEventListener('change', (e) => this.toggleAllDay(e.target.checked));

        // 重复事件切换
        document.getElementById('isRecurring').addEventListener('change', (e) => this.toggleRecurring(e.target.checked));

        // 添加提醒
        document.getElementById('addReminder').addEventListener('click', () => this.addReminderRow());

        // 编辑事件按钮
        document.getElementById('editEventBtn').addEventListener('click', () => this.editEventFromDetail());

        // 模态框事件
        const eventModal = document.getElementById('eventModal');
        eventModal.addEventListener('hidden.bs.modal', () => this.resetEventForm());

        const calendarModal = document.getElementById('calendarModal');
        calendarModal.addEventListener('hidden.bs.modal', () => this.resetCalendarForm());
    }    // 日期导航
    async previousMonth() {
        if (this.viewMode === 'month') {
            // 先设置为当月第一天，避免日期溢出问题
            this.currentDate.setDate(1);
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        } else if (this.viewMode === 'week') {
            this.currentDate.setDate(this.currentDate.getDate() - 7);
        } else {
            this.currentDate.setDate(this.currentDate.getDate() - 1);
        }
        await this.loadEvents();
        this.renderCalendar();
        this.renderMiniCalendar();
    }

    async nextMonth() {
        if (this.viewMode === 'month') {
            // 先设置为当月第一天，避免日期溢出问题
            this.currentDate.setDate(1);
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        } else if (this.viewMode === 'week') {
            this.currentDate.setDate(this.currentDate.getDate() + 7);
        } else {
            this.currentDate.setDate(this.currentDate.getDate() + 1);
        }
        await this.loadEvents();
        this.renderCalendar();
        this.renderMiniCalendar();
    }

    async goToday() {
        this.currentDate = new Date();
        await this.loadEvents();
        this.renderCalendar();
        this.renderMiniCalendar();
    }

    async setViewMode(mode) {
        this.viewMode = mode;

        // 更新按钮状态
        document.querySelectorAll('#monthView, #weekView, #dayView').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(mode + 'View').classList.add('active');

        await this.loadEvents();
        this.renderCalendar();
    }    // 加载日历列表
    async loadCalendars() {
        try {
            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch('/api/calendars/', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            }); if (response.ok) {
                const result = await response.json();
                this.calendars = result.data || result; // 处理后端包装格式
                this.calendars.forEach(calendar => {
                    // 确保ID是数字类型，统一数据类型
                    const calendarId = typeof calendar.id === 'string' ? parseInt(calendar.id) : calendar.id;
                    this.visibleCalendars.add(calendarId);
                    console.log(`添加可见日历: ID=${calendarId} (type: ${typeof calendarId}), 名称=${calendar.name}`);
                });
                console.log('可见日历集合:', [...this.visibleCalendars]);
                this.renderCalendarList();
                this.updateCalendarOptions();
            } else {
                console.error('Failed to load calendars');
            }
        } catch (error) {
            console.error('Error loading calendars:', error);
        }
    }    // 加载事件
    async loadEvents() {
        try {
            const startDate = this.getViewStartDate();
            const endDate = this.getViewEndDate();

            // 使用标准日期时间格式进行API调用
            const startTimeFormatted = this.formatStandardDateTime(startDate);
            const endTimeFormatted = this.formatStandardDateTime(endDate);

            console.log('Loading events from:', startTimeFormatted, 'to:', endTimeFormatted); const apiUrl = `/api/events/range?startTime=${encodeURIComponent(startTimeFormatted)}&endTime=${encodeURIComponent(endTimeFormatted)}`;
            console.log('API URL:', apiUrl);

            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch(apiUrl, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });
            console.log('Response status:', response.status);

            if (response.ok) {
                const result = await response.json();
                console.log('Received events data:', result);
                this.events = result.data || result; // 处理后端包装格式
                console.log('Processed events:', this.events);
            } else {
                console.error('Failed to load events, status:', response.status);
                const errorText = await response.text();
                console.error('Error response:', errorText);
            }
        } catch (error) {
            console.error('Error loading events:', error);
        }
    }

    // 渲染日历列表
    renderCalendarList() {
        const container = document.getElementById('calendarList');
        container.innerHTML = '';

        this.calendars.forEach(calendar => {
            const item = document.createElement('div');
            item.className = 'calendar-list-item';

            item.innerHTML = `
                <input type="checkbox" class="calendar-checkbox" ${this.visibleCalendars.has(calendar.id) ? 'checked' : ''} 
                       onchange="calendarManager.toggleCalendarVisibility(${calendar.id}, this.checked)">
                <div class="calendar-color" style="background-color: ${calendar.color}"></div>
                <div class="calendar-name" title="${calendar.description || calendar.name}">${calendar.name}</div>
                <div class="calendar-actions">
                    <button class="btn btn-sm btn-outline-secondary" onclick="calendarManager.editCalendar(${calendar.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                </div>
            `;

            container.appendChild(item);
        });
    }

    // 更新日历选择选项
    updateCalendarOptions() {
        const select = document.getElementById('eventCalendar');
        select.innerHTML = '';

        this.calendars.forEach(calendar => {
            const option = document.createElement('option');
            option.value = calendar.id;
            option.textContent = calendar.name;
            if (calendar.isDefault) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }    // 切换日历可见性
    toggleCalendarVisibility(calendarId, visible) {
        console.log('toggleCalendarVisibility被调用:', calendarId, visible);
        
        // 确保ID是数字类型
        const id = typeof calendarId === 'string' ? parseInt(calendarId) : calendarId;

        if (visible) {
            this.visibleCalendars.add(id);
            console.log(`显示日历 ${id}`);
        } else {
            this.visibleCalendars.delete(id);
            console.log(`隐藏日历 ${id}`);
        }
        console.log('当前可见日历:', [...this.visibleCalendars]);
        this.renderCalendar();
    }// 渲染主日历
    renderCalendar() {
        console.log('Rendering calendar with viewMode:', this.viewMode);
        console.log('Current date:', this.currentDate);
        console.log('Available events:', this.events);
        console.log('Visible calendars:', [...this.visibleCalendars]);

        const container = document.getElementById('mainCalendar');
        const title = document.getElementById('calendarTitle');

        if (this.viewMode === 'month') {
            this.renderMonthView(container);
            title.textContent = this.formatMonthTitle();
        } else if (this.viewMode === 'week') {
            this.renderWeekView(container);
            title.textContent = this.formatWeekTitle();
        } else {
            this.renderDayView(container);
            title.textContent = this.formatDayTitle();
        }
    }

    // 渲染月视图
    renderMonthView(container) {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();

        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const firstWeek = new Date(firstDay);
        firstWeek.setDate(firstWeek.getDate() - firstWeek.getDay());

        let html = '<div class="month-view"><table>';

        // 表头
        html += '<thead><tr>';
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        weekdays.forEach(day => {
            html += `<th>${day}</th>`;
        });
        html += '</tr></thead><tbody>';

        // 日期格子
        const current = new Date(firstWeek);
        for (let week = 0; week < 6; week++) {
            html += '<tr>';
            for (let day = 0; day < 7; day++) {
                const isCurrentMonth = current.getMonth() === month;
                const isToday = this.isToday(current);
                const dayEvents = this.getEventsForDate(current);

                html += `<td class="${isCurrentMonth ? '' : 'other-month'} ${isToday ? 'today' : ''}" 
                              onclick="calendarManager.selectDate('${this.formatDate(current)}')">`;
                html += `<div class="day-number">${current.getDate()}</div>`;                // 添加事件
                dayEvents.forEach(event => {
                    // 确保数据类型一致性
                    const eventCalendarId = typeof event.calendarId === 'string' ? parseInt(event.calendarId) : event.calendarId;
                    
                    // 判断事件是否可见：
                    // 1. 事件的日历在当前用户的可见日历集合中，或者
                    // 2. 事件来自共享日历（由后端的 getAccessibleEventsByTimeRange 方法已经过滤过）
                    // 由于loadEvents已经通过/api/events/range获取了用户可访问的所有事件（包括共享日历事件），
                    // 这里我们可以信任后端的过滤结果，直接显示所有返回的事件                    // 判断事件是否为自建日历
                    const isOwnCalendar = this.checkIfOwnCalendar(event);

                    const isOwnCalendarVisible = this.visibleCalendars.has(eventCalendarId);
                    const isAccessibleEvent = true; // 后端已经过滤了可访问的事件

                    // 根据事件类型决定可见性
                    let isVisible;
                    if (isOwnCalendar) {
                        // 如果是自建日历事件，由用户的日历可见性设置决定
                        isVisible = isOwnCalendarVisible;
                    } else {
                        // 如果是共享日历事件，始终可见（后端已过滤）
                        isVisible = isAccessibleEvent;
                    }


                    console.log(`事件 "${event.title}": calendarId=${event.calendarId} (${typeof event.calendarId}) -> ${eventCalendarId} (${typeof eventCalendarId}), 自有日历=${isOwnCalendar}, 日历可见=${isOwnCalendarVisible}, 最终可见=${isVisible}`);

                    if (isVisible) {
                        html += this.renderEventItem(event);
                    }
                });

                html += '</td>';
                current.setDate(current.getDate() + 1);
            }
            html += '</tr>';
        }

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // 渲染周视图
    renderWeekView(container) {
        // 实现周视图
        container.innerHTML = '<div class="week-view">周视图功能开发中...</div>';
    }

    // 渲染日视图
    renderDayView(container) {
        // 实现日视图        container.innerHTML = '<div class="day-view">日视图功能开发中...</div>';
    }

    // 渲染小日历
    renderMiniCalendar() {
        const container = document.getElementById('miniCalendar');
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();

        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const firstWeek = new Date(firstDay);
        firstWeek.setDate(firstWeek.getDate() - firstWeek.getDay());

        let html = `<div class="mini-calendar">
                     <div class="d-flex justify-content-between align-items-center mb-2">
                       <button class="btn btn-sm btn-outline-secondary" onclick="calendarManager.miniPrevMonth()">
                         <i class="bi bi-chevron-left"></i>
                       </button>
                       <span class="fw-bold">${year}年${month + 1}月</span>
                       <button class="btn btn-sm btn-outline-secondary" onclick="calendarManager.miniNextMonth()">
                         <i class="bi bi-chevron-right"></i>
                       </button>
                     </div>
                     <table>`;

        // 表头
        html += '<thead><tr>';
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        weekdays.forEach(day => {
            html += `<th>${day}</th>`;
        });
        html += '</tr></thead><tbody>';

        // 日期
        const current = new Date(firstWeek);
        for (let week = 0; week < 6; week++) {
            html += '<tr>';
            for (let day = 0; day < 7; day++) {
                const isCurrentMonth = current.getMonth() === month;
                const isToday = this.isToday(current);

                html += `<td class="${isCurrentMonth ? '' : 'other-month'} ${isToday ? 'today' : ''}" 
                              onclick="calendarManager.goToDate('${this.formatDate(current)}')">`;
                html += current.getDate();
                html += '</td>';
                current.setDate(current.getDate() + 1);
            }
            html += '</tr>';
        }

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }    // 小日历导航
    async miniPrevMonth() {
        // 先设置为当月第一天，避免日期溢出问题
        this.currentDate.setDate(1);
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        await this.loadEvents();
        this.renderMiniCalendar();
        this.renderCalendar();
    }

    async miniNextMonth() {
        // 先设置为当月第一天，避免日期溢出问题
        this.currentDate.setDate(1);
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        await this.loadEvents();
        this.renderMiniCalendar();
        this.renderCalendar();
    }

    // 跳转到指定日期
    async goToDate(dateStr) {
        this.currentDate = new Date(dateStr);
        await this.loadEvents();
        this.renderCalendar();
        this.renderMiniCalendar();
    }

    // 选择日期（创建事件）
    selectDate(dateStr) {
        this.selectedDate = dateStr;
        this.showEventModal();

        // 设置默认开始时间
        const date = new Date(dateStr);
        const now = new Date();
        if (this.isToday(date)) {
            date.setHours(now.getHours() + 1, 0, 0, 0);
        } else {
            date.setHours(9, 0, 0, 0);
        }
        document.getElementById('startTime').value = this.formatDateTimeLocal(date);

        // 设置默认结束时间
        const endDate = new Date(date);
        endDate.setHours(endDate.getHours() + 1);
        document.getElementById('endTime').value = this.formatDateTimeLocal(endDate);
    }    // 显示事件模态框
    async showEventModal(eventId = null) {
        const modal = new bootstrap.Modal(document.getElementById('eventModal'));

        if (eventId) {
            await this.loadEventForEdit(eventId);
            document.getElementById('eventModalLabel').textContent = '编辑事件';
            
            // 根据权限控制删除按钮显示
            await this.updateEventModalPermissions(eventId);
        } else {
            document.getElementById('eventModalLabel').textContent = '新建事件';
            document.getElementById('deleteEvent').style.display = 'none';
        }

        modal.show();
    }

    // 显示日历模态框
    showCalendarModal(calendarId = null) {
        const modal = new bootstrap.Modal(document.getElementById('calendarModal'));

        if (calendarId) {
            this.loadCalendarForEdit(calendarId);
            document.getElementById('calendarModalLabel').textContent = '编辑日历';
            document.getElementById('deleteCalendar').style.display = 'inline-block';
        } else {
            document.getElementById('calendarModalLabel').textContent = '新建日历';
            document.getElementById('deleteCalendar').style.display = 'none';
        }

        modal.show();
    }
    // 保存事件
    async saveEvent(e) {
        // 生成唯一的请求ID用于追踪
        const requestId = 'REQ_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
        console.log(`🔄 [${requestId}] 开始处理事件保存请求`);

        e.preventDefault(); const formData = new FormData(e.target);
        const eventData = Object.fromEntries(formData.entries());        // 删除提醒相关的字段，这些字段应该通过 collectReminders() 单独处理
        delete eventData.reminderTime;
        delete eventData.reminderType;
        delete eventData.reminderMessage;
        delete eventData.requiresCheckIn;
        delete eventData.checkInWindowMinutes;
        delete eventData.reminderRecipients; // 删除接收人字段，因为这通过reminders数组处理

        console.log(`📝 [${requestId}] 原始表单数据:`, eventData);

        // 处理复选框
        eventData.isAllDay = document.getElementById('isAllDay').checked;
        eventData.isRecurring = document.getElementById('isRecurring').checked;

        console.log(`☑️ [${requestId}] 复选框状态 - 全天:${eventData.isAllDay}, 重复:${eventData.isRecurring}`);

        // 处理日期时间格式 - 转换为标准格式 "YYYY-MM-DD HH:MM:SS"
        if (eventData.startTime) {
            console.log(`⏰ [${requestId}] 原始开始时间:`, eventData.startTime);
            // HTML datetime-local返回的格式为: "YYYY-MM-DDThh:mm"
            // 需要替换T为空格，并添加秒
            let startDateStr = eventData.startTime.replace('T', ' ') + ':00';
            // 如果是全天事件，设置时间为00:00:00
            if (eventData.isAllDay) {
                startDateStr = startDateStr.split(' ')[0] + ' 00:00:00';
            }
            eventData.startTime = startDateStr;
            console.log('格式化后的开始时间:', eventData.startTime);
        }

        if (eventData.endTime) {
            console.log('原始结束时间:', eventData.endTime);
            let endDateStr = eventData.endTime.replace('T', ' ') + ':00';
            // 如果是全天事件，设置时间为23:59:59
            if (eventData.isAllDay) {
                endDateStr = endDateStr.split(' ')[0] + ' 23:59:59';
            }
            eventData.endTime = endDateStr;
            console.log('格式化后的结束时间:', eventData.endTime);
        } else if (eventData.startTime && eventData.isAllDay) {
            // 如果是全天事件且没有设置结束时间，默认设置为与开始日期相同，但时间为23:59:59
            eventData.endTime = eventData.startTime.split(' ')[0] + ' 23:59:59';
            console.log('全天事件的默认结束时间:', eventData.endTime);
        } else if (eventData.startTime) {
            // 如果没有设置结束时间，默认设置为开始时间后1小时
            const startDate = this.parseStandardDateTime(eventData.startTime);
            if (startDate) {
                startDate.setHours(startDate.getHours() + 1);
                eventData.endTime = this.formatStandardDateTime(startDate);
                console.log('默认结束时间(开始时间+1小时):', eventData.endTime);
            }
        }

        // 处理循环结束日期
        if (eventData.recurrenceEndDate) {
            if (eventData.recurrenceEndDate.includes('T')) {
                eventData.recurrenceEndDate = eventData.recurrenceEndDate.replace('T', ' ') + ':00';
            } else {
                // 如果只有日期部分，添加时间部分
                eventData.recurrenceEndDate = eventData.recurrenceEndDate + ' 23:59:59';
            }
        }        // 收集提醒数据
        eventData.reminders = this.collectReminders();
        console.log(`🔔 [${requestId}] 收集到的提醒数据:`, eventData.reminders);

        // 设置创建用户ID - 这是必须的
        eventData.creatorId = 1; // 默认用户ID
        console.log(`👤 [${requestId}] 设置默认创建者ID: ${eventData.creatorId}`);

        // 尝试获取用户ID - 从系统中获取当前登录用户ID
        try {
            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            console.log(`🔐 [${requestId}] CSRF Token存在: ${!!token}, Header: ${header}`);

            // 从后端获取当前用户信息
            const userResponse = await fetch('/api/user/current', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });

            console.log(`👤 [${requestId}] 用户API响应状态: ${userResponse.status}`);

            if (userResponse.ok) {
                const userData = await userResponse.json();
                if (userData && userData.id) {
                    eventData.creatorId = userData.id;
                    console.log(`✅ [${requestId}] 成功获取当前用户ID作为创建者:`, userData.id);
                } else {
                    console.warn(`⚠️ [${requestId}] 获取的用户数据中没有有效的ID，使用默认值`);
                }
            } else {
                // 如果获取失败，使用默认值
                console.warn(`⚠️ [${requestId}] 无法获取当前用户ID，使用默认值`);
            }
        } catch (error) {
            console.error(`❌ [${requestId}] 获取用户ID时出错:`, error);
            // 继续使用默认值
        }

        // 添加调试日志
        console.log(`📤 [${requestId}] 准备发送的事件数据:`, JSON.stringify(eventData, null, 2));

        try {
            const method = eventData.id ? 'PUT' : 'POST';
            const url = eventData.id ? `/api/events/${eventData.id}` : '/api/events';

            console.log(`🚀 [${requestId}] 发送${method}请求到 ${url}`);
            console.log(`📊 [${requestId}] 请求开始时间: ${new Date().toISOString()}`);

            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const fetchStart = performance.now();
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Request-ID': requestId, // 添加请求ID到header
                    [header]: token
                },
                body: JSON.stringify(eventData)
            });
            const fetchEnd = performance.now();

            console.log(`📡 [${requestId}] 服务器响应状态: ${response.status}`);
            console.log(`⏱️ [${requestId}] 请求耗时: ${(fetchEnd - fetchStart).toFixed(2)}ms`);
            console.log(`📊 [${requestId}] 响应时间: ${new Date().toISOString()}`);

            if (response.ok) {
                const result = await response.json();
                console.log(`✅ [${requestId}] 服务器响应数据:`, result);

                bootstrap.Modal.getInstance(document.getElementById('eventModal')).hide();
                await this.loadEvents();
                this.renderCalendar();
                this.showSuccess('事件保存成功');
                console.log(`🎉 [${requestId}] 事件保存流程完成`);
            } else {
                const error = await response.text();
                console.error(`❌ [${requestId}] 保存事件失败:`, error);
                this.showError('保存失败: ' + error);
            }
        } catch (error) {
            console.error(`💥 [${requestId}] Error saving event:`, error);
            this.showError('保存失败，请重试');
        }
    }
    // 保存日历
    async saveCalendar(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const calendarData = Object.fromEntries(formData.entries());

        // 处理复选框
        calendarData.isDefault = document.getElementById('isDefault').checked;
        calendarData.isShared = document.getElementById('isShared').checked;

        // 设置默认用户ID - 这是必须的
        calendarData.userId = 1; // 默认用户ID        // 尝试获取用户ID - 从系统中获取当前登录用户ID
        try {
            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            // 从后端获取当前用户信息
            const userResponse = await fetch('/api/user/current', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });
            if (userResponse.ok) {
                const userData = await userResponse.json();
                if (userData && userData.id) {
                    calendarData.userId = userData.id;
                    console.log('成功获取当前用户ID:', userData.id);
                } else {
                    console.warn('获取的用户数据中没有有效的ID，使用默认值');
                }
            } else {
                // 如果获取失败，使用默认值
                console.warn('无法获取当前用户ID，使用默认值');
            }
        } catch (error) {
            console.error('获取用户ID时出错:', error);
        }

        // 确保userId不为null或undefined
        if (!calendarData.userId) {
            calendarData.userId = 1;
            console.log('最终使用默认用户ID: 1');
        }

        console.log('准备发送的日历数据:', calendarData); try {
            const method = calendarData.id ? 'PUT' : 'POST';
            const url = calendarData.id ? `/api/calendars/${calendarData.id}` : '/api/calendars/';

            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                },
                body: JSON.stringify(calendarData)
            });

            if (response.ok) {
                const responseData = await response.json();
                console.log('服务器返回的响应:', responseData);

                if (responseData && responseData.success) {
                    bootstrap.Modal.getInstance(document.getElementById('calendarModal')).hide();
                    await this.loadCalendars();
                    this.showSuccess('日历保存成功');
                } else {
                    this.showError('保存失败: ' + (responseData.message || '未知错误'));
                }
            } else {
                const error = await response.text();
                this.showError('保存失败: ' + error);
            }
        } catch (error) {
            console.error('Error saving calendar:', error);
            this.showError('保存失败，请重试');
        }
    }

    // 删除事件
    async deleteEvent() {
        const eventId = document.getElementById('eventId').value;
        if (!eventId) return; if (!confirm('确定要删除这个事件吗？')) return;

        try {
            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch(`/api/events/${eventId}`, {
                method: 'DELETE',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            }); if (response.ok) {
                bootstrap.Modal.getInstance(document.getElementById('eventModal')).hide();
                await this.loadEvents();
                this.renderCalendar();
                this.showSuccess('事件删除成功');
            } else {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.message || '删除失败';
                this.showError('删除失败: ' + errorMessage);
            }
        } catch (error) {
            console.error('Error deleting event:', error);
            this.showError('删除失败，请重试: ' + error.message);
        }
    }

    // 删除日历
    async deleteCalendar() {
        const calendarId = document.getElementById('calendarId').value;
        if (!calendarId) return; if (!confirm('确定要删除这个日历吗？删除后其中的所有事件也会被删除！')) return;

        try {
            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch(`/api/calendars/${calendarId}`, {
                method: 'DELETE',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            }); if (response.ok) {
                bootstrap.Modal.getInstance(document.getElementById('calendarModal')).hide();
                await this.loadCalendars();
                await this.loadEvents();
                this.renderCalendar();
                this.showSuccess('日历删除成功');
            } else {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.message || '删除失败';
                this.showError('删除失败: ' + errorMessage);
            }
        } catch (error) {
            console.error('Error deleting calendar:', error);
            this.showError('删除失败，请重试: ' + error.message);
        }
    }    // 编辑日历
    editCalendar(calendarId) {
        this.showCalendarModal(calendarId);
    }    // 加载事件用于编辑
    async loadEventForEdit(eventId) {
        try {
            // 获取CSRF token
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch(`/api/events/${eventId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });
            if (response.ok) {
                const result = await response.json();
                const event = result.data || result; // 处理后端包装格式
                this.populateEventForm(event);
            }
        } catch (error) {
            console.error('Error loading event:', error);
        }
    }

    // 加载日历用于编辑
    async loadCalendarForEdit(calendarId) {
        try {
            const calendar = this.calendars.find(c => c.id === calendarId);
            if (calendar) {
                this.populateCalendarForm(calendar);
            }
        } catch (error) {
            console.error('Error loading calendar:', error);
        }
    }    // 填充事件表单
    populateEventForm(event) {
        document.getElementById('eventId').value = event.id || '';
        document.getElementById('eventTitle').value = event.title || '';
        document.getElementById('eventCalendar').value = event.calendarId || '';
        document.getElementById('isAllDay').checked = event.isAllDay || false;

        // 处理日期时间格式 - 支持标准格式 "YYYY-MM-DD HH:MM:SS"
        if (event.startTime) {
            // 转换标准格式为HTML datetime-local所需的格式
            const startDate = this.parseStandardDateTime(event.startTime);
            document.getElementById('startTime').value = this.formatDateTimeLocal(startDate);
        }

        if (event.endTime) {
            const endDate = this.parseStandardDateTime(event.endTime);
            document.getElementById('endTime').value = this.formatDateTimeLocal(endDate);
        }

        document.getElementById('eventLocation').value = event.location || '';
        document.getElementById('eventType').value = event.eventType || 'MEETING';
        document.getElementById('eventPriority').value = event.priority || 'NORMAL';
        document.getElementById('eventDescription').value = event.description || '';
        document.getElementById('isRecurring').checked = event.isRecurring || false;

        if (event.isRecurring) {
            document.getElementById('recurrenceType').value = event.recurrenceType || '';
            document.getElementById('recurrenceInterval').value = event.recurrenceInterval || 1;

            if (event.recurrenceEndDate) {
                const recEndDate = this.parseStandardDateTime(event.recurrenceEndDate);
                document.getElementById('recurrenceEndDate').value = this.formatDate(recEndDate);
            } else {
                document.getElementById('recurrenceEndDate').value = '';
            }

            this.toggleRecurring(true);
        }

        this.toggleAllDay(event.isAllDay || false);

        // 加载提醒
        this.loadReminders(event.reminders || []);
    }

    // 填充日历表单
    populateCalendarForm(calendar) {
        document.getElementById('calendarId').value = calendar.id || '';
        document.getElementById('calendarName').value = calendar.name || '';
        document.getElementById('calendarDescription').value = calendar.description || '';
        document.getElementById('calendarColor').value = calendar.color || '#007bff';
        document.getElementById('isDefault').checked = calendar.isDefault || false;
        document.getElementById('isShared').checked = calendar.isShared || false;
    }

    // 重置表单
    resetEventForm() {
        document.getElementById('eventForm').reset();
        document.getElementById('eventId').value = '';
        document.getElementById('recurringOptions').style.display = 'none';
        document.getElementById('endTimeRow').style.display = 'block';
        this.clearReminders();
    } resetCalendarForm() {
        document.getElementById('calendarForm').reset();
        document.getElementById('calendarId').value = '';
        document.getElementById('calendarColor').value = '#007bff';
        document.getElementById('isDefault').checked = false;
        document.getElementById('isShared').checked = false;
    }

    // 切换全天事件
    toggleAllDay(isAllDay) {
        const endTimeRow = document.getElementById('endTimeRow');
        const startTime = document.getElementById('startTime');
        const endTime = document.getElementById('endTime');

        if (isAllDay) {
            endTimeRow.style.display = 'none';
            startTime.type = 'date';
            endTime.type = 'date';
        } else {
            endTimeRow.style.display = 'block';
            startTime.type = 'datetime-local';
            endTime.type = 'datetime-local';
        }
    }

    // 切换重复事件
    toggleRecurring(isRecurring) {
        const recurringOptions = document.getElementById('recurringOptions');
        recurringOptions.style.display = isRecurring ? 'block' : 'none';
    }    // 添加提醒行
    addReminderRow() {
        const container = document.getElementById('reminderList');
        const reminderItem = document.createElement('div');
        reminderItem.className = 'reminder-item';

        // 确定最终使用的人员数据
        let finalPersonnelData = null;

        // 优先使用 window.personnelData
        if (window.personnelData && Array.isArray(window.personnelData) && window.personnelData.length > 0) {
            finalPersonnelData = window.personnelData;
        } else if (this.personnel && Array.isArray(this.personnel) && this.personnel.length > 0) {
            finalPersonnelData = this.personnel;
        } else {
            // 重新加载人员数据
            this.loadPersonnel();
            if (this.personnel && Array.isArray(this.personnel) && this.personnel.length > 0) {
                finalPersonnelData = this.personnel;
            } else if (window.personnelData && window.personnelData.length > 0) {
                this.personnel = window.personnelData;
                finalPersonnelData = window.personnelData;
            } else {
                // 尝试从页面DOM中提取人员数据
                const personnelSpan = document.querySelector('small.text-muted span');
                if (personnelSpan && personnelSpan.textContent) {
                    const personnelCount = parseInt(personnelSpan.textContent);
                    if (personnelCount > 0) {
                        finalPersonnelData = [];
                        for (let i = 1; i <= personnelCount; i++) {
                            finalPersonnelData.push(`用户${i}`);
                        }
                    }
                }

                // 如果还是没有数据，使用默认数据
                if (!finalPersonnelData || finalPersonnelData.length === 0) {
                    finalPersonnelData = ['张三', '李四', '王五', '测试用户'];
                }
            }
        }

        // 生成人员选项
        let personnelOptions = '';
        if (finalPersonnelData && Array.isArray(finalPersonnelData) && finalPersonnelData.length > 0) {
            personnelOptions = finalPersonnelData.map(person => {
                const personName = typeof person === 'string' ? person : (person.name || person.toString());
                return `<option value="${personName}">${personName}</option>`;
            }).join('');
        }        reminderItem.innerHTML = `
            <div class="reminder-columns">
                <div class="reminder-col-1">
                    <div class="reminder-field">
                        <select class="form-select form-select-sm" name="reminderTime" required>
                            <option value="15">15分钟前</option>
                            <option value="30">30分钟前</option>
                            <option value="60">1小时前</option>
                            <option value="120">2小时前</option>
                            <option value="1440">1天前</option>
                            <option value="10080">1周前</option>
                        </select>
                    </div>
                    <div class="reminder-field">
                        <select class="form-select form-select-sm" name="reminderType" required>
                            <option value="NOTIFICATION">通知</option>
                            <option value="EMAIL">邮件</option>
                            <option value="SMS">短信</option>
                        </select>
                    </div>
                    <div class="reminder-field">
                        <input type="text" class="form-control form-control-sm" name="reminderMessage" placeholder="提醒内容（可选）">
                    </div>
                </div>
                <div class="reminder-col-2">
                    <select class="form-select form-select-sm" name="reminderRecipients" multiple title="选择接收人（可多选）">
                        <option value="">不指定接收人</option>
                        ${personnelOptions}
                    </select>
                </div>
                <div class="reminder-col-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="requiresCheckIn" id="checkIn_${Date.now()}" onchange="this.closest('.reminder-item').querySelector('.check-in-window').style.display = this.checked ? 'block' : 'none'">
                        <label class="form-check-label" for="checkIn_${Date.now()}">需要签到</label>
                    </div>
                </div>
                <div class="reminder-col-4">
                    <div class="check-in-window" style="display: none;">
                        <select class="form-select form-select-sm" name="checkInWindowMinutes">
                            <option value="15">15分钟窗口</option>
                            <option value="30" selected>30分钟窗口</option>
                            <option value="60">1小时窗口</option>
                            <option value="120">2小时窗口</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="reminder-actions">
                <button type="button" class="btn btn-sm btn-outline-danger btn-remove" onclick="calendarManager.removeReminderRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;container.appendChild(reminderItem);
    }

    // 安全删除提醒行
    removeReminderRow(button) {
        try {
            const reminderItem = button.closest('.reminder-item');
            if (reminderItem) {
                reminderItem.remove();
                console.log('提醒行已安全删除');
            } else {
                console.warn('未找到要删除的提醒行');
            }
        } catch (error) {
            console.error('删除提醒行时发生错误:', error);
        }
    }// 收集提醒数据
    collectReminders() {
        const reminders = [];
        const reminderItems = document.querySelectorAll('.reminder-item');        reminderItems.forEach((item, index) => {
            try {
                // 检查所有必需的元素是否存在
                const timeSelect = item.querySelector('select[name="reminderTime"]');
                const typeSelect = item.querySelector('select[name="reminderType"]');
                const messageInput = item.querySelector('input[name="reminderMessage"]');
                const checkInCheckbox = item.querySelector('input[name="requiresCheckIn"]');
                const checkInWindowSelect = item.querySelector('select[name="checkInWindowMinutes"]');
                const recipientsSelect = item.querySelector('select[name="reminderRecipients"]');

                // 如果任何必需元素缺失，跳过这个提醒项并记录警告
                if (!timeSelect || !typeSelect || !messageInput || !checkInCheckbox || !checkInWindowSelect || !recipientsSelect) {
                    console.warn(`提醒项 ${index + 1} 的DOM结构不完整，跳过收集`, {
                        timeSelect: !!timeSelect,
                        typeSelect: !!typeSelect,
                        messageInput: !!messageInput,
                        checkInCheckbox: !!checkInCheckbox,
                        checkInWindowSelect: !!checkInWindowSelect,
                        recipientsSelect: !!recipientsSelect,
                        itemHTML: item.innerHTML
                    });
                    return; // 跳过这个项目
                }

                const time = timeSelect.value;
                const type = typeSelect.value;
                const message = messageInput.value;
                const requiresCheckIn = checkInCheckbox.checked;
                const checkInWindowMinutes = checkInWindowSelect.value;

                // 收集接收人数据
                const selectedRecipients = Array.from(recipientsSelect.selectedOptions)
                    .map(option => option.value)
                    .filter(value => value !== ''); // 过滤掉空值
                const recipients = selectedRecipients.length > 0 ? selectedRecipients.join(',') : null;

                const reminderData = {
                    time: parseInt(time),
                    reminderType: type,
                    message: message,
                    requiresCheckIn: requiresCheckIn,
                    checkInWindowMinutes: requiresCheckIn ? parseInt(checkInWindowMinutes) : null,
                    recipients: recipients
                };
                
                // 如果是编辑模式，包含提醒ID以便更新而不是重新创建
                if (item.dataset.reminderId) {
                    reminderData.id = parseInt(item.dataset.reminderId);
                }
                
                reminders.push(reminderData);
            } catch (error) {
                console.error(`收集提醒项 ${index + 1} 时发生错误:`, error);
                console.error('出错的提醒项HTML:', item.innerHTML);
            }
        });

        return reminders;
    }    // 加载提醒
    loadReminders(reminders) {
        try {
            console.log('🔔 开始加载提醒数据:', reminders);
            this.clearReminders();
            
            if (!reminders || !Array.isArray(reminders)) {
                console.warn('提醒数据无效:', reminders);
                return;
            }
            
            reminders.forEach((reminder, index) => {
                try {
                    this.addReminderRow();
                    const lastItem = document.querySelector('.reminder-item:last-child');
                    
                    if (!lastItem) {
                        console.error(`无法找到新创建的提醒项 ${index + 1}`);
                        return;
                    }
                    
                    // 保存提醒ID，用于编辑时更新而不是重新创建
                    if (reminder.id) {
                        lastItem.dataset.reminderId = reminder.id;
                    }
                    
                    // 安全地设置各个字段值
                    const timeSelect = lastItem.querySelector('select[name="reminderTime"]');
                    const typeSelect = lastItem.querySelector('select[name="reminderType"]');
                    const messageInput = lastItem.querySelector('input[name="reminderMessage"]');
                    
                    if (timeSelect) timeSelect.value = reminder.time || 15;
                    if (typeSelect) typeSelect.value = reminder.reminderType || 'NOTIFICATION';
                    if (messageInput) messageInput.value = reminder.message || '';

                    // 设置接收人
                    if (reminder.recipients) {
                        const recipientsSelect = lastItem.querySelector('select[name="reminderRecipients"]');
                        if (recipientsSelect) {
                            const recipientsList = reminder.recipients.split(',');
                            Array.from(recipientsSelect.options).forEach(option => {
                                if (recipientsList.includes(option.value)) {
                                    option.selected = true;
                                }
                            });
                        }
                    }

                    // 设置签到相关字段
                    const checkInCheckbox = lastItem.querySelector('input[name="requiresCheckIn"]');
                    const checkInWindow = lastItem.querySelector('.check-in-window');
                    const windowSelect = lastItem.querySelector('select[name="checkInWindowMinutes"]');

                    if (reminder.requiresCheckIn && checkInCheckbox && checkInWindow && windowSelect) {
                        checkInCheckbox.checked = true;
                        checkInWindow.style.display = 'block';
                        if (reminder.checkInWindowMinutes) {
                            windowSelect.value = reminder.checkInWindowMinutes;
                        }
                    }
                    
                    console.log(`✅ 提醒项 ${index + 1} 加载成功`);
                } catch (error) {
                    console.error(`加载提醒项 ${index + 1} 时发生错误:`, error, reminder);
                }
            });
            
            console.log(`🎉 提醒加载完成，共加载 ${reminders.length} 个提醒`);
        } catch (error) {
            console.error('加载提醒数据时发生错误:', error);
        }
    }

    // 清空提醒
    clearReminders() {
        document.getElementById('reminderList').innerHTML = '';
    }    // 从详情编辑事件
    editEventFromDetail() {
        const eventId = document.getElementById('eventDetailModal').dataset.eventId;
        const detailModal = bootstrap.Modal.getInstance(document.getElementById('eventDetailModal'));

        // 确保详情模态框完全关闭后再打开编辑模态框
        const eventDetailModalElement = document.getElementById('eventDetailModal');
        const handleHidden = () => {
            eventDetailModalElement.removeEventListener('hidden.bs.modal', handleHidden);
            this.showEventModal(eventId);
        };

        eventDetailModalElement.addEventListener('hidden.bs.modal', handleHidden);
        detailModal.hide();
    }    // 显示事件详情
    async showEventDetail(eventId) {
        const event = this.events.find(e => e.id === eventId);
        if (!event) return;

        const modal = new bootstrap.Modal(document.getElementById('eventDetailModal'));
        const content = document.getElementById('eventDetailContent');

        // 构建基本信息
        let contentHtml = `
            <div class="event-detail-item">
                <div class="event-detail-label">标题</div>
                <div class="event-detail-value">${event.title}</div>
            </div>
            <div class="event-detail-item">
                <div class="event-detail-label">时间</div>
                <div class="event-detail-value">
                    ${this.formatEventTime(event)}
                </div>
            </div>
            ${event.location ? `
            <div class="event-detail-item">
                <div class="event-detail-label">地点</div>
                <div class="event-detail-value">${event.location}</div>
            </div>
            ` : ''}
            <div class="event-detail-item">
                <div class="event-detail-label">类型</div>
                <div class="event-detail-value">${this.getEventTypeText(event.eventType)}</div>
            </div>
            <div class="event-detail-item">
                <div class="event-detail-label">优先级</div>
                <div class="event-detail-value">
                    <span class="event-detail-priority ${event.priority.toLowerCase()}">${this.getPriorityText(event.priority)}</span>
                </div>
            </div>
            ${event.description ? `
            <div class="event-detail-item">
                <div class="event-detail-label">描述</div>
                <div class="event-detail-value">${event.description}</div>
            </div>
            ` : ''}
        `;

        // 添加提醒信息（包括签到信息）
        if (event.reminders && event.reminders.length > 0) {
            contentHtml += `
                <div class="event-detail-item">
                    <div class="event-detail-label">提醒</div>
                    <div class="event-detail-value">
                        ${this.renderRemindersInfo(event.reminders)}
                    </div>
                </div>
            `;
        }

        // 添加签到按钮和状态
        const checkInReminders = event.reminders ? event.reminders.filter(r => r.requiresCheckIn) : [];
        if (checkInReminders.length > 0) {
            // 等待异步渲染签到区域完成
            const checkInSectionHtml = await this.renderCheckInSection(checkInReminders, eventId);
            contentHtml += checkInSectionHtml;
        }

        content.innerHTML = contentHtml;
        document.getElementById('eventDetailModal').dataset.eventId = eventId;
        
        // 检查权限并控制编辑按钮显示
        await this.updateEventDetailPermissions(event);
        
        modal.show();
    }

    // 渲染提醒信息
    renderRemindersInfo(reminders) {
        return reminders.map(reminder => {
            const reminderType = reminder.reminderType === 'NOTIFICATION' ? '通知' :
                reminder.reminderType === 'EMAIL' ? '邮件' : '短信';
            const timeText = reminder.time ? `提前${reminder.time}分钟` : '自定义时间';
            const checkInText = reminder.requiresCheckIn ?
                ` <span class="badge bg-info">需签到</span>` : '';
            const messageText = reminder.message ? ` - ${reminder.message}` : '';

            return `<div class="reminder-item">
                        ${timeText} (${reminderType})${checkInText}${messageText}
                    </div>`;
        }).join('');
    }    // 渲染签到区域
    async renderCheckInSection(checkInReminders, eventId) {
        const now = new Date();
        let checkInButtonsHtml = '';

        for (const reminder of checkInReminders) {
            const reminderTime = this.parseStandardDateTime(reminder.reminderTime);
            const canCheckIn = this.canCheckInForReminder(reminder, now);
            const checkInStatus = await this.getCheckInStatus(reminder.id);

            if (canCheckIn && !checkInStatus.isCheckedIn) {
                checkInButtonsHtml += `
                    <button type="button" 
                            class="btn btn-success btn-sm me-2 mb-2 check-in-btn" 
                            onclick="checkInManager.handleCheckIn(${reminder.id})"
                            data-reminder-id="${reminder.id}">
                        <i class="bi bi-check-circle"></i> 立即签到
                    </button>
                `;
            } else if (checkInStatus.isCheckedIn) {
                checkInButtonsHtml += `
                    <span class="badge bg-success me-2 mb-2">
                        <i class="bi bi-check-circle-fill"></i> 已签到 (${checkInStatus.checkInTime})
                    </span>
                `;
            }
        }

        if (checkInButtonsHtml || this.hasCheckInHistory(eventId)) {
            return `
                <div class="event-detail-item">
                    <div class="event-detail-label">签到</div>
                    <div class="event-detail-value">
                        <div class="check-in-section">
                            ${checkInButtonsHtml}
                            ${this.renderCheckInHistory(eventId)}
                        </div>
                    </div>
                </div>
            `;
        }
        return '';
    }

    // 检查是否可以签到
    canCheckInForReminder(reminder, now) {
        if (!reminder.requiresCheckIn) return false;

        const reminderTime = this.parseStandardDateTime(reminder.reminderTime);
        const windowMinutes = reminder.checkInWindowMinutes || 30; // 默认30分钟窗口

        // 计算签到窗口的开始和结束时间
        const windowStart = new Date(reminderTime.getTime() - (windowMinutes / 2 * 60 * 1000));
        const windowEnd = new Date(reminderTime.getTime() + (windowMinutes / 2 * 60 * 1000));

        return now >= windowStart && now <= windowEnd;
    }    // 获取签到状态
    async getCheckInStatus(reminderId) {
        try {
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");
            
            const response = await fetch(`/api/check-ins/reminder/${reminderId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                const record = result.success ? result.data : null;
                return {
                    isCheckedIn: !!record,
                    checkInTime: record ? record.checkInTime : null,
                    checkInRecord: record
                };
            }
            return { isCheckedIn: false, checkInTime: null };
        } catch (error) {
            console.error('获取签到状态失败:', error);
            return { isCheckedIn: false, checkInTime: null };
        }
    }

    // 更新签到按钮状态
    updateCheckInButton(reminderId, checkedIn) {
        const button = document.querySelector(`[data-reminder-id="${reminderId}"]`);
        if (button) {
            if (checkedIn) {
                button.textContent = '已签到';
                button.classList.remove('btn-primary', 'btn-check-in');
                button.classList.add('btn-success');
                button.disabled = true;
            } else {
                button.textContent = '签到';
                button.classList.remove('btn-success');
                button.classList.add('btn-primary', 'btn-check-in');
                button.disabled = false;
            }
        }
    }    // 刷新签到状态
    async refreshCheckInStatus(reminderId) {
        const record = await this.getCheckInRecord(reminderId);
        this.updateCheckInButton(reminderId, !!record);
    }

    // 获取签到记录
    async getCheckInRecord(reminderId) {
        try {
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");
            
            const response = await fetch(`/api/check-ins/reminder/${reminderId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                return result.success ? result.data : null;
            }
            return null;
        } catch (error) {
            console.error('获取签到记录失败:', error);
            return null;
        }
    }

    // 渲染签到历史
    renderCheckInHistory(eventId) {
        // 这里可以根据需要实现签到历史显示
        // 暂时返回空字符串，避免错误
        return '';
    }

    // 检查是否有签到历史
    hasCheckInHistory(eventId) {
        // 暂时返回false，避免错误
        return false;
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建或更新消息显示区域
        let messageContainer = document.getElementById('checkInMessages');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'checkInMessages';
            messageContainer.style.position = 'fixed';
            messageContainer.style.top = '20px';
            messageContainer.style.right = '20px';
            messageContainer.style.zIndex = '9999';
            document.body.appendChild(messageContainer);
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        messageContainer.appendChild(alert);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }    // 显示签到统计
    async showCheckInStatistics(startDate, endDate) {
        try {
            const response = await fetch(`/api/check-ins/statistics?startDate=${startDate}&endDate=${endDate}`);
            const data = await response.json();

            if (data.success) {
                this.displayStatistics(data.data);
            }
        } catch (error) {
            console.error('获取签到统计失败:', error);
        }
    }

    // 显示统计信息
    displayStatistics(stats) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">签到统计</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">${stats.totalReminders}</h5>
                                        <p class="card-text">总提醒数</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">${stats.checkedInCount}</h5>
                                        <p class="card-text">已签到</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">${stats.missedCount}</h5>
                                        <p class="card-text">未签到</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">${stats.checkInRate.toFixed(1)}%</h5>
                                        <p class="card-text">签到率</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }    // 显示签到历史
    async showCheckInHistory(startDate, endDate) {
        try {
            const response = await fetch(`/api/check-ins/history?startDate=${startDate}&endDate=${endDate}`);
            const data = await response.json();

            if (data.success) {
                this.displayHistory(data.data);
            }
        } catch (error) {
            console.error('获取签到历史失败:', error);
        }
    }

    // 显示历史记录
    displayHistory(history) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">签到历史</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>事件标题</th>
                                        <th>提醒时间</th>
                                        <th>签到时间</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${history.map(record => `
                                        <tr>
                                            <td>${record.eventTitle || '未知事件'}</td>
                                            <td>${record.reminderTime || '-'}</td>
                                            <td>${record.checkInTime || '-'}</td>
                                            <td>
                                                <span class="badge bg-success">已签到</span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    // ========== 为签到页面添加的新方法 ==========
    // 获取待签到事件
    async getPendingCheckIns() {
        try {
            const response = await fetch(`/api/check-ins/pending`);
            const data = await response.json();
            return data.success ? data.data : [];
        } catch (error) {
            console.error('获取待签到事件失败:', error);
            return [];
        }
    }    // 获取签到统计信息
    async getCheckInStatistics() {
        try {
            const response = await fetch(`/api/check-ins/statistics`);
            const data = await response.json();
            return data.success ? data.data : {
                todayCount: 0,
                weekCount: 0,
                monthCount: 0,
                totalCount: 0
            };
        } catch (error) {
            console.error('获取签到统计失败:', error);
            return {
                todayCount: 0,
                weekCount: 0,
                monthCount: 0,
                totalCount: 0
            };
        }
    }    // 获取签到历史
    async getCheckInHistory(filter = 'all', page = 0, size = 10) {
        try {
            const params = new URLSearchParams({
                filter: filter,
                page: page,
                size: size
            });

            console.log('正在调用签到历史API:', `/api/check-ins/history?${params}`);
            const response = await fetch(`/api/check-ins/history?${params}`);

            console.log('API响应状态:', response.status);

            if (!response.ok) {
                console.error('API调用失败:', response.status, response.statusText);
                if (response.status === 403) {
                    throw new Error('用户未登录或权限不足');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('API响应数据:', data);

            if (data.success) {
                return data.data;
            } else {
                console.error('API返回错误:', data.message);
                throw new Error(data.message || 'API调用失败');
            }
        } catch (error) {
            console.error('获取签到历史失败:', error);
            throw error; // 重新抛出错误，让调用方处理
        }
    }    // 执行签到（为签到页面特别设计，基于提醒ID）
    async performCheckIn(reminderId, options = {}) {

        try {
            const requestBody = {
                notes: options.notes || '',
                latitude: options.latitude,
                longitude: options.longitude
            }; const response = await fetch(`/api/check-ins/reminder/${reminderId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();
            return {
                success: data.success,
                message: data.message || (data.success ? '签到成功' : '签到失败')
            };
        } catch (error) {
            console.error('签到失败:', error);
            return {
                success: false,
                message: '签到失败: ' + error.message
            };
        }
    }    // 检查是否可以签到（时间窗口检查）
    canCheckIn(reminderTime, checkInWindowMinutes = 30) {
        try {
            const now = new Date();
            let reminder;

            // 增强的日期解析
            if (Array.isArray(reminderTime)) {
                // 处理数组格式 [year, month, day, hour, minute, second]
                reminder = new Date(
                    reminderTime[0], // year
                    reminderTime[1] - 1, // month (JavaScript月份从0开始)
                    reminderTime[2], // day
                    reminderTime[3] || 0, // hour
                    reminderTime[4] || 0, // minute
                    reminderTime[5] || 0  // second
                );
            } else if (typeof reminderTime === 'string') {
                if (reminderTime.includes('T')) {
                    reminder = new Date(reminderTime);
                } else {
                    // 处理 "YYYY-MM-DD HH:MM:SS" 格式
                    reminder = new Date(reminderTime.replace(' ', 'T'));
                }
            } else if (typeof reminderTime === 'object' && reminderTime !== null) {
                // 处理对象格式 (可能是 LocalDateTime 对象)
                if (reminderTime.year && reminderTime.monthValue && reminderTime.dayOfMonth) {
                    reminder = new Date(
                        reminderTime.year,
                        reminderTime.monthValue - 1,
                        reminderTime.dayOfMonth,
                        reminderTime.hour || 0,
                        reminderTime.minute || 0,
                        reminderTime.second || 0
                    );
                } else {
                    reminder = new Date(reminderTime);
                }
            } else {
                reminder = new Date(reminderTime);
            }

            // 检查日期是否有效
            if (isNaN(reminder.getTime())) {
                console.error('无法解析提醒时间:', reminderTime);
                return false;
            }

            // 使用传入的签到窗口时间，默认30分钟
            const windowMinutes = checkInWindowMinutes || 30;

            const windowStart = new Date(reminder.getTime() - (windowMinutes / 2 * 60 * 1000));
            const windowEnd = new Date(reminder.getTime() + (windowMinutes / 2 * 60 * 1000));

            return now >= windowStart && now <= windowEnd;
        } catch (error) {
            console.error('检查签到时间失败:', error);
            return false;
        }
    }// 获取时间窗口信息
    getTimeWindow(reminderTime, checkInWindowMinutes = 30) {
        try {
            const now = new Date();
            let reminder;

            // 增强的日期解析
            if (Array.isArray(reminderTime)) {
                // 处理数组格式 [year, month, day, hour, minute, second]
                reminder = new Date(
                    reminderTime[0], // year
                    reminderTime[1] - 1, // month (JavaScript月份从0开始)
                    reminderTime[2], // day
                    reminderTime[3] || 0, // hour
                    reminderTime[4] || 0, // minute
                    reminderTime[5] || 0  // second
                );
            } else if (typeof reminderTime === 'string') {
                if (reminderTime.includes('T')) {
                    reminder = new Date(reminderTime);
                } else {
                    // 处理 "YYYY-MM-DD HH:MM:SS" 格式
                    reminder = new Date(reminderTime.replace(' ', 'T'));
                }
            } else if (typeof reminderTime === 'object' && reminderTime !== null) {
                // 处理对象格式 (可能是 LocalDateTime 对象)
                if (reminderTime.year && reminderTime.monthValue && reminderTime.dayOfMonth) {
                    reminder = new Date(
                        reminderTime.year,
                        reminderTime.monthValue - 1,
                        reminderTime.dayOfMonth,
                        reminderTime.hour || 0,
                        reminderTime.minute || 0,
                        reminderTime.second || 0
                    );
                } else {
                    reminder = new Date(reminderTime);
                }
            } else {
                reminder = new Date(reminderTime);
            }

            // 检查日期是否有效
            if (isNaN(reminder.getTime())) {
                console.error('无法解析提醒时间:', reminderTime);
                return { status: 'error', message: '时间解析错误' };
            }

            // 使用传入的签到窗口时间，默认30分钟
            const windowMinutes = checkInWindowMinutes || 30;

            const windowStart = new Date(reminder.getTime() - (windowMinutes / 2 * 60 * 1000));
            const windowEnd = new Date(reminder.getTime() + (windowMinutes / 2 * 60 * 1000));

            if (now < windowStart) {
                return { status: 'upcoming', message: '未到签到时间' };
            } else if (now > windowEnd) {
                return { status: 'expired', message: '签到时间已过' };
            } else {
                return { status: 'active', message: '可以签到' };
            }
        } catch (error) {
            console.error('获取时间窗口失败:', error);
            return { status: 'error', message: '时间解析错误' };
        }
    }

    // 判断事件是否为自建日历
    checkIfOwnCalendar(event) {
        // 确保数据类型一致性
        const eventCalendarId = typeof event.calendarId === 'string' ? parseInt(event.calendarId) : event.calendarId;
        
        // 检查事件的日历ID是否在用户的日历列表中
        const calendar = this.calendars.find(cal => {
            const calId = typeof cal.id === 'string' ? parseInt(cal.id) : cal.id;
            return calId === eventCalendarId;
        });
        
        if (calendar) {
            // 如果找到日历，说明是用户自己的日历
            console.log(`事件 "${event.title}" 属于自建日历: ${calendar.name} (ID: ${eventCalendarId})`);
            return true;
        } else {
            // 如果找不到日历，说明是共享日历
            console.log(`事件 "${event.title}" 属于共享日历 (calendarId: ${eventCalendarId})`);
            return false;
        }
    }

    // 工具方法
    isToday(date) {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    }
    // 解析标准格式的日期时间 "YYYY-MM-DD HH:MM:SS"
    parseStandardDateTime(dateTimeStr) {
        if (!dateTimeStr) {
            console.warn('传入空的日期字符串到parseStandardDateTime');
            return null;
        }

        //console.log('解析日期字符串:', dateTimeStr);

        try {
            // 处理标准格式 "YYYY-MM-DD HH:MM:SS"
            if (dateTimeStr.includes(' ')) {
                const [datePart, timePart] = dateTimeStr.split(' ');
                const [year, month, day] = datePart.split('-').map(part => parseInt(part, 10));
                let hours = 0, minutes = 0, seconds = 0;

                if (timePart) {
                    const timeParts = timePart.split(':').map(part => parseInt(part, 10));
                    hours = timeParts[0] || 0;
                    minutes = timeParts[1] || 0;
                    seconds = timeParts[2] || 0;
                }

                // JavaScript月份从0开始
                const date = new Date(year, month - 1, day, hours, minutes, seconds);

                // 验证日期是否有效
                if (isNaN(date.getTime())) {
                    console.warn(`无效日期: ${dateTimeStr}`);
                    return null;
                }

                //console.log(`解析标准格式日期成功: ${date.toISOString()}`);
                return date;
            }

            // 处理ISO格式 "YYYY-MM-DDThh:mm:ss" 或 "YYYY-MM-DDThh:mm:ss.sssZ"
            if (dateTimeStr.includes('T')) {
                const date = new Date(dateTimeStr);

                if (isNaN(date.getTime())) {
                    console.warn(`无法解析ISO日期: ${dateTimeStr}`);
                    return null;
                }

                console.log(`解析ISO格式日期成功: ${date.toISOString()}`);
                return date;
            }

            // 处理纯日期格式 "YYYY-MM-DD"
            if (dateTimeStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
                const [year, month, day] = dateTimeStr.split('-').map(part => parseInt(part, 10));
                const date = new Date(year, month - 1, day, 0, 0, 0);

                if (isNaN(date.getTime())) {
                    console.warn(`无法解析日期: ${dateTimeStr}`);
                    return null;
                }

                console.log(`解析纯日期格式成功: ${date.toISOString()}`);
                return date;
            }

            // 尝试直接解析
            const date = new Date(dateTimeStr);
            if (isNaN(date.getTime())) {
                console.warn(`无法解析日期字符串: ${dateTimeStr}`);
                return null;
            }

            console.log(`直接解析日期成功: ${date.toISOString()}`);
            return date;
        } catch (error) {
            console.error(`解析日期出错: ${dateTimeStr}`, error);
            return null;
        }
    } formatDate(date) {
        // 标准日期格式 YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    formatDateForAPI(date) {
        // 标准日期格式 YYYY-MM-DD
        return this.formatDate(date);
    }

    formatDateTimeLocal(date) {
        // HTML输入框需要T分隔符
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 标准格式化为 YYYY-MM-DD HH:MM:SS 格式
    formatStandardDateTime(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    formatMonthTitle() {
        return `${this.currentDate.getFullYear()}年${this.currentDate.getMonth() + 1}月`;
    }

    formatWeekTitle() {
        // 实现周标题格式化
        return `${this.currentDate.getFullYear()}年第${Math.ceil(this.currentDate.getDate() / 7)}周`;
    }

    formatDayTitle() {
        return `${this.currentDate.getFullYear()}年${this.currentDate.getMonth() + 1}月${this.currentDate.getDate()}日`;
    } formatEventTime(event) {
        if (!event || !event.startTime) {
            console.warn('formatEventTime 收到无效事件:', event);
            return '';
        }

        const start = this.parseStandardDateTime(event.startTime);
        const end = event.endTime ? this.parseStandardDateTime(event.endTime) : null;

        if (!start) {
            console.warn('无法解析开始时间:', event.startTime);
            return event.startTime || '';
        }

        if (event.isAllDay) {
            return '全天';
        } else if (end) {
            // 使用更友好的格式显示
            const startStr = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')} ${String(start.getHours()).padStart(2, '0')}:${String(start.getMinutes()).padStart(2, '0')}`;
            const endStr = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')} ${String(end.getHours()).padStart(2, '0')}:${String(end.getMinutes()).padStart(2, '0')}`;

            // 如果是同一天，只显示一次日期
            if (start.toDateString() === end.toDateString()) {
                return `${startStr.split(' ')[0]} ${startStr.split(' ')[1]} - ${endStr.split(' ')[1]}`;
            }

            return `${startStr} - ${endStr}`;
        } else {
            return `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')} ${String(start.getHours()).padStart(2, '0')}:${String(start.getMinutes()).padStart(2, '0')}`;
        }
    } formatTime(date) {
        // 标准时间格式 HH:MM:SS
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }

    getViewStartDate() {
        const date = new Date(this.currentDate);
        if (this.viewMode === 'month') {
            date.setDate(1);
            date.setDate(date.getDate() - date.getDay());
        } else if (this.viewMode === 'week') {
            date.setDate(date.getDate() - date.getDay());
        }
        return date;
    } getViewEndDate() {
        const date = new Date(this.currentDate);
        if (this.viewMode === 'month') {
            // 获取当前月的最后一天
            date.setMonth(date.getMonth() + 1, 0);
            // 计算月视图中显示的最后一天（可能在下个月）
            const lastDayOfMonth = new Date(date);
            lastDayOfMonth.setDate(lastDayOfMonth.getDate() + (6 - lastDayOfMonth.getDay()));
            return lastDayOfMonth;
        } else if (this.viewMode === 'week') {
            date.setDate(date.getDate() + (6 - date.getDay()));
        }
        return date;
    }


    // 修复后的日历事件过滤函数
    getEventsForDate(date) {
        const dateStr = this.formatDate(date);
        //console.log(`查找日期 ${dateStr} 的事件`);

        // 将目标日期转换为标准格式的字符串，用于简单比较
        const targetDateStr = dateStr;

        const events = this.events.filter(event => {
            if (!event || !event.startTime) {
                console.log('跳过无效事件:', event);
                return false;
            }

            // 解析事件开始时间
            const eventStartDate = this.parseStandardDateTime(event.startTime);
            if (!eventStartDate) {
                console.warn('无法解析事件开始时间:', event.startTime);
                return false;
            }

            // 比较日期部分（忽略时间部分）
            const eventDateStr = this.formatDate(eventStartDate);
            const isOnTargetDate = eventDateStr === targetDateStr;

           
                // console.log(`事件: "${event.title}", 开始: ${event.startTime}, ` +
                //     `在目标日期 ${dateStr}: ${isOnTargetDate}`);
            
            return isOnTargetDate;
        });
        //console.log(`在日期 ${dateStr} 找到 ${events.length} 个事件:`, events);
        return events;
    }

    // 计算颜色亮度，用于确定文本颜色
    getColorBrightness(hexColor) {
        // 移除 # 符号
        const hex = hexColor.replace('#', '');

        // 转换为 RGB
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // 计算亮度 (使用相对亮度公式)
        return (r * 299 + g * 587 + b * 114) / 1000;
    }

    // 根据背景颜色获取适合的文本颜色
    getTextColor(backgroundColor) {
        const brightness = this.getColorBrightness(backgroundColor);
        return brightness > 128 ? '#000000' : '#ffffff';
    } renderEventItem(event) {
        // 查找事件所属的日历
        const calendar = this.calendars.find(cal => cal.id === event.calendarId);
        const calendarColor = calendar ? calendar.color : '#007bff'; // 默认蓝色
        const textColor = this.getTextColor(calendarColor);

        const classes = [
            'event-item',
            `priority-${event.priority.toLowerCase()}`,
            `type-${event.eventType.toLowerCase()}`,
            event.isAllDay ? 'all-day' : ''
        ].filter(Boolean).join(' ');

        // 使用日历的颜色作为事件的背景色和边框色，自动选择文本颜色
        // 添加 event.stopPropagation() 来阻止事件冒泡到父元素（日期单元格）
        return `<div class="${classes}" style="background-color: ${calendarColor}; border-color: ${calendarColor}; color: ${textColor};" onclick="event.stopPropagation(); calendarManager.showEventDetail(${event.id})" title="${event.title}">
                  ${event.title}
                </div>`;
    }

    getEventTypeText(type) {
        const types = {
            'MEETING': '会议',
            'TASK': '任务',
            'APPOINTMENT': '约会',
            'REMINDER': '提醒',
            'OTHER': '其他'
        };
        return types[type] || type;
    }

    getPriorityText(priority) {
        const priorities = {
            'LOW': '低',
            'NORMAL': '普通',
            'HIGH': '高',
            'URGENT': '紧急'
        };
        return priorities[priority] || priority;
    }

    showSuccess(message) {
        // 可以使用Toast或其他提示方式
        alert(message);
    }

    showError(message) {
        // 可以使用Toast或其他提示方式
        alert(message);
    }    // 加载人员数据
    loadPersonnel() {
        // 优先从全局变量获取人员数据
        if (window.personnelData && Array.isArray(window.personnelData)) {
            this.personnel = window.personnelData;
        } else {
            // 备用方案：从data属性获取
            const personnelElements = document.querySelectorAll('[data-personnel]');
            if (personnelElements.length > 0) {
                const personnelData = personnelElements[0].getAttribute('data-personnel');
                try {
                    this.personnel = JSON.parse(personnelData);
                } catch (e) {
                    console.error('解析人员数据失败:', e);
                    this.personnel = [];
                }
            } else {
                console.warn('未找到人员数据，尝试从API获取');
                // 尝试从API获取人员数据
                this.loadPersonnelFromAPI();
            }
        }
    }    // 从API加载人员数据
    async loadPersonnelFromAPI() {
        try {
            const response = await fetch('/api/users/all');
            if (response.ok) {
                const users = await response.json();

                // 如果返回的是用户对象数组，提取用户名
                if (Array.isArray(users)) {
                    this.personnel = users.map(user => {
                        if (typeof user === 'string') return user;
                        return user.username || user.name || user.email || `用户${user.id}`;
                    });
                } else {
                    console.warn('API返回的不是数组格式');
                    this.personnel = [];
                }
            } else {
                console.warn('API请求失败:', response.status);
                this.personnel = [];
            }
        } catch (error) {
            console.error('从API获取人员数据时出错:', error);
            this.personnel = [];
        }
    }

    // 更新事件详情权限控制
    async updateEventDetailPermissions(event) {
        const editBtn = document.getElementById('editEventBtn');
        
        try {
            // 获取当前用户信息
            const currentUser = await this.getCurrentUser();
            
            if (currentUser && event.creatorId) {
                // 检查是否为事件创建者
                const isCreator = currentUser.id === event.creatorId;
                
                if (isCreator) {
                    // 是创建者，显示编辑按钮
                    editBtn.style.display = 'inline-block';
                    editBtn.textContent = '编辑';
                    editBtn.className = 'btn btn-primary';
                } else {
                    // 不是创建者，隐藏编辑按钮或显示为只读
                    editBtn.style.display = 'none';
                }
            } else {
                // 无法确定权限，隐藏编辑按钮
                editBtn.style.display = 'none';
            }
        } catch (error) {
            console.error('权限检查失败:', error);
            // 发生错误时，为安全起见隐藏编辑按钮
            editBtn.style.display = 'none';
        }
    }

    // 更新事件编辑模态框权限控制
    async updateEventModalPermissions(eventId) {
        const deleteBtn = document.getElementById('deleteEvent');
        
        try {
            // 获取当前用户信息
            const currentUser = await this.getCurrentUser();
            const event = this.events.find(e => e.id == eventId);  // 使用松散相等避免类型问题
            
            if (currentUser && event && event.creatorId) {
                // 检查是否为事件创建者
                const isCreator = currentUser.id === event.creatorId;
                
                if (isCreator) {
                    // 是创建者，显示删除按钮
                    deleteBtn.style.display = 'inline-block';
                } else {
                    // 不是创建者，隐藏删除按钮
                    deleteBtn.style.display = 'none';
                }
            } else {
                // 无法确定权限，隐藏删除按钮
                deleteBtn.style.display = 'none';
            }
        } catch (error) {
            console.error('权限检查失败:', error);
            // 发生错误时，为安全起见隐藏删除按钮
            deleteBtn.style.display = 'none';
        }
    }

    // 获取当前用户信息
    async getCurrentUser() {
        if (this.currentUser) {
            return this.currentUser;
        }
        
        try {
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");

            const response = await fetch('/api/user/current', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });

            if (response.ok) {
                this.currentUser = await response.json();
                return this.currentUser;
            }
        } catch (error) {
            console.error('获取当前用户信息失败:', error);
        }
        
        return null;
    }
}

/**
 * 签到管理器
 */
class CheckInManager {
    constructor() {
        this.initEventListeners();
    }

    // 初始化事件监听器
    initEventListeners() {
        // 监听签到按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-check-in')) {
                const reminderId = e.target.getAttribute('data-reminder-id');
                this.handleCheckIn(reminderId);
            }
        });
    }    // 检查是否可以签到
    async canCheckIn(reminderId) {
        try {
            const response = await fetch(`/api/check-ins/reminder/${reminderId}/can-check-in`);
            const data = await response.json();
            return data.success && data.canCheckIn;
        } catch (error) {
            console.error('检查签到状态失败:', error);
            return false;
        }
    }

    // 执行签到
    async handleCheckIn(reminderId) {
        try {
            // 先检查是否可以签到
            const canCheckIn = await this.canCheckIn(reminderId);
            if (!canCheckIn) {
                this.showMessage('当前不在签到时间窗口内或已经签到过', 'warning');
                return;
            } const response = await fetch(`/api/check-ins/reminder/${reminderId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('签到成功！', 'success');
                this.updateCheckInButton(reminderId, true);
                // 刷新签到状态
                this.refreshCheckInStatus(reminderId);
            } else {
                this.showMessage(data.message || '签到失败', 'error');
            }
        } catch (error) {
            console.error('签到失败:', error);
            this.showMessage('签到失败，请重试', 'error');
        }
    }    // 获取签到记录
    async getCheckInRecord(reminderId) {
        try {
            const token = document.querySelector("meta[name='_csrf']").getAttribute("content");
            const header = document.querySelector("meta[name='_csrf_header']").getAttribute("content");
            
            const response = await fetch(`/api/check-ins/reminder/${reminderId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    [header]: token
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                return result.success ? result.data : null;
            }
            return null;
        } catch (error) {
            console.error('获取签到记录失败:', error);
            return null;
        }
    }

    // 渲染签到历史
    renderCheckInHistory(eventId) {
        // 这里可以根据需要实现签到历史显示
        // 暂时返回空字符串，避免错误
        return '';
    }

    // 检查是否有签到历史
    hasCheckInHistory(eventId) {
        // 暂时返回false，避免错误
        return false;
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建或更新消息显示区域
        let messageContainer = document.getElementById('checkInMessages');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'checkInMessages';
            messageContainer.style.position = 'fixed';
            messageContainer.style.top = '20px';
            messageContainer.style.right = '20px';
            messageContainer.style.zIndex = '9999';
            document.body.appendChild(messageContainer);
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        messageContainer.appendChild(alert);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
}

// 初始化日历管理器
let calendarManager;
document.addEventListener('DOMContentLoaded', function () {
    // 避免重复初始化
    if (!calendarManager) {
        calendarManager = new CalendarManager();
        window.calendarManager = calendarManager; // 设置为全局变量供其他脚本使用
    }
});

// 初始化签到管理器
let checkInManager;
document.addEventListener('DOMContentLoaded', function () {
    // 避免重复初始化
    if (!checkInManager) {
        checkInManager = new CheckInManager();
        window.checkInManager = checkInManager; // 设置为全局变量供其他脚本使用
    }
});
