package com.mylog.service;

import com.mylog.model.workflow.WorkflowStep;

import java.util.List;
import java.util.Optional;

/**
 * 流程步骤服务接口
 */
public interface WorkflowStepService {

    /**
     * 查找所有流程步骤
     */
    List<WorkflowStep> findAllSteps();

    /**
     * 根据ID查找流程步骤
     */
    Optional<WorkflowStep> findStepById(Long id);

    /**
     * 根据模板ID查找流程步骤列表，按步骤顺序排序
     */
    List<WorkflowStep> findStepsByTemplateId(Long templateId);

    /**
     * 使用EntityManager直接查询流程步骤列表
     */
    List<WorkflowStep> findStepsByTemplateIdDirect(Long templateId);

    /**
     * 保存流程步骤
     */
    WorkflowStep saveStep(WorkflowStep step);

    /**
     * 批量保存流程步骤
     */
    List<WorkflowStep> saveAllSteps(List<WorkflowStep> steps);

    /**
     * 删除流程步骤
     */
    void deleteStep(Long id);

    /**
     * 查找模板的下一个步骤
     */
    Optional<WorkflowStep> findNextStep(Long templateId, Integer currentOrder);

    /**
     * 查找模板的上一个步骤
     */
    Optional<WorkflowStep> findPreviousStep(Long templateId, Integer currentOrder);

    /**
     * 查找模板的第一个步骤
     */
    Optional<WorkflowStep> findFirstStep(Long templateId);

    /**
     * 获取模板中下一个可用的步骤顺序
     */
    Integer getNextStepOrder(Long templateId);

    /**
     * 调整步骤顺序
     */
    void reorderSteps(Long templateId, Long stepId, Integer newOrder);
}
