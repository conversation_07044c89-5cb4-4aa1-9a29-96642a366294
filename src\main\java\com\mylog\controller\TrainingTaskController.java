package com.mylog.controller;

import com.mylog.model.ProjectTask;
import com.mylog.service.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/tasks/training")
public class TrainingTaskController {
    
    private static final Logger logger = LoggerFactory.getLogger(TrainingTaskController.class);
    
    @Autowired
    private TaskService taskService;
    
    @GetMapping
    public String listTrainingTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {
        
        logger.info("访问教育培训任务列表页面");
        
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        
        // 获取分页的教育培训任务
        Page<ProjectTask> trainingTasks = taskService.findMyTrainingTasks(
            currentUsername, 
            PageRequest.of(page, size)
        );
        
        // 计算任务的评论天数并保存
        taskService.calculateCommentDays(trainingTasks.getContent());
        logger.info("已计算教育培训任务中 {} 个任务的评论天数", trainingTasks.getContent().size());
        
        // 获取进行中的教育培训任务数量
        Long inProgressCount = taskService.countInProgressTrainingTasks(currentUsername);
        
        // 添加到模型
        model.addAttribute("activeMenu", "trainingTasks");
        model.addAttribute("tasks", trainingTasks);
        model.addAttribute("inProgressCount", inProgressCount);
        model.addAttribute("totalCount", trainingTasks.getTotalElements());
        
        return "tasks/training";
    }
} 