-- 验证累计工期和剩余工期字段实现
-- 测试脚本

-- 1. 检查新字段是否已添加
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_name = 'Tasks' 
  AND column_name IN ('cumulative_duration_days', 'remaining_duration_days')
ORDER BY column_name;

-- 2. 检查现有任务的工期字段状态
SELECT 
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN cumulative_duration_days IS NOT NULL THEN 1 END) as tasks_with_cumulative,
    COUNT(CASE WHEN remaining_duration_days IS NOT NULL THEN 1 END) as tasks_with_remaining,
    COUNT(CASE WHEN rated_duration_days IS NOT NULL THEN 1 END) as tasks_with_rated,
    AVG(COALESCE(cumulative_duration_days, 0)) as avg_cumulative,
    AVG(COALESCE(remaining_duration_days, 0)) as avg_remaining,
    AVG(COALESCE(rated_duration_days, 0)) as avg_rated
FROM Tasks;

-- 3. 检查工期计算是否合理（剩余工期 = 额定工期 - 累计工期）
SELECT 
    task_id,
    task_name,
    rated_duration_days,
    cumulative_duration_days,
    remaining_duration_days,
    (COALESCE(rated_duration_days, 0) - COALESCE(cumulative_duration_days, 0)) as calculated_remaining,
    CASE 
        WHEN ABS(COALESCE(remaining_duration_days, 0) - (COALESCE(rated_duration_days, 0) - COALESCE(cumulative_duration_days, 0))) > 0.01 
        THEN 'MISMATCH' 
        ELSE 'OK' 
    END as calculation_status
FROM Tasks
WHERE rated_duration_days IS NOT NULL
ORDER BY calculation_status DESC, task_id
LIMIT 10;

-- 4. 查看有累计工期的任务示例
SELECT 
    task_id,
    task_name,
    responsible,
    status,
    rated_duration_days,
    cumulative_duration_days,
    remaining_duration_days,
    created_date
FROM Tasks
WHERE cumulative_duration_days > 0
ORDER BY cumulative_duration_days DESC
LIMIT 10;

-- 5. 查看剩余工期为负数的任务（超期任务）
SELECT 
    task_id,
    task_name,
    responsible,
    status,
    rated_duration_days,
    cumulative_duration_days,
    remaining_duration_days
FROM Tasks
WHERE remaining_duration_days < 0
ORDER BY remaining_duration_days ASC
LIMIT 10;

-- 6. 验证与工时记录的一致性（随机抽样）
SELECT 
    t.task_id,
    t.task_name,
    t.cumulative_duration_days as task_cumulative,
    w.hours_inventory as worklog_inventory,
    ABS(COALESCE(t.cumulative_duration_days, 0) - COALESCE(w.hours_inventory, 0)) as difference
FROM Tasks t
LEFT JOIN (
    SELECT 
        business_id,
        hours_inventory,
        ROW_NUMBER() OVER (PARTITION BY business_id ORDER BY created_time DESC) as rn
    FROM WorkHoursLog 
    WHERE business_type = '任务'
) w ON CAST(t.task_id AS TEXT) = CAST(w.business_id AS TEXT) AND w.rn = 1
WHERE t.cumulative_duration_days > 0
  AND w.hours_inventory IS NOT NULL
ORDER BY difference DESC
LIMIT 10;

-- 7. 性能测试 - 检查字段索引（如果需要）
EXPLAIN QUERY PLAN 
SELECT task_id, cumulative_duration_days, remaining_duration_days 
FROM Tasks 
WHERE remaining_duration_days < 1.0 
  AND status = '进行中';

-- 8. 数据完整性检查
SELECT 
    'Missing cumulative_duration_days' as issue_type,
    COUNT(*) as count
FROM Tasks 
WHERE cumulative_duration_days IS NULL

UNION ALL

SELECT 
    'Missing remaining_duration_days' as issue_type,
    COUNT(*) as count
FROM Tasks 
WHERE remaining_duration_days IS NULL

UNION ALL

SELECT 
    'Negative remaining_duration' as issue_type,
    COUNT(*) as count
FROM Tasks 
WHERE remaining_duration_days < 0

UNION ALL

SELECT 
    'Large cumulative_duration' as issue_type,
    COUNT(*) as count
FROM Tasks 
WHERE cumulative_duration_days > 1000;

-- 测试完成提示
SELECT 'Duration fields validation completed' as status, NOW() as timestamp;
