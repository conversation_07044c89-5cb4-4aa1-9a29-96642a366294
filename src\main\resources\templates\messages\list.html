﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('我的消息')}">
    <meta charset="UTF-8">
    <title>我的消息</title>
    <style>
        .message-item {
            border-left: 3px solid #dee2e6;
            transition: all 0.2s;
        }
        .message-item:hover {
            border-left-color: #0d6efd;
            background-color: #f1f8ff;
        }
        .message-item.unread {
            border-left-color: #0d6efd;
            background-color: #f0f7ff;
        }
        .message-item.unread .message-title {
            font-weight: bold;
        }
    </style>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">我的消息 (<span class="text-danger" th:text="${unreadMessageCount}">0</span>/<span th:text="${messagePage.totalElements}">0</span>)</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <form th:action="@{/messages/mark-all-read}" method="post" class="me-2">
                            <button type="submit" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-check-all me-1"></i>全部标为已读
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${message}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 消息列表 -->
                <div class="card">
                    <div class="card-body p-0">
                        <div th:if="${messagePage.empty}" class="p-4 text-center text-muted">
                            <i class="bi bi-envelope-open fs-1"></i>
                            <p class="mt-3">暂无消息</p>
                        </div>

                        <div th:unless="${messagePage.empty}" class="list-group list-group-flush">
                            <div th:each="msg : ${messagePage.content}"
                                 th:class="${msg.read ? 'list-group-item message-item' : 'list-group-item message-item unread'}">
                                <div class="d-flex w-100 justify-content-between">
                                    <a th:href="@{/messages/{id}(id=${msg.messageId})}" class="text-decoration-none text-dark">
                                        <h5 class="mb-1 message-title" th:text="${msg.messageTitle}">消息标题</h5>
                                    </a>
                                    <small th:text="${msg.createdDateStr}">2023-01-01 12:00:00</small>
                                </div>
                                <p class="mb-1 text-truncate" th:text="${msg.messageContent}">消息内容...</p>
                                <div class="d-flex justify-content-between align-items-center mt-2">
                                    <small class="text-muted">
                                        <span th:if="${msg.relatedType == 'Project'}">相关项目</span>
                                        <span th:if="${msg.relatedType == 'Task'}">相关任务</span>
                                        <span th:if="${msg.relatedType == 'SubTask'}">相关任务评论</span>
                                    </small>
                                    <div>
                                        <form th:if="${!msg.read}" th:action="@{/messages/mark-read}" method="post" class="d-inline">
                                            <input type="hidden" name="messageId" th:value="${msg.messageId}">
                                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-check"></i> 标为已读
                                            </button>
                                        </form>
                                        <form th:action="@{/messages/delete}" method="post" class="d-inline">
                                            <input type="hidden" name="messageId" th:value="${msg.messageId}">
                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                    onclick="return confirm('确定要删除这条消息吗？')">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="d-flex justify-content-center mt-4" th:if="${messagePage.totalPages > 0}">
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item" th:classappend="${messagePage.first ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/messages(page=0, size=${messagePage.size})}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:classappend="${messagePage.first ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${messagePage.number - 1}, size=${messagePage.size})}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, messagePage.totalPages - 1)}"
                                th:classappend="${i == messagePage.number ? 'active' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${i}, size=${messagePage.size})}" th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item" th:classappend="${messagePage.last ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${messagePage.number + 1}, size=${messagePage.size})}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:classappend="${messagePage.last ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/messages(page=${messagePage.totalPages - 1}, size=${messagePage.size})}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
    </div>
</body>
</html>