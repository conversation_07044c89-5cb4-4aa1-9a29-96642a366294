package com.mylog.service;

import com.mylog.model.SearchPlan;
import java.util.List;
import java.util.Optional;

public interface SearchPlanService {
    
    /**
     * 保存搜索方案
     * @param searchPlan 搜索方案对象
     * @return 保存后的搜索方案
     */
    SearchPlan saveSearchPlan(SearchPlan searchPlan);
    
    /**
     * 获取用户的所有搜索方案
     * @param username 用户名
     * @return 用户的搜索方案列表
     */
    List<SearchPlan> getSearchPlansByUsername(String username);
    
    /**
     * 根据ID获取搜索方案
     * @param planId 方案ID
     * @return 可选的搜索方案
     */
    Optional<SearchPlan> getSearchPlanById(Long planId);
    
    /**
     * 删除搜索方案
     * @param planId 方案ID
     */
    void deleteSearchPlan(Long planId);
    
    /**
     * 检查方案名称是否已存在
     * @param username 用户名
     * @param planName 方案名称
     * @return 是否存在
     */
    boolean isPlanNameExists(String username, String planName);
} 