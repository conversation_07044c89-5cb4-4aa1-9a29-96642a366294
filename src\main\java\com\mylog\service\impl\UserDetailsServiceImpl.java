package com.mylog.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.mylog.model.user.User;
import com.mylog.repository.user.UserRepository;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        Optional<User> userOpt = userRepository.findByUsername(username);
        
        if (!userOpt.isPresent()) {
            throw new UsernameNotFoundException("閻劍鍩涢崥宥勭瑝鐎涙ê婀? " + username);
        }
        
        User user = userOpt.get();
        
        return new org.springframework.security.core.userdetails.User(
            user.getUsername(),
            user.getPassword(),
            getAuthorities(user)
        );
    }
    
    private Collection<? extends GrantedAuthority> getAuthorities(User user) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 濞ｈ濮為崺鐑樻拱閻劍鍩涚憴鎺曞
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        
        // 閺嶈宓侀悽銊﹀煕鐟欐帟澹婂ǎ璇插妫版繂顦婚弶鍐
        switch (user.getRole()) {
            case ADMIN:
                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
                authorities.add(new SimpleGrantedAuthority("ROLE_MANAGER"));
                break;
            case MANAGER:
                authorities.add(new SimpleGrantedAuthority("ROLE_MANAGER"));
                break;
            default:
                // OPERATOR鐟欐帟澹婃稉宥夋付鐟曚線顤傛径鏍ㄦ綀闂?
                break;
        }
        
        return authorities;
    }
} 
