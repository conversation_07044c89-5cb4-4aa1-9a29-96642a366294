<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('任务统计报表')}">
    <meta charset="UTF-8">
    <title>任务统计报表</title>
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">任务统计报表</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToPDF()">导出PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">导出Excel</button>
                        </div>
                        <a th:href="@{/reports}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">筛选条件</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/reports/task-statistics}" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">任务列表 (<span th:text="${totalTasks}">0</span>)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>任务名称</th>
                                        <th>负责人</th>
                                        <th>状态</th>
                                        <th>风险等级</th>
                                        <th>完成率</th>
                                        <th>创建日期</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="task : ${tasks}">
                                        <td th:text="${task.taskName}">任务名称</td>
                                        <td th:text="${task.responsible}">负责人</td>
                                        <td>
                                            <span th:class="${'badge ' +
                                                (task.status == '未开始' ? 'bg-secondary' :
                                                (task.status == '进行中' ? 'bg-primary' :
                                                (task.status == '已完成' ? 'bg-success' : 'bg-danger')))}"
                                                  th:text="${task.status}">状态</span>
                                        </td>
                                        <td>
                                            <span th:class="${'badge ' +
                                                (task.risk == '正常' ? 'bg-success' :
                                                (task.risk == '低' ? 'bg-info' :
                                                (task.risk == '中' ? 'bg-warning' : 'bg-danger')))}"
                                                  th:text="${task.risk}">风险等级</span>
                                        </td>
                                        <td th:text="${#numbers.formatPercent(task.ratio != null ? task.ratio : 0, 0, 0)}">完成率</td>
                                        <td th:text="${#temporals.format(task.createdDateTime, 'yyyy-MM-dd HH:mm')}">创建日期</td>
                                    </tr>
                                    <tr th:if="${tasks.empty}">
                                        <td colspan="6" class="text-center">暂无任务数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页控件 -->
                        <div class="d-flex justify-content-center mt-4" th:if="${totalPages > 0}">
                            <nav aria-label="任务列表分页">
                                <ul class="pagination">
                                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/reports/task-statistics(projectId=${projectId}, startDate=${startDate}, endDate=${endDate}, page=0, size=${pageSize})}" aria-label="首页">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/reports/task-statistics(projectId=${projectId}, startDate=${startDate}, endDate=${endDate}, page=${currentPage - 1}, size=${pageSize})}" aria-label="上一页">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                                        th:classappend="${i == currentPage ? 'active' : ''}"
                                        th:if="${i >= currentPage - 2 && i <= currentPage + 2}">
                                        <a class="page-link" th:href="@{/reports/task-statistics(projectId=${projectId}, startDate=${startDate}, endDate=${endDate}, page=${i}, size=${pageSize})}" th:text="${i + 1}">1</a>
                                    </li>
                                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/reports/task-statistics(projectId=${projectId}, startDate=${startDate}, endDate=${endDate}, page=${currentPage + 1}, size=${pageSize})}" aria-label="下一页">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                                        <a class="page-link" th:href="@{/reports/task-statistics(projectId=${projectId}, startDate=${startDate}, endDate=${endDate}, page=${totalPages - 1}, size=${pageSize})}" aria-label="末页">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- 图表展示区域 -->
                <div class="row">
                    <!-- 任务状态分布 -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">任务状态分布</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 风险等级分布 -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">风险等级分布</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="riskChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 负责人任务分配 -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">负责人任务分配</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="responsibleChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
    </div>
    <script th:inline="javascript">
        // 从后端获取数据
        const statusData = /*[[${statusData}]]*/ {};
        const riskData = /*[[${riskData}]]*/ {};
        const responsibleData = /*[[${responsibleData}]]*/ {};

        // 任务状态分布图表
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(statusData),
                datasets: [{
                    data: Object.values(statusData),
                    backgroundColor: [
                        '#6c757d', // 未开始 - 灰色
                        '#0d6efd', // 进行中 - 蓝色
                        '#198754', // 已完成 - 绿色
                        '#dc3545'  // 已暂停 - 红色
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: '任务状态分布'
                    }
                }
            }
        });

        // 风险等级分布图表
        const riskCtx = document.getElementById('riskChart').getContext('2d');
        new Chart(riskCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(riskData),
                datasets: [{
                    data: Object.values(riskData),
                    backgroundColor: [
                        '#198754', // 正常 - 绿色
                        '#0dcaf0', // 低 - 浅蓝
                        '#ffc107', // 中 - 黄色
                        '#dc3545'  // 高 - 红色
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: '风险等级分布'
                    }
                }
            }
        });

        // 负责人任务分配图表
        const responsibleCtx = document.getElementById('responsibleChart').getContext('2d');
        new Chart(responsibleCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(responsibleData),
                datasets: [{
                    label: '任务数量',
                    data: Object.values(responsibleData),
                    backgroundColor: '#0d6efd'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '负责人任务分配'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 导出PDF功能
        function exportToPDF() {
            alert('PDF导出功能正在开发中...');
        }

        // 导出Excel功能
        function exportToExcel() {
            alert('Excel导出功能正在开发中...');
        }
    </script>
</body>
</html>